const { devtools } = require("vue");

module.exports = {
  devtools: {
    enabled: true,
  },
  content: [
    './vueform.config.ts', // or where `vueform.config.js` is located. Change `.js` to `.ts` if required.
    './node_modules/@vueform/vueform/themes/tailwind/**/*.vue',
    './node_modules/@vueform/vueform/themes/tailwind/**/*.js',
  ],
  plugins: 
  [
    // require("daisyui"),
    "@tailwindcss/typography", 
    require('@vueform/vueform/tailwind')
  ],
  // daisyui: {
  //   // themes: ['light'],
  //   themes: [
  //     {
  //       mytheme: {
  //         primary: "#451da0",
  //         "primary-content": "#000616",
  //         secondary: "#2c95ff",
  //         "secondary-content": "#000900",
  //         accent: "#f09e00",
  //         "accent-content": "#140900",
  //         neutral: "#020e1e",
  //         "neutral-content": "#6f6d71",
  //         "base-100": "#fafaff",
  //         "base-200": "#ded9da",
  //         "base-300": "#bdb9ba",
  //         "base-content": "#161515",
  //         info: "#0074e3",
  //         "info-content": "#000512",
  //         success: "#00d269",
  //         "success-content": "#001004",
  //         warning: "#ffba00",
  //         "warning-content": "#160d00",
  //         error: "#ec3d61",
  //         "error-content": "#130103",


  //         "--rounded-box": "0.375rem",
  //         "--rounded-btn": "0.375rem",
  //       },
  //     },
  //   ],
  //   // darkTheme: "pastel"
  // },
  theme: {
    fontFamily: {
      body: ['"Lexend"'],
    },
  },

};
