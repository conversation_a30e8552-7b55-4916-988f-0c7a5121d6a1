import type { Company } from "~/types";
import { useAuthStore } from './auth.store';
import { httpRequestStore } from './httpRequest.store';

export const useCompanyStore = defineStore("companyStore", {
  state: () => ({
    // loading: false,
    getDetailsPending: false,
    getDetailsError: false,
    getDetailsSuccess: false,
    getDetailsMessage: null as string | null,
    company: null as Company | null,

    updatePending: false,
    updateError: false,
    updateSuccess: false,
    updateMessage: null as string | null,
  }),
  actions: {
    async getCompany(id: number) {
      return await httpRequestStore().request<Company>(
        "v1/companies/detail/" + id,
        "GET",
        null,
        null,
        () => {
          this.getDetailsPending = true;
          this.getDetailsError = false;
          this.getDetailsSuccess = false;
          this.getDetailsMessage = null;
        },
        (response: Company) => {
          this.company = response;
          this.getDetailsPending = false;
          this.getDetailsError = false;
          this.getDetailsSuccess = true;
          this.getDetailsMessage = "Lấy thông tin công ty thành công";
        },
        (error: any, message: string) => {
          this.getDetailsPending = false;
          this.getDetailsError = true;
          this.getDetailsSuccess = false;
          this.getDetailsMessage = message || "Lấy thông tin công ty thất bại";
        }
      );
    },
    async updateCompany(body: Partial<Company>) {
      try {
        this.updateMessage = null;
        this.updatePending = true;
        this.updateError = false;
        this.updateSuccess = false;

        const response = await $fetch<Company>("/v1/companies/me/update", {
          method: "POST",
          headers: {
            Authorization: `Bearer ${useAuthStore().accessToken}`,
          },
          body,
          baseURL: useRuntimeConfig().public.apiUrl,
        });
        this.company = response;
        this.updateSuccess = true;
        this.updateMessage = "Cập nhật thông tin công ty thành công";
      } catch (error) {
        this.updateError = true;
        this.updateMessage = "Cập nhật thông tin công ty thất bại";
        this.updatePending = false;
      } finally {
        this.updatePending = false;
      }
    },
  },
});
