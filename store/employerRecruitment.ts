import type { PagedResult, Recruitment } from '~/types';
import { httpRequestStore } from './httpRequest.store';

export const useEmployerRecruitmentStore = defineStore('useEmployerRecruitmentStore', {
  state: () => ({
    recruitment: null as Recruitment | null,
    updatePending: false,
    updateError: false,
    updateSuccess: false,
    updateMessage: null as string | null,

    createPending: false,
    createError: false,
    createSuccess: false,
    createMessage: null as string | null,

    getListPending: false,
    getListError: false,
    getListSuccess: false,
    getListMessage: null as string | null,
    total: 0,
    items: [] as Recruitment[],

    getDetailsPending: false,
    getDetailsError: false,
    getDetailsSuccess: false,
    getDetailsMessage: null as string | null,

    deletePending: false,
    deleteError: false,
    deleteSuccess: false,
    deleteMessage: null as string | null,
  }),

  actions: {
    async getList(body: { page: number; limit: number }) {
      httpRequestStore().authRequest<PagedResult<Recruitment>>(
        'v1/employer/recruitment/list/get',
        'POST',
        body,
        null,
        () => {
          this.getListPending = true;
          this.getListError = false;
          this.getListSuccess = false;
          this.getListMessage = '';
        },
        (response: PagedResult<Recruitment>) => {
          this.items = response.items;
          this.total = response.total;
          this.getListSuccess = true;
          this.getListMessage = 'Lấy thông tin công ty thành công';
          this.getListPending = false;
          this.getListError = false;
        },
        (error: any, message: string) => {
          this.getListError = true;
          this.getListSuccess = false;
          this.getListPending = false;
          this.getListMessage = message || 'Lấy thông tin công ty thất bại';
        },
      );
    },

    async create(body: Record<string, any>) {
      await httpRequestStore().authRequest<Recruitment>(
        'v1/employer/recruitment/create',
        'POST',
        body,
        null,
        () => {
          this.createPending = true;
          this.createError = false;
          this.createSuccess = false;
          this.createMessage = '';
        },
        (response: Recruitment) => {
          this.recruitment = response;
          this.createSuccess = true;
          this.createMessage = 'Tạo thông tin công ty thành công';
          this.createPending = false;
          this.createError = false;
        },
        (error: any, message: string) => {
          this.createError = true;
          this.createSuccess = false;
          this.createPending = false;
          this.createMessage = message || 'Tạo thông tin công ty thất bại';
        },
      );
    },

    async update(id: number, body: Record<string, any>) {
      httpRequestStore().authRequest<Recruitment>(
        `v1/employer/recruitment/details/${id}/update`,
        'POST',
        body,
        null,
        () => {
          this.updatePending = true;
          this.updateError = false;
          this.updateSuccess = false;
          this.updateMessage = '';
        },
        (response: Recruitment) => {
          this.recruitment = response;
          this.updateItemsState(response);
          this.updateSuccess = true;
          this.updateMessage = 'Cập nhật thông tin công ty thành công';
          this.updatePending = false;
          this.updateError = false;
        },
        (error: any, message: string) => {
          this.updateError = true;
          this.updateSuccess = false;
          this.updatePending = false;
          this.updateMessage = message || 'Cập nhật thông tin công ty thất bại';
        },
      );
    },

    async deleteById(id: number) {
      httpRequestStore().authRequest<Recruitment>(
        `v1/employer/recruitment/details/${id}/delete`,
        'POST',
        {},
        null,
        () => {
          this.deletePending = true;
          this.deleteError = false;
          this.deleteSuccess = false;
          this.deleteMessage = '';
        },
        (response: Recruitment) => {
          this.updateItemsState(response);
          this.deleteSuccess = true;
          this.deleteMessage = 'Xóa thông tin công ty thành công';
          this.deletePending = false;
          this.deleteError = false;
        },
        (error: any, message: string) => {
          this.deleteError = true;
          this.deleteSuccess = false;
          this.deletePending = false;
          this.deleteMessage = message || 'Xóa thông tin công ty thất bại';
        },
      );
    },

    async getDetails(id: number) {
      httpRequestStore().authRequest<Recruitment>(
        `v1/employer/recruitment/details/${id}/get`,
        'POST',
        {},
        null,
        () => {
          this.getDetailsPending = true;
          this.getDetailsError = false;
          this.getDetailsSuccess = false;
          this.getDetailsMessage = '';
        },
        (response: Recruitment) => {
          this.recruitment = response;
          this.getDetailsSuccess = true;
          this.getDetailsMessage = 'Lấy chi tiết thành công';
          this.getDetailsPending = false;
          this.getDetailsError = false;
        },
        (error: any, message: string) => {
          this.getDetailsError = true;
          this.getDetailsSuccess = false;
          this.getDetailsPending = false;
          this.getDetailsMessage = message || 'Lấy chi tiết thất bại';
        },
      );
    },

    updateItemsState(item: Recruitment) {
      const index = this.items.findIndex((i) => i.id === item.id);
      if (index !== -1) {
        this.items[index] = item;
      }
      this.items = this.items.map((_item) => ({ ..._item }));
    },
    deleteItemsState(id: number) {
      this.items = this.items.filter((i) => i.id !== id);
    },
  },
});
