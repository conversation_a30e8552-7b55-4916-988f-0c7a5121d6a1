import type { EnumRecruitmentApplyStatus, PagedResult, Recruitment, RecruitmentApply } from '~/types';
import { useAuthStore } from './auth.store';
import { httpRequestStore } from './httpRequest.store';

export const useEmployerAppliedRecruitmentStore = defineStore('useEmployerAppliedRecruitmentStore', {
  state: () => ({
    recruitment: null as Recruitment | null,
    updatePending: false,
    updateError: false,
    updateSuccess: false,
    updateMessage: null as string | null,

    createPending: false,
    createError: false,
    createSuccess: false,
    createMessage: null as string | null,

    getListPending: false,
    getListError: false,
    getListSuccess: false,
    getListMessage: null as string | null,
    total: 0,
    items: [] as RecruitmentApply[],

    getDetailsPending: false,
    getDetailsError: false,
    getDetailsSuccess: false,
    getDetailsMessage: null as string | null,
    details: null as RecruitmentApply | null,
  }),

  actions: {
    async getList(body: { page: number; limit: number; status?: EnumRecruitmentApplyStatus }) {
      httpRequestStore().authRequest<PagedResult<RecruitmentApply>>(
        'v1/employer/recruitment-apply/get-list',
        'POST',
        body,
        null,
        () => {
          this.getListPending = true;
          this.getListError = false;
          this.getListSuccess = false;
          this.getListMessage = '';
        },
        (response: PagedResult<RecruitmentApply>) => {
          this.items = response.items;
          this.total = response.total;
          this.getListSuccess = true;
          this.getListMessage = 'Lấy thông tin công ty thành công';
          this.getListPending = false;
          this.getListError = false;
        },
        (error: any, message: string) => {
          this.getListError = true;
          this.getListSuccess = false;
          this.getListPending = false;
          this.getListMessage = message || 'Lấy thông tin công ty thất bại';
        },
      );
    },

    async create(body: Record<string, any>) {
      const baseUrl = useRuntimeConfig().public.apiUrl;

      this.createPending = true;
      try {
        const response = await $fetch<Recruitment>(`${baseUrl}/v1/employer/recruitment/create`, {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${useAuthStore().accessToken}`,
          },
          body,
        });
        this.recruitment = response;
        this.createSuccess = true;
        this.createMessage = 'Tạo thông tin công ty thành công';
      } catch (error) {
        this.createError = true;
        this.createMessage = 'Tạo thông tin công ty thất bại';
      } finally {
        this.createPending = false;
      }
    },

    async getDetails(recruitmentId: number) {
      await httpRequestStore().authRequest<RecruitmentApply>(
        'v1/employer/recruitment-apply/get-by-recruitment-id',
        'POST',
        { recruitmentId },
        null,
        () => {
          this.getDetailsPending = true;
          this.getDetailsError = false;
          this.getDetailsSuccess = false;
          this.getDetailsMessage = '';
        },
        (response: RecruitmentApply) => {
          this.details = response;
          this.getDetailsSuccess = true;
          this.getDetailsMessage = 'Lấy thông tin công ty thành công';
          this.getDetailsPending = false;
          this.getDetailsError = false;
        },
        (error: any, message: string) => {
          this.getDetailsError = true;
          this.getDetailsSuccess = false;
          this.getDetailsPending = false;
          this.getDetailsMessage = message || 'Lấy thông tin công ty thất bại';
        },
      );
    },

    async update(id: number, status: EnumRecruitmentApplyStatus) {
      await httpRequestStore().authRequest<RecruitmentApply>(
        'v1/employer/recruitment-apply/details/update',
        'POST',
        { id, status },
        null,
        () => {
          this.updatePending = true;
          this.updateError = false;
          this.updateSuccess = false;
          this.updateMessage = '';
        },
        (response: RecruitmentApply) => {
          this.updateSuccess = true;
          this.updateMessage = 'Cập nhật thông tin công ty thành công';
          this.updatePending = false;
          this.updateError = false;
          if (this.items) {
            const index = this.items.findIndex((item) => item.id === id);
            if (index !== -1) {
              this.items[index] = response;
            }
          }
        },
        (error: any, message: string) => {
          this.updateError = true;
          this.updateSuccess = false;
          this.updatePending = false;
          this.updateMessage = message || 'Cập nhật thông tin công ty thất bại';
        },
      );
    },
  },
});
