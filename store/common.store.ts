import type {
  CanBeNil,
  CommonData,
  EmployeeSize,
  JobD<PERSON>ree,
  JobExperience,
  JobGender,
  JobLevel,
  JobMethod,
  JobSalary,
  Language,
  LanguageLevel,
  Occupation,
  Province,
} from "~/types";
import { httpRequestStore } from './httpRequest.store';

export const useCommonStore = defineStore("common", {
  state: () => ({
    // loading: false,
    getCommonDataPending: false,
    getCommonDataError: false,
    getCommonDataSuccess: false,
    getCommonDataMessage: null as CanBeNil<string>,

    jobDegrees: [] as JobDegree[],
    jobGenders: [] as JobGender[],
    jobExperiences: [] as JobExperience[],
    jobLevels: [] as JobLevel[],
    jobMethods: [] as JobMethod[],
    jobSalarys: [] as JobSalary[],
    occupations: [] as Occupation[],
    provinces: [] as Province[],
    languages: [] as Language[],
    employeeSizes: [] as EmployeeSize[],
    languageLevels: [] as LanguageLevel[],
    districts: [] as Province[],
    topRecruitmentByOccupation: [] as {
      occupationId: number;
      count: number;
    }[],
  }),
  actions: {
    async getCommonData() {
      await httpRequestStore().request(
        "v1/base/common-data",
        "GET",
        null,
        null,
        () => {
          this.getCommonDataPending = true;
          this.getCommonDataError = false;
          this.getCommonDataSuccess = false;
          this.getCommonDataMessage = null;
        },
        (response: CommonData) => {
          this.getCommonDataPending = false;
          this.getCommonDataError = false;
          this.getCommonDataSuccess = true;
          this.getCommonDataMessage = 'Lấy dữ liệu thành công';

          this.jobDegrees = response.jobDegrees;
          this.jobGenders = response.jobGenders;
          this.jobExperiences = response.jobExperiences;
          this.jobLevels = response.jobLevels;
          this.jobMethods = response.jobMethods;
          this.jobSalarys = response.jobSalarys;
          this.occupations = response.occupations;
          this.provinces = response.provinces;
          this.languages = response.languages;
          this.employeeSizes = response.employeeSizes;
          this.languageLevels = response.languageLevels;
          this.districts = response.districts;
          this.topRecruitmentByOccupation =
            response.topRecruitmentByOccupation;
        },
        (error) => {
          this.getCommonDataPending = false;
          this.getCommonDataError = true;
          this.getCommonDataSuccess = false;
          this.getCommonDataMessage = error?.message || 'Có lỗi xảy ra';
        }
      )
    },
    findJobLevelById(id: number | null | undefined) {
      if (!id) return null;
      return this.jobLevels.find((jobLevel) => jobLevel.id === id);
    },
    findSalaryById(id: number | null | undefined) {
      if (!id) return null;
      return this.jobSalarys.find((jobSalary) => jobSalary.id === id);
    },
    findDegreeById(id: number | null | undefined) {
      if (!id) return null;
      return this.jobDegrees.find((jobDegree) => jobDegree.id === id);
    },
    findExperienceById(id: number | null | undefined) {
      if (!id) return null;
      return this.jobExperiences.find(
        (jobExperience) => jobExperience.id === id,
      );
    },
    findOccupationByIds(ids: number[] | null | undefined) {
      if (!ids || !ids.length) return [];
      return this.occupations.filter((occupation) =>
        ids.includes(occupation.id),
      );
    },
    findJobMethodById(id: number | null | undefined) {
      if (!id) return null;
      return this.jobMethods?.find((jobMethod) => jobMethod.id === id);
    },
    findProvinceByIds(ids: number[] | null | undefined) {
      if (!ids || !ids.length) return [];
      return this.provinces.filter((province) => ids.includes(province.id));
    },
    findProvinceById(id: number | null | undefined) {
      if (!id) return null;
      return this.provinces.find((province) => province.id === id);
    },
    findDistrictById(id: number | null | undefined) {
      if (!id) return null;
      return this.districts.find((district) => district.id === id);
    },
    findByOccupationId(id: number | null | undefined) {
      if (!id) return null;
      return this.occupations.find((occupation) => occupation.id === id);
    },
    findByOccupationIds(ids: number[] | null | undefined) {
      if (!ids) return null;
      return this.occupations.filter((occupation) =>
        ids.includes(occupation.id),
      );
    },
    findByLanguageId(id: number | null | undefined) {
      if (!id) return null;
      return this.languages.find((language) => language.id === id);
    },
    findByLanguageLevelId(id: number | null | undefined) {
      if (!id) return null;
      const val = this.languageLevels.find(
        (languageLevel) => languageLevel.id === id,
      );
      console.log("findByLanguageLevelId", id, val, this.languageLevels);
      return val;
    },
    findEmployeeSizeById(id: number | null | undefined) {
      if (!id) return null;
      return this.employeeSizes.find((employeeSize) => employeeSize.id === id);
    },
  },
});
