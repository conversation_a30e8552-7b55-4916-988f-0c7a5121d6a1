import type { PagedResult } from '~/types';
import type { RecruitmentFavorite } from '~/types/favoriteRecruitment';
import { httpRequestStore } from './httpRequest.store';

export const recruitmentFavoriteStore = defineStore('myFavorite', {
  state: () => ({
    loading: false,
    getListByIdsPending: false,
    getListByIdsError: false,
    getListByIdsSuccess: false,
    getListByIdsErrorMessage: '',
    favoriteList: [] as RecruitmentFavorite[],

    pagedList: [] as RecruitmentFavorite[],
    total: 0,

    getPagedListPending: false,
    getPagedListError: false,
    getPagedListSuccess: false,
    getPagedListErrorMessage: '',

    addPending: false,
    addError: false,
    addSuccess: false,
    addErrorMessage: '',

    removePending: false,
    removeError: false,
    removeSuccess: false,
    removeErrorMessage: '',
  }),
  actions: {
    async getFavoriteRecruitmentIds(recruitmentIds: number[]) {
      await httpRequestStore().authRequest<RecruitmentFavorite[]>(
        'v1/customer/recruitment-favorite/me/get-ids',
        'POST',
        {
          recruitmentIds,
        },
        null,
        () => {
          this.getListByIdsPending = true;
          this.getListByIdsError = false;
          this.getListByIdsSuccess = false;
          this.getListByIdsErrorMessage = '';
        },
        (data: RecruitmentFavorite[]) => {
          this.favoriteList = data;
          this.getListByIdsPending = false;
          this.getListByIdsError = false;
          this.getListByIdsSuccess = true;
          this.getListByIdsErrorMessage = 'Lấy danh sách yêu thích thành công';
        },
        (error: any, message: string) => {
          this.getListByIdsPending = false;
          this.getListByIdsError = true;
          this.getListByIdsSuccess = false;
          this.getListByIdsErrorMessage = message || 'Lấy danh sách yêu thích thất bại';
        },
      );
    },
    async addFavoriteRecruitment(recruitmentId: number) {
      await httpRequestStore().authRequest<RecruitmentFavorite>(
        'v1/customer/recruitment-favorite/me/add',
        'POST',
        {
          recruitmentId,
        },
        null,
        () => {
          this.addPending = true;
          this.addError = false;
          this.addSuccess = false;
          this.addErrorMessage = '';
        },
        (data: RecruitmentFavorite) => {
          this.favoriteList.push(data);
          this.addPending = false;
          this.addError = false;
          this.addSuccess = true;
          this.addErrorMessage = 'Thêm vào danh sách yêu thích thành công';
        },
        (error: any, message: string) => {
          this.addPending = false;
          this.addError = true;
          this.addSuccess = false;
          this.addErrorMessage = message || 'Thêm vào danh sách yêu thích thất bại';
        },
      );
    },
    async removeFavoriteRecruitment(recruitmentId: number) {
      /* try {
        this.loading = true;
        await $fetch(`/v1/customer/recruitment-favorite/me/delete`, {
          method: 'POST',
          baseURL: useRuntimeConfig().public.apiUrl,
          headers: {
            Authorization: `Bearer ${useAuthStore().accessToken}`,
          },
          body: JSON.stringify({
            recruitmentId,
          }),
        });
        this.favoriteList = this.favoriteList.filter((favorite) => favorite.recruitmentId !== recruitmentId);
        this.pagedList = this.pagedList.filter((favorite) => favorite.recruitmentId !== recruitmentId);
      } catch (error) {
        console.error(error);
      } finally {
        this.loading = false;
      } */

      await httpRequestStore().authRequest<RecruitmentFavorite>(
        'v1/customer/recruitment-favorite/me/delete',
        'POST',
        {
          recruitmentId,
        },
        null,
        () => {
          this.removePending = true;
          this.removeError = false;
          this.removeSuccess = false;
          this.removeErrorMessage = '';
        },
        (data: RecruitmentFavorite) => {
          this.favoriteList = this.favoriteList.filter((favorite) => favorite.recruitmentId !== recruitmentId);
          this.pagedList = this.pagedList.filter((favorite) => favorite.recruitmentId !== recruitmentId);
          this.removePending = false;
          this.removeError = false;
          this.removeSuccess = true;
          this.removeErrorMessage = 'Xóa khỏi danh sách yêu thích thành công';
        },
        (error: any, message: string) => {
          this.removePending = false;
          this.removeError = true;
          this.removeSuccess = false;
          this.removeErrorMessage = message || 'Xóa khỏi danh sách yêu thích thất bại';
        },
      );
    },
    isFavorite(recruitmentId: number): boolean {
      return this.favoriteList.some((favorite) => favorite.recruitmentId === recruitmentId);
    },
    async getPagedList(limit: number, page: number) {
      await httpRequestStore().authRequest<PagedResult<RecruitmentFavorite>>(
        'v1/customer/recruitment-favorite/me/get-list',
        'POST',
        {
          limit,
          page,
        },
        null,
        () => {
          this.getPagedListPending = true;
          this.getPagedListError = false;
          this.getPagedListSuccess = false;
          this.getPagedListErrorMessage = '';
        },
        (data: PagedResult<RecruitmentFavorite>) => {
          this.pagedList = data.items;
          this.total = data.total;
          this.getPagedListPending = false;
          this.getPagedListError = false;
          this.getPagedListSuccess = true;
          this.getPagedListErrorMessage = 'Lấy danh sách thành công';
        },
        (error: any, message: string) => {
          this.getPagedListPending = false;
          this.getPagedListError = true;
          this.getPagedListSuccess = false;
          this.getPagedListErrorMessage = message || 'Lấy danh sách thất bại';
        },
      );
    },
  },
});
