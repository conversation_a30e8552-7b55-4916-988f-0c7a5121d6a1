import {
  ResumeType,
  type Resume,
  type ResumeAcademy,
  type ResumeExperience,
  type ResumeForeignLanguage,
  type ResumeRef,
  type ResumeTech,
} from '~/types/resume.interface';
import { httpRequestStore } from './httpRequest.store';
import { ToastType, useToast } from './toast';
import { TinyInt } from '~/types';

export const useResumeStore = defineStore('resume', {
  state: () => ({
    loading: false,
    resumes: [] as Resume[],
    onlineResumes: [] as Resume[],
    offlineResumes: [] as Resume[],

    getOnlineResumesPending: false,
    getOnlineResumesError: false,
    getOnlineResumesErrorMessage: '',
    getOnlineResumesSuccess: false,

    getAttachedResumesPending: false,
    getAttachedResumesError: false,
    getAttachedResumesErrorMessage: '',
    getAttachedResumesSuccess: false,

    getResumesPending: false,
    getResumesError: false,
    getResumesErrorMessage: '',
    getResumesSuccess: false,

    createPending: false,
    createError: false,
    createErrorMessage: '',
    createSuccess: false,

    updatePending: false,
    updateError: false,
    updateErrorMessage: '',
    updateSuccess: false,

    deletePending: false,
    deleteError: false,
    deleteErrorMessage: '',
    deleteSuccess: false,

    getResumePending: false,
    getResumeError: false,
    getResumeErrorMessage: '',
    getResumeSuccess: false,
    resume: null as Resume | null,

    createExperiencePending: false,
    createExperienceError: false,
    createExperienceErrorMessage: '',
    createExperienceSuccess: false,

    updateExperiencePending: false,
    updateExperienceError: false,
    updateExperienceErrorMessage: '',
    updateExperienceSuccess: false,

    deleteExperiencePending: false,
    deleteExperienceError: false,
    deleteExperienceErrorMessage: '',
    deleteExperienceSuccess: false,

    createAcademyPending: false,
    createAcademyError: false,
    createAcademyErrorMessage: '',
    createAcademySuccess: false,

    updateAcademyPending: false,
    updateAcademyError: false,
    updateAcademyErrorMessage: '',
    updateAcademySuccess: false,

    deleteAcademyPending: false,
    deleteAcademyError: false,
    deleteAcademyErrorMessage: '',
    deleteAcademySuccess: false,

    createLanguagePending: false,
    createLanguageError: false,
    createLanguageErrorMessage: '',
    createLanguageSuccess: false,

    updateLanguagePending: false,
    updateLanguageError: false,
    updateLanguageErrorMessage: '',
    updateLanguageSuccess: false,

    deleteLanguagePending: false,
    deleteLanguageError: false,
    deleteLanguageErrorMessage: '',
    deleteLanguageSuccess: false,

    createTechPending: false,
    createTechError: false,
    createTechErrorMessage: '',
    createTechSuccess: false,

    updateTechPending: false,
    updateTechError: false,
    updateTechErrorMessage: '',
    updateTechSuccess: false,

    deleteTechPending: false,
    deleteTechError: false,
    deleteTechErrorMessage: '',
    deleteTechSuccess: false,

    createRefsPending: false,
    createRefsError: false,
    createRefsErrorMessage: '',
    createRefsSuccess: false,

    updateRefsPending: false,
    updateRefsError: false,
    updateRefsErrorMessage: '',
    updateRefsSuccess: false,

    deleteRefsPending: false,
    deleteRefsError: false,
    deleteRefsErrorMessage: '',
    deleteRefsSuccess: false,
  }),
  getters: {
    hasAttachedResume(): boolean {
      if (!this.resume) {
        return false;
      }
      return this.resumes?.some((resume) => resume.type === ResumeType.offline);
    },
    hasOnlineResume(): boolean {
      if (!this.resume) {
        return false;
      }
      return this.resumes.some((resume) => resume.type === ResumeType.online);
    },
  },
  actions: {
    async getResumes() {
      return await httpRequestStore().authRequest<Resume[]>(
        `v1/resume/me`,
        'GET',
        null,
        null,
        () => {
          this.getResumesPending = true;
          this.getResumesError = false;
          this.getResumesSuccess = false;
        },
        (data: Resume[]) => {
          this.resumes = data;
          this.getResumesSuccess = true;
          this.getResumesError = false;
          this.getResumesErrorMessage = '';
          this.getResumePending = false;
        },
        (error: Error, message: string) => {
          this.getResumesError = true;
          this.getResumesErrorMessage = message || 'Lấy danh sách hồ sơ thất bại';
          this.getResumesPending = false;
        },
      );
    },

    async deleteResume(resumeId: number) {
      return await httpRequestStore().authRequest<Resume>(
        `v1/resume/me/${resumeId}/delete`,
        'POST',
        null,
        null,
        () => {
          this.deletePending = true;
          this.deleteError = false;
          this.deleteSuccess = false;
        },
        (data: Resume) => {
          this.deleteSuccess = true;
          this.deleteError = false;
          this.deleteErrorMessage = '';
          this.deletePending = false;
          this.resumes = this.resumes.filter((resume) => resume.id !== resumeId);
          this.onlineResumes = this.onlineResumes.filter((resume) => resume.id !== resumeId);
          this.offlineResumes = this.offlineResumes.filter((resume) => resume.id !== resumeId);
          useToast().showToast('Xóa hồ sơ thành công', ToastType.Success);
        },
        (error: Error, message: string) => {
          this.deleteError = true;
          this.deleteSuccess = false;
          this.deleteErrorMessage = message || 'Xóa hồ sơ thất bại';
          this.deletePending = false;
          useToast().showToast(this.deleteErrorMessage, ToastType.Error);
        },
      );
    },

    async getOnlineResumes() {
      return await httpRequestStore().authRequest<Resume[]>(
        `v1/resume/me?type=1`,
        'GET',
        null,
        null,
        () => {
          this.getOnlineResumesPending = true;
          this.getOnlineResumesError = false;
          this.getOnlineResumesSuccess = false;
        },
        (data: Resume[]) => {
          this.onlineResumes = data;
          this.getOnlineResumesSuccess = true;
          this.getOnlineResumesError = false;
          this.getOnlineResumesErrorMessage = '';
          this.getOnlineResumesPending = false;
        },
        (error: Error, message: string) => {
          this.getOnlineResumesError = true;
          this.getOnlineResumesErrorMessage = message || 'Lấy danh sách hồ sơ thất bại';
          this.getOnlineResumesPending = false;
        },
      );
    },

    async getAttachedResumes() {
      return await httpRequestStore().authRequest<Resume[]>(
        `v1/resume/me?type=2`,
        'GET',
        null,
        null,
        () => {
          this.getAttachedResumesPending = true;
          this.getAttachedResumesError = false;
          this.getAttachedResumesSuccess = false;
        },
        (data: Resume[]) => {
          this.offlineResumes = data;
          this.getAttachedResumesSuccess = true;
          this.getAttachedResumesError = false;
          this.getAttachedResumesErrorMessage = '';
          this.getAttachedResumesPending = false;
        },
        (error: Error, message: string) => {
          this.getAttachedResumesError = true;
          this.getAttachedResumesErrorMessage = message || 'Lấy danh sách hồ sơ thất bại';
          this.getAttachedResumesPending = false;
        },
      );
    },

    async updateGeneralInfo(resumeId: number, partial: Partial<Omit<Resume, 'id' | 'customerId' | 'totalView'>>) {
      return await httpRequestStore().authRequest<Resume>(
        `v1/resume/me/${resumeId}`,
        'POST',
        partial,
        null,
        () => {
          this.updatePending = true;
          this.updateError = false;
          this.updateSuccess = false;
        },
        (data: Resume) => {
          this.updateSuccess = true;
          this.updateError = false;
          this.updateErrorMessage = '';
          this.updatePending = false;
          this.resume = data;
          useToast().showToast('Cập nhật hồ sơ thành công', ToastType.Success);
        },
        (error: Error, message: string) => {
          this.updateError = true;
          this.updateSuccess = false;
          this.updateErrorMessage = '';
          this.updatePending = false;
          this.updateErrorMessage = message || 'Cập nhật thất bại';
          useToast().showToast(this.updateErrorMessage, ToastType.Error);
        },
      );
    },

    async getResume(id: number) {
      return await httpRequestStore().authRequest<Resume>(
        `v1/resume/me/${id}`,
        'GET',
        null,
        null,
        () => {
          this.getResumePending = true;
          this.getResumeError = false;
          this.getResumeSuccess = false;
        },
        (data: Resume) => {
          this.resume = data;
          this.getResumeSuccess = true;
          this.getResumeError = false;
          this.getResumeErrorMessage = '';
          this.getResumePending = false;
        },
        (error: Error, message: string) => {
          this.getResumeError = true;
          this.getResumeErrorMessage = message || 'Lấy thông tin hồ sơ thất bại';
          this.getResumePending = false;
        },
      );
    },

    async createResume(partial: Partial<Omit<Resume, 'id' | 'customerId' | 'totalView'>>) {
      return await httpRequestStore().authRequest<Resume>(
        `v1/resume`,
        'POST',
        partial,
        null,
        () => {
          this.createPending = true;
          this.createError = false;
          this.createSuccess = false;
        },
        (data: Resume) => {
          this.createSuccess = true;
          this.resume = data;
          this.createError = false;
          this.createErrorMessage = '';
          this.createPending = false;
          useToast().showToast('Tạo hồ sơ thành công', ToastType.Success);
        },
        (error: Error, message: string) => {
          this.createError = true;
          this.createSuccess = false;
          this.createErrorMessage = message || 'Tạo hồ sơ thất bại';
          this.createPending = false;
          useToast().showToast(this.createErrorMessage, ToastType.Error);
        },
      );
    },

    async addReference(resumeId: number, reference: Partial<ResumeRef>) {
      return await httpRequestStore().authRequest<ResumeRef>(
        `v1/resume/me/${resumeId}/ref/save`,
        'POST',
        reference,
        null,
        () => {
          this.createRefsPending = true;
          this.createRefsError = false;
          this.createRefsSuccess = false;
          this.createRefsErrorMessage = '';
        },
        (data: ResumeRef) => {
          this.createRefsSuccess = true;
          this.createRefsError = false;
          this.createRefsErrorMessage = '';
          this.createRefsPending = false;
          if (!this.resume?.refs) {
            this.resume!.refs = [];
          }
          this.resume?.refs?.push(data);
          useToast().showToast('Tạo thông tin thành công', ToastType.Success);
        },
        (error: Error, message: string) => {
          this.createRefsError = true;
          this.createRefsSuccess = false;
          this.createRefsPending = false;
          this.createRefsErrorMessage = message || 'Tạo thành công';
          useToast().showToast(this.createRefsErrorMessage, ToastType.Error);
        },
      );
    },

    async updateReference(resumeId: number, refId: number, reference: Partial<ResumeRef>) {
      return await httpRequestStore().authRequest<ResumeRef>(
        `v1/resume/me/${resumeId}/ref/${refId}/save`,
        'POST',
        reference,
        null,
        () => {
          this.updateRefsPending = true;
          this.updateRefsError = false;
          this.updateRefsSuccess = false;
        },
        (data: ResumeRef) => {
          this.updateRefsSuccess = true;
          this.updateRefsError = false;
          this.updateRefsErrorMessage = '';
          this.updateRefsPending = false;
          const index = this.resume?.refs?.findIndex((ref) => ref.id === refId);
          if (index !== undefined && this.resume) {
            this.resume.refs?.splice(index, 1, data);
          }
          useToast().showToast('Cập nhật thành công', ToastType.Success);
        },
        (error: Error, message: string) => {
          this.updateRefsError = true;
          this.updateRefsSuccess = false;
          this.updateRefsPending = false;
          this.updateRefsErrorMessage = '';
          this.updateRefsErrorMessage = message || 'Cập nhật thất bại';
          useToast().showToast(this.updateRefsErrorMessage, ToastType.Error);
        },
      );
    },

    async removeReference(resumeId: number, referenceId: number) {
      return await httpRequestStore().authRequest<Resume>(
        `v1/resume/me/${resumeId}/ref/${referenceId}/delete`,
        'POST',
        null,
        null,
        () => {
          this.deleteRefsPending = true;
          this.deleteRefsError = false;
          this.deleteRefsSuccess = false;
        },
        (data: Resume) => {
          this.deleteRefsSuccess = true;
          const index = this.resume?.refs?.findIndex((ref) => ref.id === referenceId);
          if (index !== undefined && this.resume) {
            this.resume.refs = this.resume.refs?.filter((experience) => experience.id !== referenceId);
          }
          useToast().showToast('Xóa thành công', ToastType.Success);
        },
        (error: Error, message: string) => {
          this.deleteRefsError = true;
          this.deleteRefsErrorMessage = message || 'Xóa thông tin thất bại';
          useToast().showToast(this.deleteRefsErrorMessage, ToastType.Error);
        },
      );
    },

    async createITSkills(resumeId: number, resumeIT: Partial<ResumeTech>) {
      await httpRequestStore().authRequest<ResumeTech>(
        `v1/resume/me/${resumeId}/tech/save`,
        'POST',
        resumeIT,
        null,
        () => {
          this.createTechPending = true;
          this.createTechError = false;
          this.createTechSuccess = false;
        },
        (data: ResumeTech) => {
          this.createTechSuccess = true;
          this.createTechError = false;
          this.createTechErrorMessage = '';
          this.createTechPending = false;
          if (!this.resume?.techs) {
            this.resume!.techs = [];
          }
          console.log('ResumeTech: ', data);
          this.resume?.techs?.push(data);
          useToast().showToast('Tạo thông tin thành công 22', ToastType.Success);
        },
        (error: Error, message: string) => {
          this.createTechError = true;
          this.createTechErrorMessage = message || 'Tạo thành công';
          this.createTechPending = false;
          this.createTechSuccess = false;
          useToast().showToast(this.createTechErrorMessage, ToastType.Error);
        },
      );
    },

    async updateITSkills(resumeId: number, id: number, resumeIT: Partial<ResumeTech>) {
      await httpRequestStore().authRequest<ResumeTech>(
        `v1/resume/me/${resumeId}/tech/${id}/save`,
        'POST',
        resumeIT,
        null,
        () => {
          this.updateTechPending = true;
          this.updateTechError = false;
          this.updateTechSuccess = false;
          this.updateTechErrorMessage = '';
        },
        (data: ResumeTech) => {
          this.updateTechSuccess = true;
          this.updateTechError = false;
          this.updateTechErrorMessage = '';
          this.updateTechPending = false;
          const index = this.resume?.techs?.findIndex((tech) => tech.id === id);
          if (index !== undefined && this.resume) {
            this.resume.techs?.splice(index, 1, data);
          }
          useToast().showToast('Tạo thông tin thành công', ToastType.Success);
        },
        (error: Error, message: string) => {
          this.updateTechError = true;
          this.updateTechErrorMessage = message || 'Tạo thành công';
          this.updateTechPending = false;
          this.updateTechSuccess = false;
          useToast().showToast(this.updateTechErrorMessage, ToastType.Error);
        },
      );
    },

    async createAcademy(resumeId: number, academy: Omit<ResumeAcademy, 'id' | 'resumeId'>) {
      await httpRequestStore().authRequest<ResumeAcademy>(
        `v1/resume/me/${resumeId}/academy/save`,
        'POST',
        academy,
        null,
        () => {
          this.createAcademyPending = true;
          this.createAcademyError = false;
          this.createAcademySuccess = false;
        },
        (data: ResumeAcademy) => {
          this.createAcademySuccess = true;
          this.createAcademyError = false;
          this.createAcademyErrorMessage = '';
          this.createAcademyPending = false;
          useToast().showToast('Tạo thông tin học vấn thành công', ToastType.Success);
          if (!this.resume) {
            return;
          }

          if (!this.resume?.academies) {
            this.resume!.academies = [];
          }
          this.resume?.academies?.push(data);
          this.resume.educationCompleted = TinyInt.Yes;
          this.checkAndSetResumeCompleted();
        },
        (error: Error, message: string) => {
          this.createAcademyError = true;
          this.createAcademyPending = false;
          this.createAcademySuccess = false;
          this.createAcademyErrorMessage = message || 'Tạo thành công';
          useToast().showToast(this.createAcademyErrorMessage, ToastType.Error);
        },
      );
    },
    async updateAcademy(resumeId: number, id: number, partial: Omit<ResumeAcademy, 'id' | 'resumeId'>) {
      await httpRequestStore().authRequest<ResumeAcademy>(
        `v1/resume/me/${resumeId}/academy/${id}/save`,
        'POST',
        partial,
        null,
        () => {
          this.updateAcademyPending = true;
          this.updateAcademyError = false;
          this.updateAcademySuccess = false;
          this.updateAcademyErrorMessage = '';
        },
        (data: ResumeAcademy) => {
          this.updateAcademySuccess = true;
          this.updateAcademyError = false;
          this.updateAcademyErrorMessage = '';
          this.updateAcademyPending = false;
          const index = this.resume?.academies?.findIndex((academy) => academy.id === id);
          if (index !== undefined && this.resume) {
            this.resume.academies?.splice(index, 1, data);
          }
          useToast().showToast('Tạo thông tin học vấn thành công', ToastType.Success);
        },
        (error: Error, message: string) => {
          this.updateAcademyError = true;
          this.updateAcademySuccess = false;
          this.updateAcademyPending = false;
          this.updateAcademyErrorMessage = message || 'Tạo thành công';
          useToast().showToast(this.updateAcademyErrorMessage, ToastType.Error);
        },
      );
    },
    async removeAcademy(resumeId: number, academyId: number) {
      await httpRequestStore().authRequest<Resume>(
        `v1/resume/me/${resumeId}/academy/${academyId}/delete`,
        'POST',
        null,
        null,
        () => {
          this.deleteAcademyPending = true;
          this.deleteAcademyError = false;
          this.deleteAcademySuccess = false;
        },
        (data: Resume) => {
          this.deleteAcademySuccess = true;
          this.deleteAcademyError = false;
          this.deleteAcademyErrorMessage = '';
          useToast().showToast('Xóa thành công', ToastType.Success);
          if (!this.resume) {
            return;
          }
          const index = this.resume?.academies?.findIndex((academy) => academy.id === academyId);
          if (index !== undefined && this.resume) {
            this.resume.academies = this.resume.academies?.filter((academy) => academy.id !== academyId);
          }
          if (!this.resume.academies?.length) {
            this.resume.educationCompleted = TinyInt.No;
            this.checkAndSetResumeCompleted();
          }
        },
        (error: Error, message: string) => {
          this.deleteAcademyError = true;
          this.deleteAcademyErrorMessage = message || 'Xóa thất bại';
          this.deleteAcademyPending = false;
          useToast().showToast(this.deleteAcademyErrorMessage, ToastType.Error);
        },
      );
    },

    async updateLanguage(resumeId: number, id: number, partial: Omit<ResumeForeignLanguage, 'id' | 'resumeId'>) {
      await httpRequestStore().authRequest<ResumeForeignLanguage>(
        `v1/resume/me/${resumeId}/languages/${id}/save`,
        'POST',
        partial,
        null,
        () => {
          this.updateLanguagePending = true;
          this.updateLanguageError = false;
          this.updateLanguageSuccess = false;
          this.updateLanguageErrorMessage = '';
        },
        (data: ResumeForeignLanguage) => {
          this.updateLanguageSuccess = true;
          this.updateLanguageError = false;
          this.updateLanguageErrorMessage = '';
          this.updateLanguagePending = false;
          const index = this.resume?.languages?.findIndex((academy) => academy.id === id);
          if (index !== undefined && this.resume) {
            this.resume.languages?.splice(index, 1, data);
          }
          useToast().showToast('Tạo thông tin ngôn ngữ thành công', ToastType.Success);
        },
        (error: Error, message: string) => {
          this.updateLanguageError = true;
          this.updateLanguageSuccess = false;
          this.updateLanguagePending = false;
          this.updateLanguageErrorMessage = message || 'Tạo thành công';
          useToast().showToast(this.updateLanguageErrorMessage, ToastType.Error);
        },
      );
    },
    async removeLanguage(resumeId: number, id: number) {
      await httpRequestStore().authRequest<Resume>(
        `v1/resume/me/${resumeId}/languages/${id}/delete`,
        'POST',
        null,
        null,
        () => {
          this.deleteLanguagePending = true;
          this.deleteLanguageError = false;
          this.deleteLanguageSuccess = false;
        },
        (data: Resume) => {
          this.deleteLanguageSuccess = true;
          this.deleteLanguageError = false;
          this.deleteLanguageErrorMessage = '';
          const index = this.resume?.languages?.findIndex((language) => language.id === id);
          if (index !== undefined && this.resume) {
            this.resume.languages = this.resume.languages?.filter((language) => language.id !== id);
          }
          useToast().showToast('Xóa thành công', ToastType.Success);
        },
        (error: Error, message: string) => {
          this.deleteLanguageError = true;
          this.deleteLanguageSuccess = false;
          this.deleteLanguagePending = false;
          this.deleteLanguageErrorMessage = message || 'Xóa thất bại';
          useToast().showToast(this.deleteLanguageErrorMessage, ToastType.Error);
        },
      );
    },
    async createLanguage(resumeId: number, language: Omit<ResumeForeignLanguage, 'id' | 'resumeId'>) {
      await httpRequestStore().authRequest<ResumeForeignLanguage>(
        `v1/resume/me/${resumeId}/languages/save`,
        'POST',
        language,
        null,
        () => {
          this.createLanguagePending = true;
          this.createLanguageError = false;
          this.createLanguageSuccess = false;
        },
        (data: ResumeForeignLanguage) => {
          this.createLanguageSuccess = true;
          this.createLanguageError = false;
          this.createLanguageErrorMessage = '';
          this.createLanguagePending = false;
          if (!this.resume?.languages) {
            this.resume!.languages = [];
          }
          this.resume?.languages?.push(data);
          useToast().showToast('Tạo thông tin học vấn thành công', ToastType.Success);
        },
        (error: Error, message: string) => {
          this.createLanguageError = true;
          this.createLanguageErrorMessage = message || 'Tạo thành công';
          this.createLanguagePending = false;
          this.createLanguageSuccess = false;
          useToast().showToast(this.createLanguageErrorMessage, ToastType.Error);
        },
      );
    },

    async createExperience(resumeId: number, experience: Omit<ResumeExperience, 'id' | 'resumeId'>) {
      await httpRequestStore().authRequest<ResumeExperience>(
        `v1/resume/me/${resumeId}/experience/save`,
        'POST',
        experience,
        null,
        () => {
          this.createExperiencePending = true;
          this.createExperienceError = false;
          this.createExperienceSuccess = false;
        },
        (data: ResumeExperience) => {
          this.createExperienceSuccess = true;
          this.createExperienceError = false;
          this.createExperienceErrorMessage = '';
          this.createExperiencePending = false;
          useToast().showToast('Tạo hồ sơ thành công', ToastType.Success);

          if (!this.resume) {
            return;
          }
          if (!this.resume.experiences) {
            this.resume!.experiences = [];
          }

          this.resume.experiences?.push(data);
          this.resume.experienceCompleted = TinyInt.Yes;
          this.checkAndSetResumeCompleted();
        },
        (error: Error, message: string) => {
          this.createExperienceError = true;
          this.createExperienceErrorMessage = message || 'Cập nhật thất bại';
          this.createExperiencePending = false;
          this.createExperienceSuccess = false;
          useToast().showToast(this.createExperienceErrorMessage, ToastType.Error);
        },
      );
    },
    async updateExperience(
      resumeId: number,
      experienceId: number,
      experience: Omit<ResumeExperience, 'id' | 'resumeId'>,
    ) {
      await httpRequestStore().authRequest<ResumeExperience>(
        `v1/resume/me/${resumeId}/experience/${experienceId}/save`,
        'POST',
        experience,
        null,
        () => {
          this.updateExperiencePending = true;
          this.updateExperienceError = false;
          this.updateExperienceSuccess = false;
          this.updateExperienceErrorMessage = '';
        },
        (data: ResumeExperience) => {
          this.updateExperienceSuccess = true;
          this.updateExperienceError = false;
          this.updateExperienceErrorMessage = '';
          this.updateExperiencePending = false;
          const index = this.resume?.experiences?.findIndex((experience) => experience.id === experienceId);
          if (index !== undefined && this.resume) {
            this.resume.experiences?.splice(index, 1, data);
          }
          useToast().showToast('Cập nhật thành công', ToastType.Success);
        },
        (error: Error, message: string) => {
          this.updateExperienceError = true;
          this.updateExperienceErrorMessage = message || 'Cập nhật thất bại';
          this.updateExperiencePending = false;
          this.updateExperienceSuccess = false;
          useToast().showToast(this.updateExperienceErrorMessage, ToastType.Error);
        },
      );
    },
    async removeExperience(resumeId: number, experienceId: number) {
      await httpRequestStore().authRequest<Resume>(
        `v1/resume/me/${resumeId}/experience/${experienceId}/delete`,
        'POST',
        null,
        null,
        () => {
          this.deleteExperiencePending = true;
          this.deleteExperienceError = false;
          this.deleteExperienceSuccess = false;
        },
        (data: Resume) => {
          this.deleteExperienceSuccess = true;
          this.deleteExperienceError = false;
          this.deleteExperienceErrorMessage = '';
          this.deleteExperiencePending = false;
          useToast().showToast('Xóa thành công', ToastType.Success);
          if (!this.resume) {
            return;
          }

          const index = this.resume?.experiences?.findIndex((experience) => experience.id === experienceId);
          if (index !== undefined && this.resume) {
            this.resume.experiences = this.resume.experiences?.filter((experience) => experience.id !== experienceId);
          }
          if (!this.resume.experiences?.length) {
            this.resume.experienceCompleted = TinyInt.No;
            this.checkAndSetResumeCompleted();
          }
        },
        (error: Error, message: string) => {
          this.deleteExperienceError = true;
          this.deleteExperienceErrorMessage = message || 'Xóa thông tin kinh nghiệm thất bại';
          this.deleteExperiencePending = false;
          this.deleteExperienceSuccess = false;
          useToast().showToast(this.deleteExperienceErrorMessage, ToastType.Error);
        },
      );
    },
    checkAndSetResumeCompleted(): void {
      if (!this.resume) {
        return;
      }
      let completed = TinyInt.No;
      if (this.resume.type === ResumeType.online) {
        completed = booleanToTinyint(
          !!(this.resume.generalInfoCompleted && this.resume.experienceCompleted && this.resume.educationCompleted),
        );
      } else {
        completed = booleanToTinyint(!!(this.resume.generalInfoCompleted && this.resume.cvFileCompleted));
      }
      this.resume.completed = completed;
    },
  },
});
