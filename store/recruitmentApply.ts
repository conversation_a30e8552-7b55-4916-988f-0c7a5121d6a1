import type { PagedResult, RecruitmentApply } from '~/types';
import { httpRequestStore } from './httpRequest.store';

export const appliedRecruitmentStore = defineStore('appliedRecruitment', {
  state: () => ({
    loading: false,
    list: [] as RecruitmentApply[],

    pagedList: [] as RecruitmentApply[],
    total: 0,

    getPagedListPending: false,
    getPagedListError: false,
    getPagedListSuccess: false,
    getPagedListErrorMessage: '',
    appliedRecruitment: [] as RecruitmentApply[],

    applyPending: false,
    applyError: false,
    applySuccess: false,
    applyErrorMessage: '',

    getByRecruitmentIdPending: false,
    getByRecruitmentIdError: false,
    getByRecruitmentIdSuccess: false,
    getByRecruitmentIdErrorMessage: '',

    cancelPending: false,
    cancelError: false,
    cancelSuccess: false,
    cancelErrorMessage: '',
  }),
  actions: {
    async getList(limit: number, page: number) {
      await httpRequestStore().authRequest<PagedResult<RecruitmentApply>>(
        'v1/customer/recruitment-apply/get-list',
        'POST',
        {
          limit,
          page,
        },
        null,
        () => {
          this.getPagedListPending = true;
          this.getPagedListError = false;
          this.getPagedListSuccess = false;
          this.getPagedListErrorMessage = '';
        },
        (data: PagedResult<RecruitmentApply>) => {
          this.getPagedListPending = false;
          this.getPagedListError = false;
          this.getPagedListSuccess = true;
          this.getPagedListErrorMessage = 'Lấy danh sách thành công';

          this.pagedList = data?.items;
          this.total = data.total;
        },
        (error: Error, message: string) => {
          this.getPagedListPending = false;
          this.getPagedListError = true;
          this.getPagedListSuccess = false;
          this.getPagedListErrorMessage = message || 'Lấy danh sách thất bại';
        },
      );
    },
    async apply(recruitmentId: number, resumeId: number) {
      await httpRequestStore().authRequest<RecruitmentApply>(
        'v1/customer/recruitment-apply/apply',
        'POST',
        {
          recruitmentId,
          resumeId,
        },
        null,
        () => {
          this.applyPending = true;
          this.applyError = false;
          this.applySuccess = false;
          this.applyErrorMessage = '';
        },
        (data: RecruitmentApply) => {
          this.applyPending = false;
          this.applyError = false;
          this.applySuccess = true;
          this.applyErrorMessage = 'Nộp hồ sơ thành công';

          if (!this.appliedRecruitment.some((item) => item.recruitmentId === recruitmentId)) {
            this.appliedRecruitment.push(data);
          }
        },
        (error: Error, message: string) => {
          this.applyPending = false;
          this.applyError = true;
          this.applySuccess = false;
          this.applyErrorMessage = message || 'Nộp hồ sơ thất bại';
        },
      );
    },
    async getByRecruitmentId(recruitmentId: number) {
      await httpRequestStore().authRequest<RecruitmentApply>(
        'v1/customer/recruitment-apply/get-by-recruitment-id',
        'POST',
        {
          recruitmentId,
        },
        null,
        () => {
          this.getByRecruitmentIdPending = true;
          this.getByRecruitmentIdError = false;
          this.getByRecruitmentIdSuccess = false;
          this.getByRecruitmentIdErrorMessage = '';
        },
        (data: RecruitmentApply) => {
          this.getByRecruitmentIdPending = false;
          this.getByRecruitmentIdError = false;
          this.getByRecruitmentIdSuccess = true;
          this.getByRecruitmentIdErrorMessage = 'Nộp hồ sơ thành công';

          if (!this.appliedRecruitment.some((item) => item.recruitmentId === recruitmentId) && data) {
            this.appliedRecruitment.push(data);
          }
        },
        (error: Error, message: string) => {
          this.getByRecruitmentIdPending = false;
          this.getByRecruitmentIdError = true;
          this.getByRecruitmentIdSuccess = false;
          this.getByRecruitmentIdErrorMessage = message || 'Nộp hồ sơ thất bại';
        },
      );
    },
    async cancel(recruitmentId: number) {
      await httpRequestStore().authRequest<RecruitmentApply>(
        'v1/customer/recruitment-apply/cancel',
        'POST',
        {
          recruitmentId,
        },
        null,
        () => {
          this.cancelPending = true;
          this.cancelError = false;
          this.cancelSuccess = false;
          this.cancelErrorMessage = '';
        },
        (data: RecruitmentApply) => {
          this.cancelPending = false;
          this.cancelError = false;
          this.cancelSuccess = true;
          this.cancelErrorMessage = 'Hủy nộp hồ sơ thành công';

          const index = this.appliedRecruitment.findIndex((item) => item.recruitmentId === recruitmentId);
          if (index !== -1) {
            this.appliedRecruitment.splice(index, 1);
          }
        },
        (error: Error, message: string) => {
          this.cancelPending = false;
          this.cancelError = true;
          this.cancelSuccess = false;
          this.cancelErrorMessage = message || 'Hủy nộp hồ sơ thất bại';
        },
      );
    },
    isApplied(recruitmentId: number): boolean {
      return this.appliedRecruitment.some((item) => item?.recruitmentId === recruitmentId);
    },
  },
});
