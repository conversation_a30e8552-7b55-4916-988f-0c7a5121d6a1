import type { PagedResult, Recruitment } from '~/types';
import { httpRequestStore } from './httpRequest.store';

export interface IJobFilterOptions {
  keyword?: string | undefined | null;
  occupationIds?: number[];
  provinceId?: number | undefined | null;
  districtId?: number | undefined | null;
  jobSalaryId?: number | undefined | null;
  jobExperienceId?: number | undefined | null;
  jobLevelId?: number | undefined | null;
  jobDegreeId?: number | undefined | null;
  jobMethodId?: number | undefined | null;
  jobGenderId?: number | undefined | null;
  page: number;
  limit: number;
  sort: string;
}

export const defaultFilers: IJobFilterOptions = {
  keyword: undefined,
  occupationIds: [],
  provinceId: undefined,
  districtId: undefined,
  jobSalaryId: undefined,
  jobExperienceId: undefined,
  jobLevelId: undefined,
  jobDegreeId: undefined,
  jobMethodId: undefined,
  jobGenderId: undefined,
  page: 1,
  limit: 20,
  sort: 'activated_at,desc',
};

export const useJobBrowserStore = defineStore('jobBrowserStore', {
  state: () => ({
    getRecruitmentListPending: false,
    getRecruitmentListError: false,
    getRecruitmentListSuccess: false,
    getRecruitmentListMessage: null as string | null,
    total: 0,
    recruitmentList: [] as Recruitment[],
    filters: { ...defaultFilers },

    sortFiltersOptions: [
      // {
      //   value: 'priority_max,desc',
      //   name: 'Việc làm phù hợp',
      // },
      {
        value: 'activated_at,desc',
        name: 'Việc làm mới nhất',
      },
    ],
  }),
  actions: {
    async getPagedList() {
      const {
        keyword,
        occupationIds,
        provinceId,
        districtId,
        jobSalaryId,
        jobExperienceId,
        jobLevelId,
        jobDegreeId,
        jobMethodId,
        jobGenderId,
        page,
        limit,
        sort,
      } = this.filters;
      const payload = Object.fromEntries(
        Object.entries({
          keyword,
          occupationIds,
          provinceId,
          districtId,
          jobSalaryId,
          jobExperienceId,
          jobLevelId,
          jobDegreeId,
          jobMethodId,
          jobGenderId,
          page,
          limit,
          sort,
        }).filter(([_, v]) => v !== undefined && v !== null && v !== -1),
      );
      httpRequestStore().request<PagedResult<Recruitment>>(
        'v1/recruitment/get-list',
        'GET',
        null,
        payload,
        () => {
          this.getRecruitmentListPending = true;
          this.getRecruitmentListError = false;
          this.getRecruitmentListSuccess = false;
          this.getRecruitmentListMessage = null;
        },
        (data: PagedResult<Recruitment>) => {
          this.getRecruitmentListPending = false;
          this.getRecruitmentListError = false;
          this.getRecruitmentListSuccess = true;
          this.recruitmentList = data.items;
          this.total = data.total;
        },
        (error, message) => {
          this.getRecruitmentListPending = false;
          this.getRecruitmentListError = true;
          this.getRecruitmentListSuccess = false;
          this.getRecruitmentListMessage = message || 'Lỗi không xác định';
        },
      );
    },
    async setFilters(partialFilter: Partial<IJobFilterOptions>) {
      console.log('partialFilter', partialFilter);
      console.log('this.filters', this.filters);
      this.filters = Object.assign({...defaultFilers}, partialFilter);
      console.log('filters', this.filters);
    },
    async setAndFetchFilters(partialFilter: Partial<IJobFilterOptions>) {
      this.setFilters(partialFilter);
      await this.getPagedList();
    },
  },
});
