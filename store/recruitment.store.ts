import { TinyInt, type BasePagination, type OccupationStats, type PagedResult, type Recruitment } from '~/types';
import { httpRequestStore } from './httpRequest.store';

export enum EGetPagedRecruitmentSort {
  ACTIVATED_AT_DESC = 'activated_at,desc',
}

export interface GetPagedRecruitmentDto extends BasePagination {
  page: number;
  limit: number;
  keyword?: string;
  provinceId?: number;
  districtId?: number;
  jobExperienceId?: number;
  salaryRangeId?: number;
  levelId?: number;
  jobDegreeId?: number;
  jobMethodId?: number;
  jobGenderId?: number;
  minSalary?: number;
  maxSalary?: number;
  occupationIds?: number[];
  urgent?: TinyInt;
  companyIds?: number[];
  sort: EGetPagedRecruitmentSort;
}

export const useRecruitmentStore = defineStore('recruitmentStore', {
  state: () => ({
    getRecruitmentListPending: false,
    getRecruitmentListError: false,
    getRecruitmentListSuccess: false,
    getRecruitmentListMessage: null as string | null,
    items: null as Recruitment[] | null,
    total: 0,

    getOccupationStaticsPending: false,
    getOccupationStaticsError: false,
    getOccupationStaticsSuccess: false,
    getOccupationStaticsMessage: null as string | null,
    occupationStatics: null as OccupationStats[] | null,

    getDetailsPending: false,
    getDetailsError: false,
    getDetailsSuccess: false,
    getDetailsMessage: null as string | null,
    details: null as Recruitment | null,

    getUgentListAtHomePending: false,
    getUgentListAtHomeError: false,
    getUgentListAtHomeSuccess: false,
    getUgentListAtHomeMessage: null as string | null,
    urgentList: null as Recruitment[] | null,
    urgentTotal: 0,

    getNewestListAtHomePending: false,
    getNewestListAtHomeError: false,
    getNewestListAtHomeSuccess: false,
    getNewestListAtHomeMessage: null as string | null,
    newestList: null as Recruitment[] | null,
    newestTotal: 0,
  }),

  actions: {
    async getList(inputArgs: GetPagedRecruitmentDto) {
      await httpRequestStore().request<PagedResult<Recruitment>>(
        'v1/recruitment/get-list',
        'GET',
        null,
        inputArgs,
        () => {
          this.getRecruitmentListPending = true;
          this.getRecruitmentListError = false;
          this.getRecruitmentListSuccess = false;
          this.getRecruitmentListMessage = null;
        },
        (data: PagedResult<Recruitment>) => {
          this.getRecruitmentListPending = false;
          this.getRecruitmentListError = false;
          this.getRecruitmentListSuccess = true;
          this.items = data.items;
          this.total = data.total;
        },
        (error, message) => {
          this.getRecruitmentListPending = false;
          this.getRecruitmentListError = true;
          this.getRecruitmentListSuccess = false;
          this.getRecruitmentListMessage = message || 'Lỗi không xác định';
        },
      );
    },

    async getUrgentListAtHome() {
      const payload: GetPagedRecruitmentDto = {
        page: 1,
        limit: 12,
        urgent: TinyInt.Yes,
        sort: EGetPagedRecruitmentSort.ACTIVATED_AT_DESC,
      };
      await httpRequestStore().request<PagedResult<Recruitment>>(
        'v1/recruitment/get-list',
        'GET',
        null,
        payload,
        () => {
          this.getUgentListAtHomePending = true;
          this.getUgentListAtHomeError = false;
          this.getUgentListAtHomeSuccess = false;
          this.getUgentListAtHomeMessage = null;
        },
        (data: PagedResult<Recruitment>) => {
          this.getUgentListAtHomePending = false;
          this.getUgentListAtHomeError = false;
          this.getUgentListAtHomeSuccess = true;
          this.urgentList = data.items;
          this.urgentTotal = data.total;
        },
        (error, message) => {
          this.getUgentListAtHomePending = false;
          this.getUgentListAtHomeError = true;
          this.getUgentListAtHomeSuccess = false;
          this.getUgentListAtHomeMessage = message || 'Lỗi không xác định';
        },
      );
    },

    async getNewestListAtHome(page: number) {
      const payload: GetPagedRecruitmentDto = {
        page,
        limit: 12,
        sort: EGetPagedRecruitmentSort.ACTIVATED_AT_DESC,
      };
      await httpRequestStore().request<PagedResult<Recruitment>>(
        'v1/recruitment/get-list',
        'GET',
        null,
        payload,
        () => {
          this.getNewestListAtHomePending = true;
          this.getNewestListAtHomeError = false;
          this.getNewestListAtHomeSuccess = false;
          this.getNewestListAtHomeMessage = null;
        },
        (data: PagedResult<Recruitment>) => {
          this.getNewestListAtHomePending = false;
          this.getNewestListAtHomeError = false;
          this.getNewestListAtHomeSuccess = true;
          this.newestList = data.items;
          this.newestTotal = data.total;
        },
        (error, message) => {
          this.getNewestListAtHomePending = false;
          this.getNewestListAtHomeError = true;
          this.getNewestListAtHomeSuccess = false;
          this.getNewestListAtHomeMessage = message || 'Lỗi không xác định';
        },
      );
    },

    async getOccupationStatics() {
      await httpRequestStore().request<OccupationStats[]>(
        'v1/recruitment/occupation/statistics',
        'GET',
        null,
        null,
        () => {
          this.getOccupationStaticsPending = true;
          this.getOccupationStaticsError = false;
          this.getOccupationStaticsSuccess = false;
          this.getOccupationStaticsMessage = null;
        },
        (data: OccupationStats[]) => {
          this.getOccupationStaticsPending = false;
          this.getOccupationStaticsError = false;
          this.getOccupationStaticsSuccess = true;
          this.occupationStatics = data;
        },
        (error, message) => {
          this.getOccupationStaticsPending = false;
          this.getOccupationStaticsError = true;
          this.getOccupationStaticsSuccess = false;
          this.getOccupationStaticsMessage = message || 'Lỗi không xác định';
        },
      );
    },

    async getDetails(id: number) {
      await httpRequestStore().request<Recruitment>(
        `v1/recruitment/get-detail/${id}`,
        'GET',
        null,
        null,
        () => {
          this.getDetailsPending = true;
          this.getDetailsError = false;
          this.getDetailsSuccess = false;
          this.getDetailsMessage = null;
        },
        (data: Recruitment) => {
          this.getDetailsPending = false;
          this.getDetailsError = false;
          this.getDetailsSuccess = true;
          this.details = data;
        },
        (error, message) => {
          this.getDetailsPending = false;
          this.getDetailsError = true;
          this.getDetailsSuccess = false;
          this.getDetailsMessage = message || 'Lỗi không xác định';
        },
      );
    },
  },
  getters: {},
});
