import { getAccessToken } from '@/utils/authLocalstorage';
import { useAuthStore } from './auth.store';

export interface HttpResponse<T> {
  success: boolean;
  message: string;
  data: T;
}

const UNAUTHORIZED = 401;

const removeNil = (obj: Record<string, any>) => {
  const newObj: Record<string, any> = {};
  Object.keys(obj).forEach((key) => {
    if (obj[key] !== undefined && obj[key] !== null) {
      newObj[key] = obj[key];
    }
  });
  return newObj;
};

export const httpRequestStore = defineStore('httpRequest', {
  state: () => ({}),
  actions: {
    isUnauthorized(status: number) {
      return status === UNAUTHORIZED;
    },
    handleUnauthorized() {
      removeAuth();
      window.location.reload();
    },
    handleByStatus(status: number) {
      if (this.isUnauthorized(status)) {
        this.handleUnauthorized();
      }
    },
    async authRequest<T>(
      path: string,
      method: 'GET' | 'POST' | 'PUT' | 'DELETE',
      body?: Record<string, any> | null,
      params?: Record<string, any> | null,
      pendingCb?: () => void,
      successCb?: (data: T) => void,
      errorCb?: (error: any, message: string) => void,
    ): Promise<T | null | undefined> {
      pendingCb?.();
      if (!useAuthStore().authenticated) {
        errorCb?.(null, 'Unauthorized');
        return;
      }
      const baseUrl = useRuntimeConfig().public.apiUrl;
      // const domain = 'http://localhost:3333';
      const url = `${baseUrl}/${path}?${params ? new URLSearchParams(removeNil(params)).toString() : ''}`;
      try {
        const response = await fetch(url, {
          method,
          body: body ? JSON.stringify(body) : undefined,
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${getAccessToken()}`,
          },
        });

        const jsonData: HttpResponse<T> = await response?.json();
        if (response.ok) {
          if (jsonData.success) {
            successCb?.(jsonData.data);
          } else {
            errorCb?.(null, jsonData.message);
          }
        } else {
          this.handleByStatus(response.status);
          errorCb?.(jsonData, 'Server error');
        }
        return jsonData.data;
        // console.log(`jsonData`, jsonData);
        // if (response.ok) {
        //   successCb?.(jsonData);
        // } else {
        //   throw jsonData;
        // }
        // return jsonData;
      } catch (error: Error | any) {
        console.error(error);
        errorCb?.(error, error?.message);
        return null;
      }
    },
    async request<T>(
      path: string,
      method: 'GET' | 'POST' | 'PUT' | 'DELETE',
      body?: Record<string, any> | null,
      params?: Record<string, any> | null,
      pendingCb?: () => void,
      successCb?: (data: T) => void,
      errorCb?: (error: any, message: string) => void,
    ): Promise<T | null | undefined> {
      pendingCb?.();
      const baseUrl = useRuntimeConfig().public.apiUrl;
      const url = `${baseUrl}/${path}?${params ? new URLSearchParams(removeNil(params)).toString() : ''}`;
      try {
        const response = await fetch(url, {
          method,
          body: body ? JSON.stringify(body) : undefined,
          headers: {
            'Content-Type': 'application/json',
          },
        });
        // const jsonData: T = await response?.json();
        // if (response.ok) {
        //   // if (jsonData.success) {
        //   //   successCb?.(jsonData.data);
        //   // } else {
        //   //   errorCb?.(null, jsonData.message);
        //   // }
        //   successCb?.(jsonData);
        // } else {
        //   const errorMsg = jsonData?.message || 'Server error';
        //   errorCb?.(jsonData, errorMsg);
        // }
        // return jsonData;

        const jsonData: HttpResponse<T> = await response?.json();
        if (response.ok) {
          if (jsonData.success) {
            successCb?.(jsonData.data);
          } else {
            errorCb?.(null, jsonData.message);
          }
        } else {
          this.handleByStatus(response.status);
          errorCb?.(jsonData, 'Server error');
        }
        return jsonData.data;
      } catch (error: Error | any) {
        console.error(error);
        errorCb?.(error, error?.message);
        return null;
      }
    },
  },
});
