import { defineStore } from 'pinia';
import type { Customer, CustomerAuth } from '~/types';

import { getAuth, removeAuth, saveAuth } from '~/utils/authLocalstorage';
import { httpRequestStore } from './httpRequest.store';

export const useAuthStore = defineStore('auth', {
  state: () => ({
    authenticated: false,
    loading: false,
    profile: null as Customer | null,
    accessToken: null as string | null,
    authPopupOpen: false,
    loadAuthorizationSuccess: false,

    authenticateUserPending: false,
    authenticateUserSuccess: false,
    authenticateUserFailed: false,
    authenticateUserMessage: '',

    checkEmailExistPending: false,
    checkEmailExistSuccess: false,
    checkEmailExistFailed: false,
    checkEmailExistMessage: '',
    checkEmailExistData: false,

    signInWithEmailPending: false,
    signInWithEmailSuccess: false,
    signInWithEmailFailed: false,
    signInWithEmailMessage: '',

    requestForgotPasswordEmailOtpPending: false,
    requestForgotPasswordEmailOtpSuccess: false,
    requestForgotPasswordEmailOtpFailed: false,
    requestForgotPasswordEmailOtpMessage: '',

    verifyForgotPasswordEmailOtpPending: false,
    verifyForgotPasswordEmailOtpSuccess: false,
    verifyForgotPasswordEmailOtpFailed: false,
    verifyForgotPasswordEmailOtpMessage: '',

    changePasswordWithOtpPending: false,
    changePasswordWithOtpSuccess: false,
    changePasswordWithOtpFailed: false,
    changePasswordWithOtpMessage: '',

    SignUpPending: false,
    SignUpSuccess: false,
    SignUpFailed: false,
    SignUpMessage: '',
  }),
  actions: {
    async authenticateUserWithToken(accessToken: string) {
      await httpRequestStore().request<CustomerAuth>(
        'v1/auth/google-login',
        'POST',
        { accessToken },
        null,
        () => {
          this.authenticateUserPending = true;
          this.authenticateUserSuccess = false;
          this.authenticateUserFailed = false;
          this.authenticateUserMessage = '';
        },
        (data) => {
          this.authenticateUserPending = false;
          this.authenticateUserSuccess = true;
          this.authenticateUserFailed = false;
          this.authenticateUserMessage = '';
          saveAuth(data);
          this.authenticated = true;
          this.profile = data.profile;
          this.accessToken = data.accessToken;
          this.openAuthPopup(false);
        },
        (error, message) => {
          this.authenticateUserPending = false;
          this.authenticateUserSuccess = false;
          this.authenticateUserFailed = true;
          this.authenticateUserMessage = message;
          this.authenticated = false;
          this.profile = null;
          this.accessToken = null;
          removeAuth();
        },
      );
    },

    async authenticateUserWithIdToken(idToken: string) {
      await httpRequestStore().request<CustomerAuth>(
        'v1/auth/google-one-tap',
        'POST',
        { idToken },
        null,
        () => {
          this.authenticateUserPending = true;
          this.authenticateUserSuccess = false;
          this.authenticateUserFailed = false;
          this.authenticateUserMessage = '';
        },
        (data) => {
          this.authenticateUserPending = false;
          this.authenticateUserSuccess = true;
          this.authenticateUserFailed = false;
          this.authenticateUserMessage = '';
          saveAuth(data);
          this.authenticated = true;
          this.profile = data.profile;
          this.accessToken = data.accessToken;
          this.openAuthPopup(false);
        },
        (error, message) => {
          this.authenticateUserPending = false;
          this.authenticateUserSuccess = false;
          this.authenticateUserFailed = true;
          this.authenticateUserMessage = message;
          this.authenticated = false;
          this.profile = null;
          this.accessToken = null;
          removeAuth();
        },
      );
    },

    async checkEmailExist(email: string): Promise<boolean | null | undefined> {
      return await httpRequestStore().request<boolean>(
        'v1/auth/check-email-exist',
        'POST',
        {
          email,
        },
        null,
        () => {
          this.checkEmailExistPending = true;
          this.checkEmailExistSuccess = false;
          this.checkEmailExistFailed = false;
          this.checkEmailExistMessage = '';
        },
        (data) => {
          this.checkEmailExistPending = false;
          this.checkEmailExistSuccess = true;
          this.checkEmailExistFailed = false;
          this.checkEmailExistMessage = '';
          this.checkEmailExistData = data;
        },
        (error, message) => {
          this.checkEmailExistPending = false;
          this.checkEmailExistSuccess = false;
          this.checkEmailExistFailed = true;
          this.checkEmailExistMessage = message;
          removeAuth();
        },
      );
    },

    async signUp(inputArgs: {
      type: 'username' | 'phone' | 'email';
      password: string;
      email: string;
      phoneNumber: string;
      fullName: string;
    }) {
      await httpRequestStore().request<CustomerAuth>(
        'v1/auth/signup',
        'POST',
        {
          fullName: inputArgs.fullName,
          type: inputArgs.type,
          password: inputArgs.password,
          email: inputArgs.email,
          phoneNumber: inputArgs.phoneNumber,
        },
        null,
        () => {
          this.SignUpPending = true;
          this.SignUpSuccess = false;
          this.SignUpFailed = false;
          this.SignUpMessage = '';
        },
        (data) => {
          this.SignUpPending = false;
          this.SignUpSuccess = true;
          this.SignUpFailed = false;
          this.SignUpMessage = '';
        },
        (error, message) => {
          this.SignUpPending = false;
          this.SignUpSuccess = false;
          this.SignUpFailed = true;
          this.SignUpMessage = message;
        },
      );
    },

    async signIn(email: string, password: string) {
      await httpRequestStore().request<CustomerAuth>(
        'v1/auth/login',
        'POST',
        {
          email,
          password,
          type: 'email',
        },
        null,
        () => {
          this.signInWithEmailPending = true;
          this.signInWithEmailSuccess = false;
          this.signInWithEmailFailed = false;
          this.signInWithEmailMessage = '';
        },
        (data) => {
          this.signInWithEmailPending = false;
          this.signInWithEmailSuccess = true;
          this.signInWithEmailFailed = false;
          this.signInWithEmailMessage = '';
          saveAuth(data);
          this.authenticated = true;
          this.profile = data.profile;
          this.accessToken = data.accessToken;
          this.openAuthPopup(false);
        },
        (error, message) => {
          this.signInWithEmailPending = false;
          this.signInWithEmailSuccess = false;
          this.signInWithEmailFailed = true;
          this.signInWithEmailMessage = message;
          this.authenticated = false;
          this.profile = null;
          this.accessToken = null;
          removeAuth();
        },
      );
    },

    async requestForgotPasswordEmailOtp(email: string) {
      await httpRequestStore().request<CustomerAuth>(
        'v1/auth/forgot-password',
        'POST',
        {
          email,
        },
        null,
        () => {
          this.requestForgotPasswordEmailOtpPending = true;
          this.requestForgotPasswordEmailOtpSuccess = false;
          this.requestForgotPasswordEmailOtpFailed = false;
          this.requestForgotPasswordEmailOtpMessage = '';
        },
        (data) => {
          this.requestForgotPasswordEmailOtpPending = false;
          this.requestForgotPasswordEmailOtpSuccess = true;
          this.requestForgotPasswordEmailOtpFailed = false;
          this.requestForgotPasswordEmailOtpMessage = '';
        },
        (error, message) => {
          this.requestForgotPasswordEmailOtpPending = false;
          this.requestForgotPasswordEmailOtpSuccess = false;
          this.requestForgotPasswordEmailOtpFailed = true;
          this.requestForgotPasswordEmailOtpMessage = message;
        },
      );
    },

    async verifyForgotPasswordEmailOtp(email: string, otp: string) {
      await httpRequestStore().request<CustomerAuth>(
        'v1/auth/forgot-password-verify-otp',
        'POST',
        {
          email,
          otp,
        },
        null,
        () => {
          this.verifyForgotPasswordEmailOtpPending = true;
          this.verifyForgotPasswordEmailOtpSuccess = false;
          this.verifyForgotPasswordEmailOtpFailed = false;
          this.verifyForgotPasswordEmailOtpMessage = '';
        },
        (data) => {
          this.verifyForgotPasswordEmailOtpPending = false;
          this.verifyForgotPasswordEmailOtpSuccess = true;
          this.verifyForgotPasswordEmailOtpFailed = false;
          this.verifyForgotPasswordEmailOtpMessage = '';
        },
        (error, message) => {
          this.verifyForgotPasswordEmailOtpPending = false;
          this.verifyForgotPasswordEmailOtpSuccess = false;
          this.verifyForgotPasswordEmailOtpFailed = true;
          this.verifyForgotPasswordEmailOtpMessage = message;
        },
      );
    },

    async changePasswordWithOtp(email: string, otp: string, password: string) {
      await httpRequestStore().request<CustomerAuth>(
        'v1/auth/otp-change-password',
        'POST',
        {
          email,
          otp,
          password,
        },
        null,
        () => {
          this.changePasswordWithOtpPending = true;
          this.changePasswordWithOtpSuccess = false;
          this.changePasswordWithOtpFailed = false;
          this.changePasswordWithOtpMessage = '';
        },
        (data) => {
          this.changePasswordWithOtpPending = false;
          this.changePasswordWithOtpSuccess = true;
          this.changePasswordWithOtpFailed = false;
          this.changePasswordWithOtpMessage = '';
        },
        (error, message) => {
          this.changePasswordWithOtpPending = false;
          this.changePasswordWithOtpSuccess = false;
          this.changePasswordWithOtpFailed = true;
          this.changePasswordWithOtpMessage = message;
        },
      );
    },

    logUserOut() {
      removeAuth(); // remove user data from local storage
      this.authenticated = false; // set authenticated  state value to false
      window.location.reload();
    },

    loadFromLocalStorage() {
      const auth = getAuth(); // get user data from local storage
      if (auth) {
        this.authenticated = true; // set authenticated state value to true
        this.profile = auth.profile; // set profile state value to user data
      } else {
        this.authenticated = false; // set authenticated state value to false
        this.profile = null; // set profile state value to null
      }
      this.loadAuthorizationSuccess = true;
    },

    openAuthPopup(open: boolean) {
      console.log('openAuthPopup', open, this.authenticated);
      if (open && !this.authenticated) {
        this.authPopupOpen = true;
      } else {
        this.authPopupOpen = false;
      }
    },

    isAuthenticated() {
      const auth = getAuth();
      return auth !== null;
    },
  },
});
