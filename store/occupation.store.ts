import type {
  CanBeNil,
  CommonData,
  EmployeeSize,
  JobDegree,
  JobExperience,
  JobGender,
  JobLevel,
  JobMethod,
  JobSalary,
  Language,
  LanguageLevel,
  Occupation,
  Province,
} from '~/types';
import { httpRequestStore } from './httpRequest.store';

export const useOccupationStore = defineStore('occupationStore', {
  state: () => ({
    getOccupationDetailPending: false,
    getOccupationDetailError: false,
    getOccupationDetailSuccess: false,
    getOccupationDetailMessage: null as CanBeNil<string>,
    occupation: null as Occupation | null,
  }),
  actions: {
    async getOccupationDetail(id: number) {
      await httpRequestStore().request<Occupation>(
        `v1/occupation/${id}`,
        'GET',
        null,
        null,
        () => {
          this.getOccupationDetailPending = true;
          this.getOccupationDetailError = false;
          this.getOccupationDetailSuccess = false;
          this.getOccupationDetailMessage = null;
        },
        (data: Occupation) => {
          this.getOccupationDetailPending = false;
          this.getOccupationDetailError = false;
          this.getOccupationDetailSuccess = true;
          this.occupation = data;
        },
        (error, message) => {
          this.getOccupationDetailPending = false;
          this.getOccupationDetailError = true;
          this.getOccupationDetailSuccess = false;
          this.getOccupationDetailMessage = message || 'Lỗi không xác định';
        },
      );
    },
  },
});
