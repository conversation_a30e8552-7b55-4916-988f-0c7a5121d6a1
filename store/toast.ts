export enum ToastType {
  Success = "success",
  Error = "error",
  Warning = "warning",
  Info = "info",
}

interface IToast {
  id: string;
  message: string;
  type: ToastType;
}

export const useToast = defineStore("toast", {
  state: () => ({
    items: [] as IToast[],
  }),
  actions: {
    showToast(message: string, type: ToastType) {
      const id = new Date().valueOf().toString();
      this.items.push({
        id,
        message,
        type,
      });
      setTimeout(() => {
        this.removeToastByIndex(id);
      }, 3000);
    },

    success(message: string) {
      this.showToast(message, ToastType.Success);
    },

    error(message: string) {
      this.showToast(message, ToastType.Error);
    },

    warning(message: string) {
      this.showToast(message, ToastType.Warning);
    },

    removeToastByIndex(id: string) {
      const index = this.items.findIndex((item) => item.id === id);
      if (index !== -1) {
        this.items.splice(index, 1);
      }
    },
  },
});
