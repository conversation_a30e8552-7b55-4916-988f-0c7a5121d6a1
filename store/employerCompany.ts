import type { CanBeNil, Company } from '~/types';
import { httpRequestStore } from './httpRequest.store';

export const useEmployerCompanyStore = defineStore('useCompanyStore', {
  state: () => ({
    getProfilePending: false,
    getProfileError: false,
    getProfileSuccess: false,
    getProfileMessage: null as string | null,
    profile: null as Company | null,

    updatePending: false,
    updateError: false,
    updateSuccess: false,
    updateMessage: null as string | null,

    createPending: false,
    createError: false,
    createSuccess: false,
    createMessage: null as string | null,

    updateBgPending: false,
    updateBgError: false,
    updateBgSuccess: false,
    updateBgMessage: null as string | null,
  }),

  actions: {
    async getMyCompany() {
      await httpRequestStore().authRequest<Company>(
        'v1/companies/me/get',
        'GET',
        null,
        null,
        () => {
          this.getProfilePending = true;
          this.getProfileError = false;
          this.getProfileSuccess = false;
          this.getProfileMessage = null;
        },
        (response: Company) => {
          this.profile = response;
          this.getProfilePending = false;
          this.getProfileError = false;
          this.getProfileSuccess = true;
          this.getProfileMessage = 'Lấy thông tin công ty thành công';
        },
        (error: any, message: string) => {
          this.getProfilePending = false;
          this.getProfileError = true;
          this.getProfileSuccess = false;
          this.getProfileMessage = message || 'Lấy thông tin công ty thất bại';
        },
      );
    },
    async update(body: Partial<Company>): Promise<CanBeNil<Company>> {
      return await httpRequestStore().authRequest<Company>(
        'v1/companies/me/update',
        'POST',
        body,
        null,
        () => {
          this.updateMessage = null;
          this.updatePending = true;
          this.updateError = false;
          this.updateSuccess = false;
        },
        (response: Company) => {
          this.profile = response;
          this.updateMessage = 'Cập nhật thông tin công ty thành công';
          this.updatePending = false;
          this.updateError = false;
          this.updateSuccess = true;
        },
        (error: any, message: string) => {
          this.updateMessage = message || 'Cập nhật thông tin công ty thất bại';
          this.updatePending = false;
          this.updateError = true;
          this.updateSuccess = false;
        },
      );
    },
    async updateBgImgCompany(bgImage: string) {
      await httpRequestStore().authRequest<Company>(
        'v1/companies/me/update',
        'POST',
        { bgImage },
        null,
        () => {
          this.updateBgMessage = null;
          this.updateBgPending = true;
          this.updateBgError = false;
          this.updateBgSuccess = false;
        },
        (response: Company) => {
          this.profile = response;
          this.updateBgMessage = 'Cập nhật thông tin công ty thành công';
          this.updateBgPending = false;
          this.updateBgError = false;
          this.updateBgSuccess = true;
        },
        (error: any, message: string) => {
          this.updateBgMessage = message || 'Cập nhật thông tin công ty thất bại';
          this.updateBgPending = true;
          this.updateBgError = false;
          this.updateBgSuccess = false;
        },
      );
    },
    async create(body: Partial<Company>) {
      httpRequestStore().authRequest<Company>(
        'v1/companies/me/create',
        'POST',
        body,
        null,
        () => {
          this.createMessage = null;
          this.createPending = true;
          this.createError = false;
          this.createSuccess = false;
        },
        (response: Company) => {
          this.profile = response;
          this.createMessage = 'Tạo thông tin công ty thành công';
          this.createPending = false;
          this.createError = false;
          this.createSuccess = true;
        },
        (error: any, message: string) => {
          this.createMessage = message || 'Tạo thông tin công ty thất bại';
          this.createPending = false;
          this.createError = true;
          this.createSuccess = false;
        },
      );
    },
  },
});
