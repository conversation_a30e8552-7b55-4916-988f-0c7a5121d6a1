import type { Customer } from '~/types';
import { useAuthStore } from './auth.store';
import { ToastType, useToast } from './toast';
import { httpRequestStore } from './httpRequest.store';

export interface UpdateProfileDto {
  email: string | null;
  phoneNumber: string | null;
  fullName: string | null;
  birthday: string | null;
  provinceId: number | null;
  address: string | null;
  avatar: string | null;
  gender: string | null;
  maritalStatus: number | null;
}

export const useProfileStore = defineStore('profile', {
  state: () => ({
    loading: false,
    profile: null as Customer | null,

    getProfilePending: false,
    getProfileError: false,
    getProfileSuccess: false,
    getProfileMessage: null as string | null,

    updatePending: false,
    updateError: false,
    updateSuccess: false,
    updateMessage: null as string | null,

    sendOtpToVerifyEmailPending: false,
    sendOtpToVerifyEmailError: false,
    sendOtpToVerifyEmailSuccess: false,
    sendOtpToVerifyEmailMessage: null as string | null,

    verifyEmailPending: false,
    verifyEmailError: false,
    verifyEmailSuccess: false,
    verifyEmailMessage: null as string | null,

    updatePasswordPending: false,
    updatePasswordError: false,
    updatePasswordSuccess: false,
    updatePasswordMessage: null as string | null,
  }),

  actions: {
    async getProfile() {
      await httpRequestStore().authRequest<Customer>(
        'v1/users/profile',
        'GET',
        null,
        null,
        () => {
          this.getProfilePending = true;
          this.getProfileError = false;
          this.getProfileSuccess = false;
          this.getProfileMessage = null as string | null;
        },
        (data: Customer) => {
          this.getProfilePending = false;
          this.getProfileError = false;
          this.getProfileSuccess = true;
          this.getProfileMessage = null as string | null;
          this.profile = data;
        },
        (error, message) => {
          this.getProfilePending = false;
          this.getProfileError = true;
          this.getProfileSuccess = false;
          this.getProfileMessage = message ?? 'Lỗi không xác định';
        },
      );
    },

    async updateProfile(data: Partial<UpdateProfileDto>) {
      await httpRequestStore().authRequest<Customer>(
        'v1/users/profile',
        'PUT',
        data,
        null,
        () => {
          this.updatePending = true;
          this.updateError = false;
          this.updateSuccess = false;
          this.updateMessage = null as string | null;
        },
        (data: Customer) => {
          this.updatePending = false;
          this.updateError = false;
          this.updateSuccess = true;
          this.updateMessage = null as string | null;
          this.profile = data;
          useToast().showToast('Cập nhật thông tin thành công', ToastType.Success);
        },
        (error, message) => {
          this.updatePending = false;
          this.updateError = true;
          this.updateSuccess = false;
          this.updateMessage = message ?? 'Lỗi không xác định';
          useToast().showToast(this.updateMessage || 'Cập nhật thất bại', ToastType.Error);
        },
      );
    },

    async verifyEmail(data: { otp: string }) {
      await httpRequestStore().authRequest<boolean>(
        'v1/auth/verify-email-by-otp',
        'POST',
        data,
        null,
        () => {
          this.verifyEmailPending = true;
          this.verifyEmailError = false;
          this.verifyEmailSuccess = false;
          this.verifyEmailMessage = null as string | null;
        },
        (data: boolean) => {
          this.verifyEmailPending = false;
          this.verifyEmailError = false;
          this.verifyEmailSuccess = true;
          this.verifyEmailMessage = null as string | null;
          // useToast().showToast('Xác thực email thành công!', ToastType.Success);
        },
        (error, message) => {
          this.verifyEmailPending = false;
          this.verifyEmailError = true;
          this.verifyEmailSuccess = false;
          this.verifyEmailMessage = message ?? 'Lỗi không xác định';
          // useToast().showToast(this.verifyEmailMessage || 'Cập nhật thất bại', ToastType.Error);
        },
      );
    },

    async updatePassword(data: { oldPassword: string; newPassword: string }) {
      await httpRequestStore().authRequest<boolean>(
        'v1/auth/change-password',
        'POST',
        data,
        null,
        () => {
          this.updatePasswordPending = true;
          this.updatePasswordError = false;
          this.updatePasswordSuccess = false;
          this.updatePasswordMessage = null as string | null;
        },
        (data: boolean) => {
          this.updatePasswordPending = false;
          this.updatePasswordError = false;
          this.updatePasswordSuccess = true;
          this.updatePasswordMessage = null as string | null;
          console.log('data', data);
          useToast().showToast('Đổi mật khẩu thành công!', ToastType.Success);
        },
        (error, message) => {
          this.updatePasswordPending = false;
          this.updatePasswordError = true;
          this.updatePasswordSuccess = false;
          this.updatePasswordMessage = message ?? 'Lỗi không xác định';
          useToast().showToast(this.updatePasswordMessage || 'Đổi mật khẩu thất bại', ToastType.Error);
        },
      );
    },

    async sendOtpToVerifyEmail() {
      await httpRequestStore().authRequest<boolean>(
        'v1/auth/send-otp-to-verify-email',
        'POST',
        null,
        null,
        () => {
          this.sendOtpToVerifyEmailPending = true;
          this.sendOtpToVerifyEmailError = false;
          this.sendOtpToVerifyEmailSuccess = false;
          this.sendOtpToVerifyEmailMessage = null as string | null;
        },
        (data: boolean) => {
          this.sendOtpToVerifyEmailPending = false;
          this.sendOtpToVerifyEmailError = false;
          this.sendOtpToVerifyEmailSuccess = true;
          this.sendOtpToVerifyEmailMessage = null as string | null;
          // useToast().showToast('Gửi mã OTP thành công', ToastType.Success);
        },
        (error, message) => {
          this.sendOtpToVerifyEmailPending = false;
          this.sendOtpToVerifyEmailError = true;
          this.sendOtpToVerifyEmailSuccess = false;
          this.sendOtpToVerifyEmailMessage = message ?? 'Lỗi không xác định';
          // useToast().showToast(this.sendOtpToVerifyEmailMessage || 'Gửi mã OTP thất bại', ToastType.Error);
        },
      );
    },
  },
});
