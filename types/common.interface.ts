import type { Occupation } from "./occupation.interface";

export interface CommonData {
  jobDegrees: JobDegree[];
  jobGenders: JobGender[];
  jobExperiences: JobExperience[];
  jobLevels: JobLevel[];
  jobMethods: JobMethod[];
  jobSalarys: JobSalary[];
  occupations: Occupation[];
  provinces: Province[];
  districts: Province[];
  languages: Language[];
  employeeSizes: EmployeeSize[];
  languageLevels: LanguageLevel[];
  topRecruitmentByOccupation: {
    occupationId: number;
    count: number;
  }[];
}

export class EmployeeSize {
  id!: number;
  slug!: string;
  name!: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface JobDegree {
  id: number;
  code: string;
  name: string;
}

export interface JobGender {
  id: number;
  code: string;
  name: string;
}

export interface JobExperience {
  id: number;
  name: string;
}

export interface JobLevel {
  id: number;
  code: string;
  name: string;
}

export interface JobMethod {
  id: number;
  code: string;
  name: string;
}

export interface JobSalary {
  id: number;
  code: string;
  name: string;
  min: any;
  max: any;
}

export interface Province {
  id: number;
  name: string;
  level: number;
  parentId: any;
}

export interface Language {
  id: number;
  name: string;
}
export interface LanguageLevel {
  id: number;
  code: string;
  name: string;
}

export enum TinyInt {
  Yes = 1,
  No = 0,
}

export interface FormRule {
  required?: boolean;
  message?: string;
  trigger?: string;
}

export type IsNil = null | undefined;
export type CanBeNil<T> = T | IsNil;