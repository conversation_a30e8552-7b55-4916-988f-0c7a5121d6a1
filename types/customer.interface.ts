import type { TinyInt } from "./common.interface";

export interface Customer {
  id: number;
  createdAt: string;
  updatedAt: string;
  firstName: string;
  lastName: string;
  fullName: any;
  avatar: string;
  username: string;
  emailAddress: string;
  verifyEmail: number;
  phoneNumber: string;
  verifyPhoneNumber: number;
  birthday: string;
  address: string;
  avatarId: number;
  gender: Gender;
  maritalStatus: TinyInt;
  userId: number;
  provinceId: number;
}

export enum Gender {
  male = "male",
  female = "female",
}
