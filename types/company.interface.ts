import type { Province, TinyInt } from './common.interface';
import type { Occupation } from './occupation.interface';
import type { Recruitment } from "./recruitment.interface";

export interface Company {
  id: number;
  slug: string | null;
  userId: any;
  logo: string | null;
  bgImage: string | null;
  isBot: TinyInt;
  name: string | null;
  overview: string | null;
  website: string | null;
  fanPageFacebookLink: string | null;
  type: string | null;
  // occupationId: number;
  employeeSizeId: number | null;
  provinceId: number | null;
  address: string | null;
  totalFollowers: number;
  latitude: string | null;
  longitude: string | null;
  createdAt: string | null;
  updatedAt: string | null;
  phone: string | null;
  recruitmentItems: Recruitment[];
  occupations: Occupation[];
  province: Province;
}
