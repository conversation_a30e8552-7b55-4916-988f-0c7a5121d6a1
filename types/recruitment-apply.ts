import type { Customer } from "./customer.interface";
import type { Recruitment } from "./recruitment.interface";
import type { Resume } from "./resume.interface";

export enum EnumRecruitmentApplyStatus {
  PENDING = "pending",
  SUITABLE = "suitable", // VI: <PERSON><PERSON> hợp
  INTERVIEWING = "interviewing",
  APPROVED = "approved",
  REJECTED = "rejected",
}

export interface RecruitmentApply {
  id: number;
  customerId: number;
  recruitmentId: number;
  appliedAt: string;
  status: EnumRecruitmentApplyStatus;
  resumeId: number;
  recruitment?: Recruitment;
  customer?: Customer;
  resume?: Resume;
  updatedAt: string;
  createdAt: string;
}