import type { TinyInt } from './common.interface';
import type { Customer } from './customer.interface';

export interface Resume {
  id: number;
  title: string;
  type: ResumeType;
  expectPosition: string;
  occupationIds: number[];
  totalView: number;
  cvFileUrl: string | null;
  currentLevelId: number | null;
  expectLevelId: number | null;
  expectSalary: number | null;
  experienceId: number | null;
  isSearchAllowed: TinyInt;
  provinceIds: number[];
  typeOfEmploymentId: number | null;
  softSkills: string[];
  educationId: number | null;
  careerObjective: string | null;
  customerId: number | null;
  status: ResumeStatus;
  generalInfoCompleted: TinyInt;
  experienceCompleted: TinyInt;
  educationCompleted: TinyInt;
  techsCompleted: TinyInt;
  languagesCompleted: TinyInt;
  cvFileCompleted: TinyInt;
  completed: TinyInt;
  createdAt: string;
  updatedAt: string;
  academies?: ResumeAcademy[];
  techs?: ResumeTech[];
  refs?: ResumeRef[];
  customer?: Customer;
  languages?: ResumeForeignLanguage[];
  experiences?: ResumeExperience[];
}

export enum ResumeStatus {
  active = 1,
  inactive = 0,
}

export enum ResumeType {
  online = 1,
  offline = 2,
}

export interface ResumeAcademy {
  id: number;
  schoolName: string;
  specialize: string;
  startDate: string;
  endDate: string;
  degree: string;
  resumeId: number;
}

export interface ResumeTech {
  id: number;
  skills: string[];
  resumeId: number;
}

export interface ResumeRef {
  id: number;
  fullName: string;
  phone: string;
  companyName: string;
  position: string;
  resumeId: number;
}

export interface ResumeForeignLanguage {
  id: number;
  languageId: number;
  // proficiencyId: number;
  resumeId: number;
  levelId: number;
}

export const languageLevels = [
  {
    id: 1,
    name: 'Sơ cấp',
  },
  {
    id: 2,
    name: 'Trung cấp',
  },
  {
    id: 3,
    name: 'Cao cấp',
  },
];

export interface ResumeExperience {
  id: number;
  title: string;
  companyName: string;
  position: string;
  startDate: string;
  endDate: string | null;
  description: string;
  isCurrent: number;
  resumeId: number;
}
