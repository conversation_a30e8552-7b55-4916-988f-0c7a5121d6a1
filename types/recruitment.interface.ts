import type { TinyInt } from './common.interface';
import type { Company } from "./company.interface";
import type { Customer } from './customer.interface';
import type { Occupation } from "./occupation.interface";
import type { Workspace } from "./workspace.interface";

export enum ERecruitmentStatus {
  // đang duyệt
  PENDING = 'pending',
  // đã duyệt
  APPROVED = 'approved',
  // từ chối
  REJECTED = 'rejected',
  // draft - meanning that the recruitment is not published yet
  DRAFT = 'draft',
}

export interface Recruitment {
  id: number;
  slug: string;
  logo: string; // for bot
  companyName: string; // for bot
  companyId: number;
  companyAddressId: any;
  title: string;
  descriptionHtml: string;
  requirementHtml: string;
  whyWorkingHereHtml: string;
  benefitHtml: string;
  workingDayFrom: number;
  workingDayTo: number;
  workingTimeFrom: string;
  workingTimeTo: string;
  salaryCurrency: string;
  jobDegreeId: number;
  jobExperienceId: number;
  jobGenderId: number;
  jobLevelId: number;
  jobMethodId: number;
  jobSalaryId: number;
  totalViews: number;
  minSalary: number;
  maxSalary: number;
  salaryNegotiable: TinyInt;
  probationDuration: number;
  vacancyQuantity: number;
  applyExpiredAt: any;
  contactName: string;
  contactPhone: string;
  contactPhones: string[];
  contactAddress: string;
  contactEmail: string;
  minAge: number;
  maxAge: number;
  urgent: any;
  status: ERecruitmentStatus;
  isVerified: TinyInt;
  isPublished: TinyInt;
  publishedAt: string;
  skills: string[];
  createdAt: string;
  updatedAt: string;
  company?: Company;
  workspaces?: Workspace[];
  occupations?: Occupation[];
  customer?: Customer;
}
