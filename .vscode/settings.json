{
  "prettier.singleAttributePerLine": true,
  "html.format.wrapLineLength": 80,
  "editor.wordWrap": "on",
  "editor.fontLigatures": true,
  "files.exclude": {
    "**/.git": true,
    "**/.svn": true,
    "**/.hg": true,
    "**/.DS_Store": true,
    "**/Thumbs.db": true,
    "**/node_modules": true
  },
  // "editor.defaultFormatter": "esbenp.prettier-vscode",
  // "editor.formatOnSave": true,
  // "[vue]": {
  //   "editor.defaultFormatter": "esbenp.prettier-vscode"
  // },
  // "prettier.requireConfig": true
}