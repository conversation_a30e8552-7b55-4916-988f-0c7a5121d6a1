import tailwindcss from '@tailwindcss/vite';

// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  devtools: { enabled: true },
  extends: [],

  modules: [
    '@pinia/nuxt',
    '@nuxt/fonts',
    '@nuxtjs/device',
    '@nuxt/eslint',
    '@vee-validate/nuxt',
    '@nuxtjs/sitemap',
    'nuxt-gtag',
    '@nuxtjs/seo',
  ],

  app: {
    head: {
      title: 'Việc làm Lâm Đồng - Tìm việc tại <PERSON>, <PERSON>hu v<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>',
      htmlAttrs: {
        lang: 'vi',
      },
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no' },
        { name: 'format-detection', content: 'telephone=no' },
        {
          name: 'description',
          content:
            'T<PERSON><PERSON> vi<PERSON>, tuyển dụng việc làm mới mỗi ngày tại việc làm Lâm Đồng với hơn 10,000 công ty tuyển dụng theo Ngành nghề / Cấp bậc / Mức lương / Khu vực địa lý / Loại hợp đồng / Kinh nghiệm / Trình độ / Giới tính ...',
        },
        {
          name: 'keywords',
          content:
            'việc làm Lâm Đồng, tuyển dụng Lâm Đồng, việc làm Bảo Lộc, tuyển dụng Bảo Lộc, việc làm Đà Lạt, tuyển dụng Đà Lạt, việc làm Đức Trọng, tuyển dụng Đức Trọng, việc làm Di Linh, tuyển dụng Di Linh, việc làm Bảo Lâm, tuyển dụng Bảo Lâm, tìm việc làm Lâm Đồng, việc làm mới nhất Lâm Đồng, tuyển nhân sự Lâm Đồng, việc làm thời vụ Lâm Đồng, việc làm full-time Đà Lạt, tuyển gấp Lâm Đồng, tìm việc nhanh Bảo Lộc',
        },
      ],
      link: [
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
        { rel: 'manifest', href: '/manifest.json' },
        {
          rel: 'icon',
          type: 'image/png',
          sizes: '32x32',
          href: '/images/logo/favicon-32x32.png',
        },
        {
          rel: 'icon',
          type: 'image/png',
          sizes: '96x96',
          href: '/images/logo/favicon-96x96.png',
        },
        {
          rel: 'icon',
          type: 'image/png',
          sizes: '16x16',
          href: '/images/logo/favicon-16x16.png',
        },
        {
          rel: 'apple-touch-icon',
          sizes: '57x57',
          href: '/images/logo/apple-icon-57x57.png',
        },
        {
          rel: 'apple-touch-icon',
          sizes: '60x60',
          href: '/images/logo/apple-icon-60x60.png',
        },
        {
          rel: 'apple-touch-icon',
          sizes: '72x72',
          href: '/images/logo/apple-icon-72x72.png',
        },
        {
          rel: 'apple-touch-icon',
          sizes: '76x76',
          href: '/images/logo/apple-icon-76x76.png',
        },
        {
          rel: 'apple-touch-icon',
          sizes: '114x114',
          href: '/images/logo/apple-icon-114x114.png',
        },
        {
          rel: 'apple-touch-icon',
          sizes: '120x120',
          href: '/images/logo/apple-icon-120x120.png',
        },
        {
          rel: 'apple-touch-icon',
          sizes: '144x144',
          href: '/images/logo/apple-icon-144x144.png',
        },
        {
          rel: 'apple-touch-icon',
          sizes: '152x152',
          href: '/images/logo/apple-icon-152x152.png',
        },
        {
          rel: 'apple-touch-icon',
          sizes: '180x180',
          href: '/images/logo/apple-icon-180x180.png',
        },
        {
          rel: 'icon',
          type: 'image/png',
          sizes: '192x192',
          href: '/images/logo/android-icon-192x192.png',
        },
      ],
    },
  },

  build: {
    // transpile: ['@fortawesome/vue-fontawesome'],
  },

  // googleSignIn: {
  //   // clientId: '537192556570-bi669coue40log7d165uhp560aut0e23.apps.googleusercontent.com',
  //   clientId: process.env.NUXT_PUBLIC_GOOGLE_CLIENT_ID,
  // },

  fonts: {
    families: [
      {
        name: 'Lexend',
        provider: 'google',
      },
    ],
  },

  vite: {
    plugins: [tailwindcss()],
  },

  css: [
    // '@fortawesome/fontawesome-svg-core/styles.css',
    '~/assets/css/app.css',
    '~/assets/css/main.css',
  ],

  // postcss: {
  //   plugins: {
  //     tailwindcss: {},
  //     autoprefixer: {},
  //   },
  // },

  site: {
    url: 'https://vieclamlamdong.site',
    name: 'Việc làm Lâm Đồng',
    description: 'Việc làm Lâm Đồng - Tìm việc tại Lâm Đồng, khu vực Bảo Lộc, Đà Lạt, Đức Trọng, Di Linh, Bảo Lâm',
    image: '/images/horizontal-logo.png',
    locale: 'vi_VN',
  },
  sitemap: {
    xsl: false,
    cacheMaxAgeSeconds: 1000 * 60 * 60, // 1 hour
    sitemapName: 'sitemap-index.xml',
    // Define multiple sitemaps
    sitemaps: {
      static: {
        sources: ['/api/sitemap-static'],
      },
      occupations: {
        sources: ['/api/sitemap-occupations'],
      },
      provinces: {
        sources: ['/api/sitemap-provinces'],
      },
      districts: {
        sources: ['/api/sitemap-districts'],
      },
      jobs: {
        sources: ['/api/sitemap-jobs'],
        // Jobs change frequently, so we set a higher priority
        defaults: {
          changefreq: 'daily',
          priority: 0.8,
        },
      },
    },
    // Define the URLs that should be excluded from the sitemap
    exclude: ['/employer/**', '/ho-so-cua-ban/**', '/viec-lam-da-luu', '/viec-lam-da-ung-tuyen'],
    // Define the default configuration for all URLs
    defaults: {
      changefreq: 'daily',
      priority: 0.7,
      lastmod: new Date(),
    },
  },

  runtimeConfig: {
    public: {
      apiUrl: process.env.NUXT_PUBLIC_API_URL || 'http://localhost:3001/api',
      googleClientId: process.env.NUXT_PUBLIC_GOOGLE_CLIENT_ID,
    },
  },
  gtag: {
    id: process.env.NUXT_PUBLIC_GTAG_ID,
  },

  ssr: true,

  plugins: [
    '~/plugins/quillEditor.client.ts',
    '~/plugins/googleSignin.client.ts',
    '~/plugins/googleOneTap.client.ts'
  ],

  compatibilityDate: '2024-10-10',
});
