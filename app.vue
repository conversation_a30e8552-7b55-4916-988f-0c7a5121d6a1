<template>
  <div class="bg-primary/2.5 font-[Lexend]">
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>
    <Message />
    <LoginModal :show="useAuth.authPopupOpen" />
  </div>
</template>
<script setup lang="ts">
import { useAuthStore } from './store/auth.store';
import { useCommonStore } from './store/common.store';
import { useProfileStore } from './store/profile';
import Message from './components/common/Message.vue';
import LoginModal from './components/auth/LoginModal.vue';

const { loadFromLocalStorage } = useAuthStore();
const { getCommonData } = useCommonStore();
const { getProfile } = useProfileStore();
const useAuth = useAuthStore();
onMounted(() => {
  loadFromLocalStorage();
  getCommonData();
  getProfile();
});
</script>
