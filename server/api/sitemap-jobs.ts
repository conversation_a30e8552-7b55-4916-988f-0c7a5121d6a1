import { define<PERSON>vent<PERSON>and<PERSON> } from 'h3';

// Define the sitemap entry type
interface SitemapEntry {
  loc: string;
  lastmod?: string;
  changefreq?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority?: number;
}

export default defineEventHandler(async () => {
  const config = useRuntimeConfig();
  const baseUrl = config.public.apiUrl;
  const siteUrl = 'https://vieclamlamdong.site';

  try {
    const jobUrls: SitemapEntry[] = [];
    let currentPage = 1;
    const pageSize = 100; // Fetch 100 jobs per request
    let hasMorePages = true;

    // Fetch all pages of job listings
    while (hasMorePages) {
      // For GET requests, parameters should be in the URL query string, not in the body
      const queryParams = new URLSearchParams({
        page: currentPage.toString(),
        limit: pageSize.toString(),
        sort: 'activated_at,desc'
      });

      const jobsResponse = await fetch(`${baseUrl}/v1/recruitment/get-list?${queryParams}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      const jobsData = await jobsResponse.json();

      // Process the current page of jobs
      if (jobsData && jobsData.success && jobsData.data) {
        // If the data is in data.items (paginated response)
        if (jobsData.data.items && Array.isArray(jobsData.data.items)) {
          // Add jobs to our URLs array
          jobsData.data.items.forEach((job: any) => {
            jobUrls.push({
              loc: `${siteUrl}/cong-viec/${job.slug}id${job.id}`,
              lastmod: job.updatedAt || job.createdAt || new Date().toISOString(),
              changefreq: 'daily',
              priority: 0.8,
            });
          });

          // Check if we've reached the last page
          if (jobsData.data.items.length < pageSize) {
            hasMorePages = false;
          } else {
            currentPage++;
          }
        } else {
          // If we don't get the expected data structure, stop pagination
          hasMorePages = false;
        }
      } else {
        // If the request failed or returned unexpected data, stop pagination
        hasMorePages = false;
      }
    }

    return jobUrls;
  } catch (error) {
    console.error('Error generating job sitemap URLs:', error);
    return [];
  }
});
