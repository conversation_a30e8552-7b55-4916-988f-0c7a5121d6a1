import { defineEventHandler } from 'h3';

/**
 * This server route is used to generate dynamic URLs for the sitemap
 * It fetches job listings and other dynamic content from your API
 * and returns them in a format that can be used by the sitemap module
 */
export default defineEventHandler(async (event) => {
  const config = useRuntimeConfig();
  const baseUrl = config.public.apiUrl;
  const siteUrl = 'https://vieclamlamdong.site';

  function removeUnicode(str: string): string {
    if (!str) return str;
    str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, 'a');
    str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, 'e');
    str = str.replace(/ì|í|ị|ỉ|ĩ/g, 'i');
    str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, 'o');
    str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, 'u');
    str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, 'y');
    str = str.replace(/đ/g, 'd');
    str = str.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/g, 'A');
    str = str.replace(/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/g, 'E');
    str = str.replace(/Ì|Í|Ị|Ỉ|Ĩ/g, 'I');
    str = str.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/g, 'O');
    str = str.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/g, 'U');
    str = str.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/g, 'Y');
    str = str.replace(/Đ/g, 'D');
    return str;
  }

  try {
    // Create sitemap index structure
    const today = new Date().toISOString().split('T')[0];
    
    // Main sitemap for static pages
    const staticUrls = [
      {
        loc: `${siteUrl}/sitemap-static.xml`,
        lastmod: today,
      },
      {
        loc: `${siteUrl}/sitemap-occupations.xml`,
        lastmod: today,
      },
      {
        loc: `${siteUrl}/sitemap-provinces.xml`,
        lastmod: today,
      },
      {
        loc: `${siteUrl}/sitemap-districts.xml`,
        lastmod: today,
      }
    ];
    
    return staticUrls;
  } catch (error) {
    console.error('Error generating sitemap URLs:', error);
    return [];
  }
});
