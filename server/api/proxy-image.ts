import { defineEvent<PERSON><PERSON><PERSON>, readBody, createError } from 'h3';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { url } = body;

    if (!url) {
      throw createError({
        statusCode: 400,
        statusMessage: 'URL is required',
      });
    }

    // Fetch the image from the provided URL
    const response = await fetch(url);
    
    if (!response.ok) {
      throw createError({
        statusCode: response.status,
        statusMessage: `Failed to fetch image: ${response.statusText}`,
      });
    }

    // Get the image as an array buffer
    const arrayBuffer = await response.arrayBuffer();
    
    // Get the content type
    const contentType = response.headers.get('content-type') || 'image/jpeg';
    
    // Convert to base64
    const base64 = Buffer.from(arrayBuffer).toString('base64');
    
    // Return the image as a data URL
    return {
      dataUrl: `data:${contentType};base64,${base64}`,
      contentType,
    };
  } catch (error: any) {
    console.error('Proxy image error:', error);
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.message || 'Failed to proxy image',
    });
  }
});
