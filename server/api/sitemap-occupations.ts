import { defineEventHandler } from 'h3';

export default defineEventHandler(async (event) => {
  const config = useRuntimeConfig();
  const baseUrl = config.public.apiUrl;
  const siteUrl = 'https://vieclamlamdong.site';

  try {
    // Fetch occupations
    const occupationsResponse = await fetch(`${baseUrl}/v1/occupations`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const occupationsData = await occupationsResponse.json();

    const occupationUrls =
      occupationsData.data?.map((occupation: any) => ({
        loc: `${siteUrl}/viec-lam-${occupation.code}-o${occupation.id}`,
        changefreq: 'weekly',
        priority: 0.6,
      })) || [];

    return occupationUrls;
  } catch (error) {
    console.error('Error generating occupation sitemap URLs:', error);
    return [];
  }
});