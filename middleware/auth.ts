import { HOME_PAGE_PATH } from '~/constants';
import { useAuthStore } from '~/store/auth.store';

export default defineNuxtRouteMiddleware((to, from) => {
  // skip middleware on server
  if (import.meta.server) return;
  const useAuth = useAuthStore();
  if (!useAuth.loadAuthorizationSuccess) {
    useAuth.loadFromLocalStorage();
  }
  if (!useAuth.authenticated) {
    // useAuth.openAuthPopup(true);
    return navigateTo(HOME_PAGE_PATH);
  }
});
