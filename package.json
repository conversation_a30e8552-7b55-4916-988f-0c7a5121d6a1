{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "start:prod": "nuxt start", "lint:eslint": "eslint .", "lint:prettier": "prettier . --check", "lintfix": "eslint . --fix && prettier --write --list-different ."}, "dependencies": {"@nuxt/fonts": "^0.0.1", "@nuxtjs/seo": "^3.0.3", "@nuxtjs/sitemap": "^7.2.10", "@pinia/nuxt": "^0.5.1", "@tailwindcss/postcss": "^4.0.9", "@tailwindcss/typography": "^0.5.13", "@tailwindcss/vite": "^4.0.9", "@vee-validate/nuxt": "^4.15.0", "@vee-validate/yup": "^4.15.0", "@vueup/vue-quill": "^1.2.0", "cally": "^0.8.0", "daisyui": "^5.0.0", "eslint-plugin-prettier": "^5.2.1", "moment": "^2.30.1", "nuxt": "^3.15.4", "nuxt-gtag": "^3.0.2", "nuxt-vue3-google-signin": "^0.0.6", "pikaday": "^1.8.2", "pinia": "^2.1.7", "postcss": "^8.5.3", "tailwindcss": "^4.0.9", "vee-validate": "^4.15.0", "vue": "^3.4.27", "vue-router": "^4.3.2", "yup": "^1.6.1"}, "version": "1.0.0", "main": "index.js", "license": "MIT", "devDependencies": {"@nuxt/devtools": "^2.1.1", "@nuxt/eslint": "^1.1.0", "@nuxtjs/device": "^3.1.1", "eslint": "^9.21.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-vue": "^10.0.0", "prettier": "^3.5.3", "typescript-eslint": "^8.29.0"}}