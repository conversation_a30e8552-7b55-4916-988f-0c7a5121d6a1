<script setup lang="ts">
import type { NuxtError } from '#app';

const props = defineProps({
  error: Object as () => NuxtError,
});
</script>

<template>
  <div class="hero bg-base-200 min-h-screen">
    <div class="hero-content text-center">
      <div class="max-w-md">
        <h1 class="text-5xl font-bold">{{ props.error?.statusCode }}</h1>
        <p>{{ props.error?.message }}</p>
        <p>{{ props.error?.stack }}</p>
        <p class="py-6"><PERSON><PERSON> có lỗi xảy ra, vui lòng thử lại sau.</p>
        <NuxtLink to="/" class="btn btn-primary">
          Trở về trang chủ
        </NuxtLink>
      </div>
    </div>
  </div>
</template>
