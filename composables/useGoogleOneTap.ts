import { useOneTap, type CredentialResponse } from 'vue3-google-signin';
import { useAuthStore } from '~/store/auth.store';
import { useToast } from '~/store/toast';

export const useGoogleOneTapAuth = () => {
  const authStore = useAuthStore();
  const toast = useToast();

  const handleOneTapSuccess = async (response: CredentialResponse) => {
    try {
      const idToken = response.credential;
      if (idToken) {
        await authStore.authenticateUserWithIdToken(idToken);
        toast.success('Đăng nhập thành công với Google One Tap!');

        // Close any open authentication modals
        authStore.openAuthPopup(false);

        // Redirect to dashboard or intended page if needed
        await navigateTo('/');
      }
    } catch (error) {
      console.error('One Tap authentication error:', error);
      toast.error('Đăng nhập One Tap thất bại');
    }
  };

  const handleOneTapError = () => {
    console.log('One Tap error:');
    // Don't show error toast for One Tap failures as they're often due to user dismissal
    // or normal flow interruptions
  };

  const initializeOneTap = (options?: {
    autoSelect?: boolean;
    cancelOnTapOutside?: boolean;
    disabledCooldown?: boolean;
  }) => {
    // Only initialize if user is not already authenticated
    if (!authStore.authenticated) {
      try {
        useOneTap({
          onSuccess: handleOneTapSuccess,
          onError: handleOneTapError,
          cancelOnTapOutside: options?.cancelOnTapOutside ?? false,
          // Auto-select account if only one Google account is available
          autoSelect: options?.autoSelect ?? true,
        });
      } catch (error) {
        console.error('Failed to initialize Google One Tap:', error);
      }
    }
  };

  const isOneTapAvailable = () => {
    // Check if One Tap is available (user not authenticated and Google API loaded)
    return !authStore.authenticated && typeof window !== 'undefined' && window.google;
  };

  return {
    initializeOneTap,
    handleOneTapSuccess,
    handleOneTapError,
    isOneTapAvailable,
  };
};
