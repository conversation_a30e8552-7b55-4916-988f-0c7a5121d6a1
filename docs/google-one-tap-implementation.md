# Google One Tap Implementation

This document describes the Google One Tap authentication implementation in the Nuxt.js application.

## Overview

Google One Tap provides a seamless authentication experience by automatically prompting users to sign in with their Google account without requiring them to click a sign-in button.

## Implementation Components

### 1. Auth Store Method (`store/auth.store.ts`)

Added `authenticateUserWithIdToken` method to handle Google ID tokens from One Tap:

```typescript
async authenticateUserWithIdToken(idToken: string) {
  await httpRequestStore().request<CustomerAuth>(
    'v1/auth/google-one-tap',
    'POST',
    { idToken },
    // ... handlers
  );
}
```

### 2. Composable (`composables/useGoogleOneTap.ts`)

Created a reusable composable that handles:
- One Tap initialization
- Success/error handling
- Authentication state management
- Availability checking

### 3. Global Plugin (`plugins/googleOneTap.client.ts`)

Automatically initializes One Tap across the entire application:
- Runs on app start with a 1-second delay
- Re-initializes when user logs out
- Only runs on client-side

### 4. Updated Login Component (`components/auth/LoginWithGoogleForm.vue`)

Simplified to focus on manual Google Sign-In button, while One Tap is handled globally.

## Configuration

### Backend API Endpoint

The implementation expects a backend endpoint at `v1/auth/google-one-tap` that:
- Accepts an `idToken` parameter
- Verifies the Google ID token
- Returns user authentication data

### Environment Variables

Ensure `NUXT_PUBLIC_GOOGLE_CLIENT_ID` is set in your environment.

## Usage

### Automatic Initialization

One Tap is automatically initialized globally. No additional setup required.

### Manual Initialization

```typescript
const { initializeOneTap } = useGoogleOneTapAuth();

initializeOneTap({
  autoSelect: true,
  cancelOnTapOutside: false,
});
```

### Check Availability

```typescript
const { isOneTapAvailable } = useGoogleOneTapAuth();

if (isOneTapAvailable()) {
  // One Tap can be shown
}
```

## Testing

Visit `/test-one-tap` to test the implementation:
- Shows authentication status
- Displays One Tap availability
- Allows manual triggering
- Provides testing instructions

## Behavior

1. **When user is not authenticated**: One Tap appears automatically after 1 second
2. **When user is authenticated**: One Tap is not shown
3. **After logout**: One Tap re-initializes automatically
4. **On error**: Errors are logged but don't show user-facing messages (to avoid spam)
5. **On success**: User is authenticated and redirected to home page

## Browser Requirements

- Modern browsers with Google API support
- JavaScript enabled
- Third-party cookies enabled (for Google services)

## Troubleshooting

### One Tap Not Appearing

1. Check if user is already authenticated
2. Verify Google Client ID is configured
3. Ensure Google API is loaded
4. Check browser console for errors
5. Verify domain is authorized in Google Console

### Authentication Failures

1. Check backend endpoint implementation
2. Verify ID token validation
3. Check network requests in browser dev tools
4. Ensure proper CORS configuration

## Security Considerations

- ID tokens are verified on the backend
- Tokens are transmitted securely over HTTPS
- User consent is handled by Google
- No sensitive data is stored in frontend
