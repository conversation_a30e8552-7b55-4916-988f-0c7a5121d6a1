import type { CustomerAuth } from '~/types';

const AUTH_PROFILE = 'profile';
const AUTH_ACCESS_TOKEN = 'accessToken';

function saveAuth(auth: CustomerAuth) {
  if (!auth) {
    return;
  }
  const { profile, accessToken } = auth;

  localStorage.setItem(AUTH_PROFILE, JSON.stringify(profile));
  localStorage.setItem(AUTH_ACCESS_TOKEN, accessToken);
}

function getAuth(): CustomerAuth | null {
  try {
    const profile = localStorage.getItem(AUTH_PROFILE);
    const accessToken = localStorage.getItem(AUTH_ACCESS_TOKEN);
    if (!profile || !accessToken) {
      return null;
    }
    return {
      profile: JSON.parse(profile),
      accessToken,
    };
  } catch (error) {
    console.error('Error getting auth from localstorage', error);
    return null;
  }
}

function getAccessToken(): string | null {
  return localStorage.getItem(AUTH_ACCESS_TOKEN);
}

function removeAuth() {
  localStorage.removeItem(AUTH_PROFILE);
  localStorage.removeItem(AUTH_ACCESS_TOKEN);
}

export { saveAuth, getAuth, removeAuth, getAccessToken };
