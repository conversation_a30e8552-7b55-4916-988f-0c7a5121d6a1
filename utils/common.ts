import { TinyInt, type CanBeNil } from '~/types';

export function formatNumber(value: number): string {
  return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}
// 1000000 => 1,000,000

export function generateJobUrl(baseUrl: string, slug: string): string {
  return `${baseUrl}/cong-viec/${slug}`;
}

export function removeUnicode(str: string): string {
  if (!str) return str;
  str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, 'a');
  str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, 'e');
  str = str.replace(/ì|í|ị|ỉ|ĩ/g, 'i');
  str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, 'o');
  str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, 'u');
  str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, 'y');
  str = str.replace(/đ/g, 'd');
  str = str.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/g, 'A');
  str = str.replace(/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/g, 'E');
  str = str.replace(/Ì|Í|Ị|Ỉ|Ĩ/g, 'I');
  str = str.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/g, 'O');
  str = str.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/g, 'U');
  str = str.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/g, 'Y');
  str = str.replace(/Đ/g, 'D');
  return str;
}

export function removeSpecialCharacter(str: string): string {
  if (!str) return str;
  return str.replace(/[&\/\\#,+()$~%.'":*?<>{}]/g, '');
}

export function isBoolean(value: any): boolean {
  return typeof value === 'boolean';
}

export function isNil(value: null | undefined | any): boolean {
  return value === null || value === undefined;
}

export function tinyintToBoolean(value: CanBeNil<TinyInt>): boolean {
  return value === TinyInt.Yes;
}

export function booleanToTinyint(value: CanBeNil<boolean>): TinyInt {
  return value ? TinyInt.Yes : TinyInt.No;
}

export function copyToClipboard(text: string): Promise<void> {
  return new Promise((resolve, reject) => {
    if (!navigator.clipboard) {
      reject(new Error('Clipboard API not supported'));
      return;
    }
    navigator.clipboard
      .writeText(text)
      .then(() => resolve())
      .catch((error) => reject(error));
  });
}