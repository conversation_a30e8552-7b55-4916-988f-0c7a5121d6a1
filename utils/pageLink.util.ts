import type { CanBeNil, Recruitment } from '~/types';

export function getJobPath(slug: string, id: number): string {
  if (!slug || !id) {
    return '/';
  }
  return `/cong-viec/${slug}id${id}`;
}

export function getJobLinkWithDomain(recruitment: CanBeNil<Recruitment>, domain: string = location.origin): string {
  const path = getJobPath(recruitment?.slug || '', recruitment?.id || 0);

  return `${domain}${path}`;
}
