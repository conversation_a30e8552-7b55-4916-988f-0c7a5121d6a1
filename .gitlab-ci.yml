# This file is a template, and might need editing before it works on your project.
# To contribute improvements to CI/CD templates, please follow the Development guide at:
# https://docs.gitlab.com/ee/development/cicd/templates.html
# This specific template is located at:
# https://gitlab.com/gitlab-org/gitlab/-/blob/master/lib/gitlab/ci/templates/Docker.gitlab-ci.yml

# Build a Docker image with CI/CD and push to the GitLab registry.
# Docker-in-Docker documentation: https://docs.gitlab.com/ee/ci/docker/using_docker_build.html
#
# This template uses one generic job with conditional builds
# for the default branch and all other (MR) branches.
#
image: docker:20.10.16
stages:
  - "build"
  - "push"
  - "webhook"

# This folder is cached between builds
# http://docs.gitlab.com/ee/ci/yaml/README.html#cache
default:
  services:
    - docker:20.10.16-dind

before_script:
  - export IMAGE_NAME_WITH_REGISTRY_PREFIX=$CI_REGISTRY_IMAGE # Your repository prefixed with GitLab Registry URL
  - export COMMIT_HASH=$CI_COMMIT_SHA # Your current commit sha
  - docker login -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD" $CI_REGISTRY
  - apk add --update curl && rm -rf /var/cache/apk/*
  - export CONTAINER_FULL_IMAGE_NAME=$IMAGE_NAME_WITH_REGISTRY_PREFIX/$CI_COMMIT_REF_NAME
  - export CONTAINER_FULL_IMAGE_NAME_WITH_TAG=$CONTAINER_FULL_IMAGE_NAME:$COMMIT_HASH
docker-build:
  stage: build
  # Default branch leaves tag empty (= latest tag)
  # All other branches are tagged with the escaped branch name (commit ref slug)
  #
  only:
    - development
    - master
    - main
  script:
    - |
      if [[ "$CI_COMMIT_BRANCH" == "$CI_DEFAULT_BRANCH" ]]; then
        tag=""
        echo "Running on default branch '$CI_DEFAULT_BRANCH': tag = 'latest'"
      else
        tag="$CI_COMMIT_REF_SLUG.$CI_COMMIT_SHA"
        echo "Running on branch '$CI_COMMIT_BRANCH': tag = $tag"
      fi
    - docker build --pull -t "${CONTAINER_FULL_IMAGE_NAME_WITH_TAG}" .
    - docker push "${CONTAINER_FULL_IMAGE_NAME_WITH_TAG}"

push-latest:
  variables:
    GIT_STRATEGY: none
  stage: push
  only:
    - master
    - main
    - development
  script:
    - docker pull $CONTAINER_FULL_IMAGE_NAME_WITH_TAG
    - docker tag $CONTAINER_FULL_IMAGE_NAME_WITH_TAG $CONTAINER_FULL_IMAGE_NAME:latest
    - docker push $CONTAINER_FULL_IMAGE_NAME:latest

push-tag:
  variables:
    GIT_STRATEGY: none
  stage: push
  rules:
    - if: '$CI_COMMIT_TAG =~ /^v[0-9]+(\.[0-9]+)*$/' # Regex for semantic versioning tags
  script:
    - docker pull $CONTAINER_FULL_IMAGE_NAME_WITH_TAG || true
    - docker tag $CONTAINER_FULL_IMAGE_NAME_WITH_TAG $CONTAINER_FULL_IMAGE_NAME:$CI_COMMIT_TAG
    - docker push $CONTAINER_FULL_IMAGE_NAME:$CI_COMMIT_TAG

webhook:
  stage: webhook
  only:
    - development
    - main
  script:
    - docker run caprover/cli-caprover:v2.1.1 caprover deploy --caproverUrl $CAPROVER_URL --caproverPassword $CAPROVER_PASSWORD --caproverApp $CAPROVER_APP --imageName $CONTAINER_FULL_IMAGE_NAME_WITH_TAG
