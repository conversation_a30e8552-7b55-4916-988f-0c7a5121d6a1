<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    stroke-width="1.5"
  >
    <path
      fill="none"
      stroke="currentColor"
      stroke-linecap="round"
      stroke-linejoin="round"
      d="m9.037 10.867l1.055 2.129c.144.296.528.58.852.635l1.914.32c1.224.206 1.512 1.101.63 1.984L12 17.435c-.252.255-.39.745-.312 1.096l.426 1.857c.336 1.47-.438 2.038-1.728 1.27l-1.793-1.07c-.324-.195-.858-.195-1.188 0l-1.794 1.07c-1.284.768-2.064.193-1.728-1.27l.426-1.858c.078-.35-.06-.84-.312-1.094l-1.488-1.5c-.876-.884-.594-1.779.63-1.985l1.914-.32c.318-.055.702-.339.846-.635l1.056-2.13c.576-1.155 1.512-1.155 2.082 0M22 2l-8 8m2-8l-5 5m9 3l-3 3"
      color="currentColor"
    />
  </svg>
</template>
<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'IconFallingStar',
});
</script>
