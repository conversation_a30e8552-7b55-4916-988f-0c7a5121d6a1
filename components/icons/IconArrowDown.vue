<script setup lang="ts">
import { defineComponent } from 'vue';

defineComponent({
  name: 'IcRoundKeyboardArrowDown',
});
</script>
<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    stroke-linecap="round"
    stroke-linejoin="round"
    width="24"
    height="24"
    stroke-width="1.5"
  >
    <path d="M6 9l6 6l6 -6"></path>
  </svg>
</template>
