<script setup lang="ts">
import { defineComponent } from 'vue';

defineComponent({
  name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
});
</script>
<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    stroke-linecap="round"
    stroke-linejoin="round"
    width="24"
    height="24"
    stroke-width="1.5"
  >
    <path d="M12 12m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0"></path>
    <path d="M3 6m0 2a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v8a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z"></path>
    <path d="M18 12l.01 0"></path>
    <path d="M6 12l.01 0"></path>
  </svg>
</template>
