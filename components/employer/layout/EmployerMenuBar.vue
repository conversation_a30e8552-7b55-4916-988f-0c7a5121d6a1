<template>
  <ul class="menu menu-lg bg-base-100 w-full md:max-w-[280px]">
    <template
      v-for="menu in menuList"
      :key="`employer-profile-menu-key-${menu.key}`"
    >
      <li class="menu-title text-gray-950">
        {{ menu.title }}
      </li>
      <li
        v-for="item in menu.items || []"
        :key="`employer-profile-menu-key-${item.key}`"
      >
        <NuxtLink
          :to="item.path"
          :class="[
            'text-[13px] text-nowrap',
            {
              'menu-active': activeMenuItem === item.key,
            },
          ]"
          @click="() => $emit('change', item.key)"
        >
          <span>
            <component
              :is="item.iconComponent"
              :class="[
                {
                  '!text-base-300': activeMenuItem === item.key,
                },
                'w-6 h-6 text-xl text-info stroke-2',
              ]"
            />
          </span>
          <span class="font-normal">{{ item.title }}</span>
        </NuxtLink>
      </li>
    </template>
  </ul>
</template>
<script setup lang="ts">
import IconClipboardDocument from '~/components/icons/IconClipboardDocument.vue';
import IconFallingStar from '~/components/icons/IconFallingStar.vue';
import IconGroup from '~/components/icons/IconGroup.vue';
import IconPlus from '~/components/icons/IconPlus.vue';
import IconTabler from '~/components/icons/IconTabler.vue';

const emits = defineEmits(['change']);

const route = useRoute();
const activeMenuItem = ref<string | null>(null);

const menuList = [
  {
    key: 'job-management',
    title: 'Quản lý đăng tuyển',
    items: [
      {
        key: 'create-job',
        path: '/employer/jobs/create',
        title: 'Tạo tin tuyển dụng',
        icon: null,
        iconComponent: IconPlus,
      },
      {
        key: 'job-list',
        path: '/employer/jobs/list',
        title: 'Danh sách tin tuyển dụng',
        icon: null,
        iconComponent: IconClipboardDocument,
      },
    ],
  },
  {
    key: 'applied-jobs',
    title: 'Quản lý ứng viên',
    items: [
      {
        key: 'applied-jobs-list',
        path: '/employer/jobs/applied',
        title: 'Danh sách ứng viên',
        icon: null,
        iconComponent: IconGroup,
      },
    ],
  },
  {
    key: 'account',
    title: 'Quản lý tài khoản',
    items: [
      {
        key: 'profile',
        path: '/employer/accounts/profile',
        title: 'Tài khoản nhà tuyển dụng',
        icon: null,
        iconComponent: IconTabler,
      },
      {
        key: 'company_trademark',
        path: '/employer/accounts/trademark',
        title: 'Quản bá thương hiệu',
        icon: null,
        iconComponent: IconFallingStar,
      },
    ],
  },
];

const activeClass = ref('bg-info text-base-100');

const setActiveMenu = () => {
  const path = window.location.pathname;
  const menuItemFound = menuList
    .map(({ items }) => items)
    .flat()
    .find((item) => item.path === path);
  if (menuItemFound) {
    activeMenuItem.value = menuItemFound.key;
  } else {
    activeMenuItem.value = null;
  }
};

onMounted(() => {
  setActiveMenu();
});

watch(
  () => route.path,
  () => {
    console.log('route changed' + route.path);
    setActiveMenu();
  },
);
</script>
