<template>
  <button class="btn btn-link text-info !no-underline text-nowrap">
    <IconPlus class="w-4 h-4 stroke-3" />
    <span>{{ label }}</span>
    <span
      v-if="tips"
      class="text-xs text-gray-500"
    >
      {{ tips }}
    </span>
  </button>
</template>
<script lang="ts" setup>
import IconPlus from '~/components/icons/IconPlus.vue';
defineProps({
  label: {
    type: String,
    required: true,
  },
  tips: {
    type: String,
    default: '',
  },
});
</script>
