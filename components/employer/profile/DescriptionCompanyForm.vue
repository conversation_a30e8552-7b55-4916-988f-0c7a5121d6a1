<template>
  <div>
    <FormItem
      :required="true"
      label="<PERSON>iớ<PERSON> thiệu doanh nghiệp"
      :error="errors.overview"
    >
      <RichEditor v-model:model-value="overview" />
    </FormItem>
    <FormItem
      :required="false"
      label="Website công ty"
      :error="errors.website"
    >
      <input
        v-model="website"
        type="text"
        class="input input-bordered w-full"
        placeholder="https://"
      />
    </FormItem>
    <FormItem
      :required="false"
      label=""
    >
      <button
        class="btn btn-primary px-16"
        @click="onSubmit"
      >
        Cập nhật
      </button>
    </FormItem>
  </div>
</template>
<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/yup';
import { object, string } from 'yup';

import RichEditor from '~/components/common/RichEditor.vue';
import FormItem from '~/components/form/formItem.vue';
import type { CanBeNil } from '~/types';
const emits = defineEmits(['ok']);
export type ICompanyDescriptionPayload = ICompanyDescriptionForm;

const props = defineProps({
  defaultWebsite: {
    type: String as () => CanBeNil<string>,
    required: false,
    default: '',
  },
  defaultOverview: {
    type: String as () => CanBeNil<string>,
    required: false,
    default: '',
  },
});

interface ICompanyDescriptionForm {
  overview: string;
  website: string;
}
const { values, handleSubmit, setErrors, errors, defineField } = useForm<ICompanyDescriptionForm>({
  validationSchema: toTypedSchema(
    object({
      overview: string().required('Giới thiệu doanh nghiệp không được để trống'),
      website: string().nullable().url('Website không hợp lệ'),
    }),
  ),
  initialValues: {
    overview: '',
    website: '',
  },
});
const [overview] = defineField('overview');
const [website] = defineField('website');

const onSubmit = handleSubmit((values) => {
  console.log('onSubmit', values);
  const payload: ICompanyDescriptionPayload = {
    overview: values.overview,
    website: values.website,
  };

  emits('ok', payload);
  // Call the API to update the company description
  // useCompanyStore.updateCompanyDescription(values);
});

const mapWebsiteToState = () => {
  if (props.defaultWebsite) {
    website.value = props.defaultWebsite;
  }
};
const mapOverviewToState = () => {
  if (props.defaultOverview) {
    overview.value = props.defaultOverview;
  }
};

const mapPropsToState = () => {
  mapOverviewToState();
  mapWebsiteToState();
};

onMounted(() => {
  mapPropsToState();
});

watch(
  () => props.defaultOverview,
  (newValue, oldValue) => {
    if (newValue !== oldValue) {
      mapOverviewToState();
    }
  },
);
watch(
  () => props.defaultWebsite,
  (newValue, oldValue) => {
    if (newValue !== oldValue) {
      mapWebsiteToState();
    }
  },
);
</script>
