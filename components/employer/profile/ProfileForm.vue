<template>
  <div>
    <!-- {{ errors }} -->
    <HorizontalFormItem
      label="Ảnh đại diện"
      :error="errors['logo']"
    >
      <div class="flex justify-start items-center">
        <div class="mr-8">
          <div class="avatar">
            <div class="w-24 rounded-full">
              <img alt="vietlamlamdong.site" :src="logo ? logo : '/images/default_avatar.png'" />
            </div>
          </div>
        </div>
        <div>
          <button
            class="btn btn-ghost"
            @click="uploadFile"
          >
            <span>Tải ảnh lên</span>
          </button>
          <input
            id="input_upload_avatar"
            type="file"
            class="hidden"
            accept="image/jpg,image/jpeg,image/png"
            @change="onFileChange"
          />
          <p class="font-light text-xs text-gray-400 my-3">
            Định dạng .JPG, .JPEG, .PNG dung lượng thấp hơn 300 KB với kích thước tối thiểu 300x300 px
          </p>
        </div>
      </div>
    </HorizontalFormItem>
    <HorizontalFormItem
      label="Tên công ty"
      required
      :error="errors['name']"
    >
      <input
        type="text"
        class="input input-bordered w-full"
        placeholder="Tên công ty"
        :value="name"
        @change="(e) => (name = (e.target as HTMLInputElement)?.value)"
      />
    </HorizontalFormItem>
    <HorizontalFormItem
      label="Quy mô công ty"
      required
      :error="errors['employeeSizeId']"
    >
      <JobEmployeeSizeSelect v-model="employeeSizeId" />
    </HorizontalFormItem>
    <HorizontalFormItem
      label="Địa điểm"
      required
      :error="errors['provinceId']"
    >
      <ProvinceSelect v-model="provinceId" />
    </HorizontalFormItem>
    <HorizontalFormItem
      label="Địa chỉ"
      required
      :error="errors['address']"
    >
      <textarea
        class="input input-bordered w-full"
        placeholder="Nhập địa chỉ"
        :value="address"
        @change="address = ($event.target as HTMLInputElement).value"
      ></textarea>
    </HorizontalFormItem>
    <HorizontalFormItem
      label="Số điện thoại cố định"
      :required="false"
      :error="errors['phone']"
    >
      <input
        type="text"
        class="input input-bordered w-full"
        placeholder="Nhập số điện thoại cố định"
        :value="phone"
        @change="onPhoneChange"
      />
    </HorizontalFormItem>

    <div class="my-4 flex justify-end">
      <button
        class="btn btn-primary btn-wide px-12"
        @click="onSubmit"
      >
        {{ props.okText }}
      </button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { toTypedSchema } from '@vee-validate/yup';
import { number, object, string } from 'yup';

import { isNil } from '@/utils/common';
import HorizontalFormItem from '~/components/form/HorizontalFormItem.vue';
import JobEmployeeSizeSelect from '~/components/resume/GeneralInformation/JobEmployeeSizeSelect.vue';
import ProvinceSelect from '~/components/resume/ProvinceSelect.vue';
import type { CanBeNil, Company, File, Recruitment } from '~/types';
import type { HttpResponse } from '~/store/httpRequest.store';
import { useToast } from '~/store/toast';

definePageMeta({
  layout: 'employer',
});

export interface IEmployerProfileForm {
  name: string;
  // taxCode: string;
  provinceId: number;
  address: string;
  employeeSizeId: number;
  // occupationIds: number[];
  logo: CanBeNil<string>;
  phone: CanBeNil<string>;
}

const emits = defineEmits(['ok']);

const props = defineProps({
  okText: {
    type: String,
    default: 'Cập nhật',
  },
  defaultProfile: {
    type: Object as () => CanBeNil<Company>,
    default: () => null,
  },
});

interface FormState {
  name: string;
  // taxCode: string;
  logo: CanBeNil<string>;
  address: string;
  provinceId: number | null;
  employeeSizeId: number | null;
  // occupationIds: number[];
  phone: CanBeNil<string>;
}
const { values, handleSubmit, setErrors, errors, defineField } = useForm<FormState>({
  validationSchema: toTypedSchema(
    object({
      name: string().required('Tên công ty không được để trống'),
      // taxCode: string().required('Mã số thuế không được để trống'),
      logo: string().optional().nullable().test('logo', 'Logo không đúng định dạng', (value) => {
        if (isNil(value)) return true;
        if (!value) return true;

        const fileRegex = /\.(jpg|jpeg|png)$/i;
        return fileRegex.test(value || '');
      }),
      address: string().required('Địa chỉ không được để trống'),
      provinceId: number().integer().required('Tỉnh/Thành phố không được để trống'),
      employeeSizeId: number().required('Quy mô công ty không được để trống'),
      // occupationIds: array()
      //   .min(1, 'Ngành nghề không được để trống')
      //   .of(number().integer().required('Ngành nghề không được để trống'))
      //   .required('Ngành nghề không được để trống'),
      phone: string()
        .nullable()
        .test('phone', 'Số điện thoại không đúng định dạng', (value) => {
          if (isNil(value)) return true;
          if (!value) return true;

          const phoneRegex = /^(0[3|5|7|8|9]|01[2|6|8|9])[0-9]{8}$/;
          return phoneRegex.test(value || '');
        }),
    }),
  ),
  // initialValues: {
  //   name: 'Cty TNHH ABC',
  //   // taxCode: '1234567890',
  //   logo: '',
  //   address: '123 Đường ABC, Phường XYZ, Quận 1',
  //   provinceId: 1186,
  //   employeeSizeId: 3,
  //   // occupationIds: [1, 2],
  //   phone: '0123456789',
  // },
});

const [name] = defineField('name');
// const [taxCode] = defineField('taxCode');
const [logo] = defineField('logo');
const [address] = defineField('address');
const [provinceId] = defineField('provinceId');
const [employeeSizeId] = defineField('employeeSizeId');
// const [occupationIds] = defineField('occupationIds');
const [phone] = defineField('phone');

const uploadFile = () => {
  const input = document.getElementById('input_upload_avatar');
  if (input) {
    input.click();
  }
};

const onFileChange = async (e: Event) => {
  const target = e.target as HTMLInputElement;
  const file = target.files?.[0];
  if (file) {
    const reader = new FileReader();
    reader.onload = (e) => {
      logo.value = e.target?.result as string;
    };
    reader.readAsDataURL(file);

    const config = useRuntimeConfig();
    const baseUrl = config.public.apiUrl;
    const formData = new FormData();
    formData.append('file', file);
    try {
      const response = await fetch(`${baseUrl}/v1/files/upload`, {
        method: 'POST',
        body: formData,
        headers: {
          Authorization: `Bearer ${getAccessToken()}`,
        },
      });
      const responseData: HttpResponse<File> = await response.json();

      if (responseData.success) {
        logo.value = responseData.data?.url;
      }
    } catch (error: any) {
      console.error(error);
      useToast().error(error?.message || 'Tải ảnh lên thất bại');
    }
  }
};

const onPhoneChange = (e: Event) => {
  phone.value = (e.target as HTMLInputElement).value;
};

const onSubmit = handleSubmit(async (values) => {
  const eventData: IEmployerProfileForm = {
    name: values.name,
    // taxCode: values.taxCode || '',
    logo: values.logo,
    address: values.address || '',
    provinceId: values.provinceId || 0,
    employeeSizeId: values.employeeSizeId || 0,
    // occupationIds: values.occupationIds || [],
    phone: values.phone,
  };
  emits('ok', eventData);
});
const mapProfilePropsToState = () => {
  console.log('mapProfilePropsToState', props.defaultProfile);
  if (props.defaultProfile) {
    name.value = props.defaultProfile.name || '';
    // taxCode.value = props.defaultProfile.taxCode;
    logo.value = props.defaultProfile.logo;
    address.value = props.defaultProfile.address || '';
    provinceId.value = props.defaultProfile.provinceId;
    employeeSizeId.value = props.defaultProfile.employeeSizeId;
    // occupationIds.value = props.defaultProfile.occupationIds || [];
    phone.value = props.defaultProfile.phone;
  }
};
onMounted(() => {
  mapProfilePropsToState();
});

watch(
  () => props.defaultProfile,
  (newValue) => {
    if (newValue) {
      mapProfilePropsToState();
    }
  },
  { immediate: true },
);
</script>
