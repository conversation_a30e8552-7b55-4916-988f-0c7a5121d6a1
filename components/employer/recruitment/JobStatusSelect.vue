<template>
  <DropdownSearchSelect
    :list="emptySelect ? [emptyValue, ...list] : list"
    id-key="id"
    value-key="name"
    placeholder="Chọn trạng thái"
    @select="onSelect"
  />
</template>
<script setup lang="ts">
import { ERecruitmentStatus } from '@/types/recruitment.interface';
import DropdownSearchSelect from '~/components/common/DropdownSearchSelect.vue';
import { RecruitmentStatusTextMapper } from '~/constants/recruitment.constant';

const list = Object.values(ERecruitmentStatus).map((item) => ({
  id: item,
  name: RecruitmentStatusTextMapper[item],
}));

const emptyValue = {
  id: null,
  name: 'Tất cả trạng thái',
};
const emits = defineEmits(['onChange']);
const props = defineProps({
  emptySelect: {
    type: Boolean,
    default: false,
  },
  value: {
    type: Number as () => number | null,
    default: null,
  },
});

const onSelect = (id: number) => {
  console.log('onSelect', id);
  emits('onChange', id);
};
</script>
