<template>
  <Collapse
    :title="formTitle"
    :expanded="true"
    :show-adding="false"
    :loading="loading"
  >
    <div v-if="props.company?.isBot">
      {{ errors }}
      <FormItem label="Raw json">
        <textarea
          v-model="rawJson"
          class="input textarea w-full"
        />
      </FormItem>
      <div class="grid grid-cols-12 gap-2">
        <FormItem
          label="Tỉnh/Thành phố"
          class="col-span-1 md:col-span-4"
          :required="true"
          :error="errors.addresses"
        >
          <ProvinceSelect v-model="rawProvinceId" />
        </FormItem>
        <FormItem
          label="Quận/Huyện"
          class="col-span-12 md:col-span-4"
          :required="true"
          :error="errors.addresses"
        >
          <DistrictSelect
            v-model="rawDistrictId"
            :province-id="rawProvinceId"
          />
        </FormItem>

        <FormItem
          label="Tên công ty"
          class="col-span-12 md:col-span-4"
          :required="true"
          :error="errors.addresses"
        >
          <input
            type="text"
            class="input input-bordered w-full"
            placeholder="Nhập tên công ty"
            v-model="companyName"
          />
        </FormItem>
      </div>
      <!-- <AvatarUploader v-model:model-value="logo" /> -->
      <DragDropImageUploader v-model:model-value="logo" />
      <br />
      <button
        class="btn btn-primary w-full"
        @click="onParse"
      >
        Parse
      </button>
    </div>
    <div>
      <FormItem
        label="Tiêu đề tin tuyển dụng"
        required
        :error="errors.title"
      >
        <input
          type="text"
          class="input input-bordered w-full"
          placeholder="Nhập tiêu đề"
          :value="title"
          @change="onTitleChange"
        />
      </FormItem>
    </div>
    <div class="grid grid-cols-12 gap-2">
      <FormItem
        label="Ngành nghề"
        class="col-span-12 md:col-span-6"
        required
        :error="errors.occupations"
      >
        <OccupationMultipleSelect v-model="occupations" />
      </FormItem>
      <FormItem
        label="Kinh nghiệm"
        class="col-span-12 md:col-span-2"
        required
        :error="errors.experience"
      >
        <JobExperienceSelect v-model="experience" />
      </FormItem>
      <FormItem
        label="Số lượng tuyển"
        class="col-span-12 md:col-span-2"
        required
        :error="errors.vacancy"
      >
        <input
          type="number"
          class="input input-bordered w-full"
          placeholder="Nhập số lượng. VD: 10"
          :value="vacancy"
          @change="onVacancyChange"
        />
      </FormItem>
      <FormItem
        label="Hạn nộp hồ sơ"
        class="col-span-12 md:col-span-2"
        required
        :error="errors.expiredAt"
      >
        <input
          type="date"
          :value="formatDate(expiredAt, 'YYYY-MM-DD')"
          class="input input-bordered w-full"
          :min="new Date().toISOString().split('T')[0]"
          @change="onExpiredAtChange"
        />
      </FormItem>
    </div>
    <div>
      <div
        v-for="(address, idx) of addresses"
        :key="idx"
        class="bg-gray-100 p-3 mb-2 rounded-sm"
      >
        <div class="flex justify-between items-center text-sm font-semibold">
          <div>Địa chỉ {{ idx + 1 }}</div>
          <a
            href="javascript:void(0)"
            @click="onRemoveAddress(idx)"
          >
            <IconTrash class="w-4 h-4 text-red-500" />
          </a>
        </div>
        <Divider />
        <div class="grid grid-cols-12 gap-2">
          <FormItem
            label="Tỉnh/Thành phố"
            class="col-span-12 md:col-span-4"
            :required="true"
            :error="errors.addresses"
          >
            <ProvinceSelect v-model="address.provinceId" />
          </FormItem>
          <FormItem
            label="Quận/Huyện"
            class="col-span-12 md:col-span-4"
            :required="true"
            :error="errors.addresses"
          >
            <DistrictSelect
              v-model="address.districtId"
              :province-id="address.provinceId"
            />
          </FormItem>
          <FormItem
            label="Địa chỉ chi tiết"
            class="col-span-12 md:col-span-4"
            :required="true"
            :error="errors.addresses"
          >
            <input
              type="text"
              class="input input-bordered w-full"
              placeholder="VD: 123 Điện Biên Phủ"
              :value="address.address"
              @change="(e) => onAddressChange(idx, e)"
            />
          </FormItem>
        </div>
      </div>
      <AddingButton
        v-if="addresses?.length < MAX_ADDRESS"
        label="Thêm địa điểm làm việc"
        tips="Tối đa 5 địa điểm"
        @click="onAddNewLocation"
      />
    </div>
    <br />
    <div class="text-base font-semibold mb-4">Yêu cầu chung</div>
    <div class="grid grid-cols-12 gap-2">
      <FormItem
        label="Hình thức làm việc"
        class="col-span-12 lg:col-span-3"
        required
        :error="errors.method"
      >
        <JobMethodSelect v-model="method" />
      </FormItem>
      <FormItem
        label="Cấp bậc"
        class="col-span-12 lg:col-span-3"
        required
        :error="errors.level"
      >
        <JobLevelSelect v-model="level" />
      </FormItem>
      <div class="col-span-12 md:col-span-6 flex items-center space-x-2">
        <FormItem
          label="Lương tối thiểu"
          required
          :error="errors.minSalary"
        >
          <div class="input input-bordered w-full flex space-x pr-0 items-center">
            <input
              type="number"
              :min="0"
              :value="minSalary"
              class="h-full w-full"
              :disabled="salaryNegotiable"
              placeholder="Nhập tối thiểu"
              @change="onMinSalaryChange"
            />
            <div class="text-xs px-2 h-full text-center leading-9 bg-slate-200 rounded-r-lg font-normal">triệu</div>
          </div>
        </FormItem>
        <FormItem :label="''">
          <div class="mx-2 h-10 leading-10">-</div>
        </FormItem>
        <FormItem
          label="Lương tối đa"
          required
          :error="errors.maxSalary"
        >
          <div class="input input-bordered w-full flex space-x pr-0">
            <input
              type="number"
              :min="0"
              :value="maxSalary"
              class="h-full w-full"
              :disabled="salaryNegotiable"
              placeholder="Nhập tối đa"
              @change="onMaxSalaryChange"
            />
            <div class="text-xs px-2 h-full text-center leading-9 bg-slate-200 rounded-r-lg font-normal">triệu</div>
          </div>
        </FormItem>
        <FormItem :label="''">
          <CheckBox
            v-model="salaryNegotiable"
            text="Lương thỏa thuận"
          />
        </FormItem>
      </div>
      <FormItem
        v-if="probationFormOpen"
        label="Thời gian thử việc"
        class="col-span-12 md:col-span-2"
        :required="true"
        :error="errors.probationDuration"
      >
        <div class="input input-bordered w-full flex space-x pr-0">
          <input
            type="number"
            :min="0"
            class="h-full w-full"
            placeholder="Nhập thời gian thử việc"
            :value="probationDuration"
            @change="onProbationDurationChange"
          />
          <div class="text-xs px-2 h-full text-center rounded-r-lg leading-9 bg-slate-200 font-normal">tháng</div>
        </div>
      </FormItem>
      <FormItem
        v-if="degreeFormOpen"
        label="Bằng cấp"
        class="col-span-12 md:col-span-2"
        :required="true"
        :error="errors.degree"
      >
        <JobDegreeSelect v-model="degree" />
      </FormItem>
      <FormItem
        v-if="genderFormOpen"
        label="Yêu cầu giới tính"
        class="col-span-12 md:col-span-2"
        :required="true"
        :error="errors.gender"
      >
        <JobGenderSelect v-model="gender" />
      </FormItem>
      <div
        v-if="ageFormOpen"
        class="flex col-span-12 md:col-span-4"
      >
        <FormItem
          label="Tuổi tối thiểu"
          :required="true"
          :error="errors.minAge"
        >
          <div class="input input-bordered w-full flex space-x pr-0">
            <input
              type="number"
              :min="0"
              class="h-full w-full"
              placeholder="Nhập tuổi tối thiểu"
              :value="minAge"
              @change="onMinAgeChange"
            />
            <div class="text-xs px-2 h-full text-center leading-9 rounded-r-lg bg-slate-200 font-normal">tuổi</div>
          </div>
        </FormItem>
        <FormItem :label="''">
          <div class="mx-2 h-10 leading-10">-</div>
        </FormItem>
        <FormItem
          label="Tuổi tối đa"
          required
          :error="errors.maxAge"
        >
          <div class="input input-bordered w-full flex space-x pr-0">
            <input
              type="number"
              :min="0"
              class="h-full w-full"
              placeholder="Nhập tuổi tối đa"
              :value="maxAge"
              @change="onMaxAgeChange"
            />
            <div class="text-xs px-2 h-full text-center leading-9 rounded-r-lg bg-slate-200 font-normal">tuổi</div>
          </div>
        </FormItem>
      </div>
    </div>
    <div class="flex justify-start items-center flex-wrap md:gap-8 md:mb-10">
      <AddingButton
        v-if="!probationFormOpen"
        label="Thời gian thử việc"
        @click="probationFormOpen = true"
      />
      <AddingButton
        v-if="!degreeFormOpen"
        label="Yêu cầu bằng cấp"
        @click="degreeFormOpen = true"
      />
      <AddingButton
        v-if="!genderFormOpen"
        label="Yêu cầu giới tính"
        @click="genderFormOpen = true"
      />
      <AddingButton
        v-if="!ageFormOpen"
        label="Yêu cầu độ tuổi"
        @click="ageFormOpen = true"
      />
    </div>
    <div class="text-base font-semibold mb-4">Công việc chi tiết</div>
    <FormItem
      label="Mô tả công việc"
      :required="true"
      :error="errors.description"
    >
      <RichEditor v-model:model-value="description" />
    </FormItem>
    <FormItem
      label="Yêu cầu công việc"
      :required="true"
      :error="errors.jobRequirement"
    >
      <RichEditor v-model:model-value="jobRequirement" />
    </FormItem>
    <FormItem
      label="Quyền lợi"
      :required="true"
      :error="errors.jobBenefit"
    >
      <RichEditor v-model:model-value="jobBenefit" />
    </FormItem>
    <div class="col-span-12 md:col-span-3 flex items-end">
      <AddingButton
        v-if="!skillFormOpen"
        label="Kỹ năng cần thiết"
        @click="skillFormOpen = true"
      />
    </div>
    <FormItem
      v-if="skillFormOpen"
      label="Kỹ năng cần thiết"
      :error="errors.skills"
    >
      <SkillMultipleTagsSelect v-model="skills" />
    </FormItem>
    <div class="text-base font-semibold mb-4">Thông tin liên hệ</div>
    <div class="grid grid-cols-12 gap-2">
      <FormItem
        label="Họ và tên"
        class="col-span-12 md:col-span-3 xl:col-span-2"
        required
        :error="contactName.errorMessage.value"
      >
        <input
          type="text"
          :value="contactName.value.value"
          class="input input-bordered w-full"
          placeholder="Nhập tên người liên hệ"
          @change="onReferenceNameChange"
        />
      </FormItem>
      <FormItem
        label="Email nhận hồ sơ"
        class="col-span-12 md:col-span-3 xl:col-span-2"
        required
        :error="contactEmailErrorMessage"
      >
        <input
          :value="contactEmail"
          type="text"
          class="input input-bordered w-full"
          placeholder="Nhập email"
          @change="onReferenceEmailChange"
        />
      </FormItem>
      <!-- <div class="grid grid-cols-12 gap-2 relative"> -->
      <FormItem
        v-for="(phone, idx) of contactPhones"
        :key="idx"
        label="Số điện thoại"
        class="col-span-12 md:col-span-2 xl:col-span-2"
        required
        :error="getPhoneError(idx)"
      >
        <div class="col-span-12 group relative">
          <input
            type="text"
            :value="phone.value"
            class="input input-bordered w-full"
            placeholder="Nhập số điện thoại"
            @change="onContactPhoneChange($event, idx)"
          />
          <a
            v-if="idx > 0"
            href="javascript:void(0)"
            class="absolute right-0 -top-2 bg-white hidden group-hover:block"
            @click="onRemoveContactPhone(idx)"
          >
            <IconTrash class="w-4 h-4 text-red-500" />
          </a>
        </div>
      </FormItem>
      <FormItem
        :label="''"
        class="col-span-2"
      >
        <AddingButton
          v-if="contactPhones?.length < 3"
          class="md:mb-9"
          label="Thêm số"
          @click="onAddNewContactPhone"
        />
      </FormItem>
    </div>
    <FormItem
      label="Địa điểm phỏng vấn"
      :required="true"
      :error="errors.interviewAddress"
    >
      <input
        :value="interviewAddress"
        type="text"
        class="input input-bordered w-full"
        @change="onInterviewAddressChange"
      />
    </FormItem>
    <div class="flex justify-end items-center">
      <div class="w-full">
        <div class="space-x-3 flex justify-between items-center">
          <div>
            <NuxtLink
              class="btn btn-ghost btn-link"
              to="/employer/jobs/list"
            >
              <IconsIconClearRound class="w-4 h-4" />
              Hủy
            </NuxtLink>
          </div>
          <!-- <button class="btn btn-soft btn-secondary">Xem trước</button> -->
          <div class="flex space-x-2">
            <button
              class="btn btn-outline btn-primary"
              @click="() => onSubmit(ERecruitmentStatus.DRAFT)"
            >
              Lưu nháp
            </button>
            <button
              class="btn btn-primary"
              :disabled="loading"
              @click="() => onSubmit(ERecruitmentStatus.PENDING)"
            >
              Lưu và gửi duyệt
            </button>
          </div>
        </div>
        <!-- <span class="text-xs text-gray-500">
          Bằng việc nhấn 'Lưu và tiếp tục', bạn xác nhận đồng ý với các
          <NuxtLink
            class="text-blue-500"
            target="_blank"
            to="dieu-khoan-su-dung"
            >điều kiện và điều khoản sử dụng</NuxtLink
          >
          với Vieclamlamdong.site
        </span> -->
      </div>
    </div>
  </Collapse>
</template>
<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/yup';
import { useForm } from 'vee-validate';
import { array, date, number, object, string } from 'yup';
import moment from 'moment';

import AvatarUploader from '~/components/common/AvatarUploader.vue';
import CheckBox from '~/components/common/CheckBox.vue';
import Collapse from '~/components/common/collapse.vue';
import Divider from '~/components/common/Divider.vue';
import AddingButton from '~/components/employer/common/AddingButton.vue';
import FormItem from '~/components/form/formItem.vue';
import IconTrash from '~/components/icons/IconTrash.vue';
import DistrictSelect from '~/components/resume/DistrictSelect.vue';
import JobDegreeSelect from '~/components/resume/GeneralInformation/JobDegreeSelect.vue';
import JobExperienceSelect from '~/components/resume/GeneralInformation/JobExperienceSelect.vue';
import DragDropImageUploader from '~/components/common/DragDropImageUploader.vue';
import JobGenderSelect from '~/components/resume/GeneralInformation/JobGenderSelect.vue';
import JobLevelSelect from '~/components/resume/GeneralInformation/JobLevelSelect.vue';
import JobMethodSelect from '~/components/resume/GeneralInformation/JobMethodSelect.vue';
import OccupationMultipleSelect from '~/components/resume/GeneralInformation/OccupationMultipleSelect.vue';
import SkillMultipleTagsSelect from '~/components/resume/GeneralInformation/SkillMultipleTagsSelect.vue';
import ProvinceSelect from '~/components/resume/ProvinceSelect.vue';
import RichEditor from '~/components/common/RichEditor.vue';
import { ToastType, useToast } from '~/store/toast';
import { ERecruitmentStatus, TinyInt, type Company, type Recruitment } from '~/types';
import type { UnwrapRef } from 'vue';
import { useCommonStore } from '~/store/common.store';

const emits = defineEmits(['submit']);

const rawJson = ref<string>('');
const rawProvinceId = ref<number | null>(null);
const rawDistrictId = ref<number | null>(null);

export interface IPayload {
  name: string;
  title: string | null;
  logo: string | null;
  companyName: string | null;
  contentHtml: string | null;
  descriptionHtml: string | null;
  requirementHtml: string | null;
  whyWorkingHereHtml: string | null;
  benefitHtml: string | null;
  isActive: TinyInt;
  isPublish: TinyInt;
  degreeId: number | null;
  experienceId: number | null;
  genderId: number | null;
  levelId: number | null;
  methodId: number | null;
  salaryId: number | null;
  companyId: number | null;
  minAge: number | null;
  maxAge: number | null;
  minSalary: number | null;
  maxSalary: number | null;
  skills: string[] | null;
  contactEmail: string | null;
  contactName: string | null;
  contactPhones: string[] | null;
  contactAddress: string | null;
  occupationIds: number[] | null;
  workspaces: IAddress[];
  vacancyQuantity: number | null;
  applyExpiredAt: string | null;
  probationDuration: number | null;
  salaryNegotiable: TinyInt;
  status: ERecruitmentStatus;
}
const props = defineProps({
  recruitment: {
    type: Object as PropType<Recruitment> | undefined | null,
    default: null,
  },
  company: {
    type: Object as PropType<Company> | undefined | null,
    default: null,
  },
  defaultContactName: {
    type: String,
    default: '',
  },
  defaultContactEmail: {
    type: String,
    default: '',
  },
  defaultContactPhones: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
});
const formTitle = computed<string>(() => {
  return props.recruitment ? 'Chỉnh sửa tin tuyển dụng' : 'Tạo tin tuyển dụng';
});
const defaultAddress: IAddress = {
  provinceId: null,
  districtId: null,
  address: '',
};

interface IAddress {
  provinceId: number | null;
  districtId: number | null;
  address: string | null;
}
interface IContact {
  name: string | null;
  email: string | null;
  phones: string[];
}
interface FormState {
  title: string | null;
  logo: string | null;
  companyName: string | null;
  probationDuration: number | null;
  experience: number | null;
  occupations: number[];
  method: number | null;
  level: number | null;
  minSalary: number | null;
  maxSalary: number | null;
  salaryNegotiable: boolean;
  description: string | null;
  jobRequirement: string | null;
  jobBenefit: string | null;
  skills: string[];
  minAge: number | null;
  maxAge: number | null;
  vacancy: number | null;
  expiredAt: string | null;
  contact: IContact;
  interviewAddress: string | null;
  addresses: IAddress[];
  gender: number | null;
  degree: number | null;
}
const probationFormOpen = ref<boolean>(false);
const degreeFormOpen = ref<boolean>(false);
const genderFormOpen = ref<boolean>(false);
const ageFormOpen = ref<boolean>(false);
const skillFormOpen = ref<boolean>(false);

const initialValues: UnwrapRef<FormState> = reactive({
  title: 'Trưởng Phòng Kinh Doanh Bất Động Sản - Phỏng Vấn Đi Làm Ngay',
  logo: null,
  companyName: null,
  probationDuration: 2,
  experience: 4,
  occupations: [1, 10, 33],
  method: 2,
  level: 4,
  minSalary: 12,
  maxSalary: 35,
  salaryNegotiable: false,
  description: 'Mô tả công việc',
  jobRequirement: 'Yêu cầu công việc',
  jobBenefit: 'Quyền lợi',
  skills: ['nodejs', 'reactjs'],
  minAge: 20,
  maxAge: 35,
  contact: {
    name: 'Nguyễn Văn A',
    email: '<EMAIL>',
    phones: ['0901234567', '0912345678'],
  },
  interviewAddress: '123 Điện Biên Phủ, Quận 1, TP.HCM',
  vacancy: 14,
  expiredAt: moment().add(1, 'month').format('YYYY-MM-DD'),
  addresses: [
    {
      provinceId: 1563,
      districtId: 1573,
      address: '123 Điện Biên Phủ, Quận 1, TP.HCM',
    },
  ],
  degree: 1,
  gender: 3,
});

// const initialValues: UnwrapRef<FormState> = reactive({
//   title: '',
//   probationDuration: null,
//   experience: null,
//   occupations: [],
//   method: null,
//   level: null,
//   minSalary: null,
//   maxSalary: null,
//   salaryNegotiable: false,
//   description: '',
//   jobRequirement: '',
//   jobBenefit: '',
//   skills: [],
//   minAge: null,
//   maxAge: null,
//   contact: {
//     name: null,
//     email: null,
//     phones: [],
//   },
//   interviewAddress: '',
//   vacancy: null,
//   expiredAt: null,
//   addresses: [
//     {
//       provinceId: null,
//       districtId: null,
//       address: '',
//     },
//   ],
//   degree: -1,
//   gender: -1,
// });

const { values, handleSubmit, errors, defineField } = useForm<FormState>({
  validationSchema: toTypedSchema(
    object({
      title: string().required('Yêu cầu nhập tiêu đề'),
      logo: string().nullable(),
      companyName: string().nullable(),
      probationDuration: number()
        .nullable()
        .test('validate-probationDuration', 'Thời gian thử việc phải lớn hơn hoặc bằng 0', function (value) {
          if (!value && !probationFormOpen.value) {
            return true;
          }
          if (!value) {
            return false;
          }
          return value >= 0;
        })
        .min(0, 'Thời gian thử việc phải lớn hơn hoặc bằng 0')
        .integer('Thời gian thử việc phải là số nguyên'),
      gender: number()
        .nullable()
        .test('is-gender-empty', 'Yêu cầu chọn giới tính', function (value) {
          if (!value && !genderFormOpen.value) {
            return true;
          }
          if (!value) {
            return false;
          }
          return value > 0;
        }),
      degree: number()
        .nullable()
        .test('validate-degree', 'Yêu cầu chọn bằng cấp', function (value) {
          if (!value && !degreeFormOpen.value) {
            return true;
          }
          if (!value) {
            return false;
          }
          return value > 0;
        }),
      experience: number().required('Yêu cầu chọn kinh nghiệm'),
      occupations: array().required('Yêu cầu chọn ngành nghề'),
      method: number().required('Yêu cầu chọn hình thức làm việc'),
      level: number().required('Yêu cầu chọn cấp bậc'),
      minSalary: number()
        .nullable()
        .test('is-min-salary-empty', 'Lương tối thiểu không được rỗng', function (value) {
          const { salaryNegotiable } = this.parent;
          if (salaryNegotiable) {
            return true;
          }
          if (!value && !ageFormOpen.value) {
            return true;
          }
          if (!value) {
            return false;
          }
          return true;
        })
        .test('is-greater', 'Lương tối thiểu phải lớn hơn 0', function (value) {
          const { salaryNegotiable } = this.parent;
          if (salaryNegotiable) {
            return true;
          }
          if (!value) {
            return false;
          }
          return value > 0;
        }),
      // .min(1, 'Lương tối thiểu phải lớn hơn 0'),
      maxSalary: number()
        .nullable()
        // .min(1, 'Lương tối đa phải lớn hơn 0')
        // .integer('Tuổi tối đa phải là số nguyên')
        .test('is-greater', 'Tuổi tối đa phải lớn hơn tuổi tối thiểu', function (value) {
          const { minSalary, salaryNegotiable } = this.parent;
          if (salaryNegotiable) {
            return true;
          }
          if (!minSalary) {
            return true;
          }
          if (!value) {
            return true;
          }
          return value >= minSalary;
        })
        .test('is-min-value-of-max-salary', 'Lương tối đa phải lớn hơn 0', function (value) {
          const { salaryNegotiable } = this.parent;
          if (salaryNegotiable) {
            return true;
          }
          if (!value && !ageFormOpen.value) {
            return true;
          }
          if (!value) {
            return false;
          }
          return value > 0;
        }),
      description: string()
        .required('Yêu cầu nhập mô tả công việc')
        .test('require-desc', 'Yêu cầu nhập mô tả công việc', function (value) {
          if (!value) {
            return false;
          }
          return value?.length > 0;
        }),
      jobRequirement: string()
        .required('Yêu cầu nhập yêu cầu công việc')
        .test('require-job-requirement', 'Yêu cầu nhập yêu cầu công việc', function (value) {
          if (!value) {
            return false;
          }
          return value?.length > 0;
        }),
      jobBenefit: string()
        .required('Yêu cầu nhập quyền lợi')
        .test('require-job-benefit', 'Yêu cầu nhập quyền lợi', function (value) {
          if (!value) {
            return false;
          }
          return value?.length > 0;
        }),
      skills: array().optional(),
      minAge: number()
        .nullable()
        .test('is-min-age-empty', 'Tuổi tối thiểu không được rỗng', (value) => {
          if (!value && !ageFormOpen.value) {
            return true;
          }
          if (!value) {
            return false;
          }
          return true;
        })
        .min(1, 'Tuổi tối thiểu phải lớn hơn 0')
        .integer('Tuổi tối thiểu phải là số nguyên'),
      maxAge: number()
        .nullable()
        .test('is-max-age-empty', 'Tuổi tối đa không được rỗng', (value) => {
          if (!value && !ageFormOpen.value) {
            return true;
          }
          if (!value) {
            return false;
          }
          return true;
        })
        .min(1, 'Tuổi tối đa phải lớn hơn 0')
        .test('is-greater', 'Tuổi tối đa phải lớn hơn tuổi tối thiểu', function (value) {
          const { minAge } = this.parent;
          if (!minAge) {
            return true;
          }

          if (!value) {
            return true;
          }
          return value > minAge;
        }),
      contact: object({
        name: string().required('Yêu cầu nhập tên người liên hệ'),
        email: string().email('Email không hợp lệ. Vui lòng nhập đúng định dạng email').required('Yêu cầu nhập email'),
        phones: array().min(1, 'Không để trống').of(string().required('Không để trống')),
      }).required('Yêu cầu nhập thông tin liên hệ'),
      interviewAddress: string().required('Yêu cầu nhập địa chỉ phỏng vấn'),
      vacancy: number().required('Yêu cầu nhập số lượng tuyển'),
      expiredAt: date().required('Yêu cầu nhập hạn nộp hồ sơ'),
      addresses: array()
        .min(1, 'Yêu cầu nhập ít nhất 1 địa điểm làm việc')
        .of(
          object({
            provinceId: number().optional().required('Yêu cầu chọn tỉnh/thành phố'),
            districtId: number().required('Yêu cầu chọn quận/huyện'),
            address: string().required('Yêu cầu nhập địa chỉ chi tiết'),
          }),
        )
        .default([
          {
            provinceId: -1,
            districtId: -1,
            address: '',
          },
        ]),
    }),
  ),
  initialValues: {
    // ...initialValues,
    // contact: {
    //   name: props.defaultContactName,
    //   email: props.defaultContactEmail,
    //   phones: props.defaultContactPhones,
    // },
  },
});

const [title] = defineField('title');
const [probationDuration] = defineField('probationDuration');
const [experience] = defineField('experience');
const [occupations] = defineField('occupations');
const [method] = defineField('method');
const [level] = defineField('level');
const [minSalary] = defineField('minSalary');
const [maxSalary] = defineField('maxSalary');
const [salaryNegotiable] = defineField('salaryNegotiable');
const [description] = defineField('description');
const [jobRequirement] = defineField('jobRequirement');
const [jobBenefit] = defineField('jobBenefit');
const [skills] = defineField('skills');
const [minAge] = defineField('minAge');
const [maxAge] = defineField('maxAge');
const [interviewAddress] = defineField('interviewAddress');
const [vacancy] = defineField('vacancy');
const [expiredAt] = defineField('expiredAt');
const [addresses] = defineField('addresses');
const [gender] = defineField('gender');
const [degree] = defineField('degree');
const [logo] = defineField('logo');
const [companyName] = defineField('companyName');
const contactName = useField<string>('contact.name');

const { value: contactEmail, errorMessage: contactEmailErrorMessage } = useField<string>('contact.email');

const {
  fields: contactPhones,
  push: addContactPhone,
  remove: removeContactPhone,
  update: updateContactPhones,
  replace: replaceContactPhones,
} = useFieldArray<string>('contact.phones');

const MAX_ADDRESS = 5;
const onAddNewLocation = () => {
  if (addresses.value.length < MAX_ADDRESS) {
    addresses.value.push({
      ...defaultAddress,
    });
  }
};
const getPhoneError = (idx: number) => {
  const key: string = `contact.phones[${idx}]`;
  return (errors.value as any)[key];
};
const onRemoveAddress = (idx: number) => {
  addresses.value.splice(idx, 1);
};
const onAddressChange = (idx: number, e: Event) => {
  const target = e.target as HTMLInputElement;
  addresses.value[idx].address = target.value;
};
const onReferenceNameChange = (e: Event) => {
  const target = e.target as HTMLInputElement;
  contactName.value.value = target.value;
};
const onReferenceEmailChange = (e: Event) => {
  const target = e.target as HTMLInputElement;
  contactEmail.value = target.value;
};
const onContactPhoneChange = (e: Event, idx: number) => {
  const target = e.target as HTMLInputElement;
  updateContactPhones(idx, target.value);
};
const onRemoveContactPhone = (idx: number) => {
  removeContactPhone(idx);
};
const onAddNewContactPhone = () => {
  if (!contactPhones.value) {
    contactPhones.value = [];
  }
  if (contactPhones.value.length >= 3) {
    return;
  }
  addContactPhone('');
};
const onInterviewAddressChange = (e: Event) => {
  const target = e.target as HTMLInputElement;
  interviewAddress.value = target.value;
};
const onMinSalaryChange = (e: Event) => {
  const target = e.target as HTMLInputElement;
  minSalary.value = Number(target.value);
};

const onMaxSalaryChange = (e: Event) => {
  const target = e.target as HTMLInputElement;
  maxSalary.value = Number(target.value);
};
const onMinAgeChange = (e: Event) => {
  const target = e.target as HTMLInputElement;
  minAge.value = Number(target.value);
};

const onMaxAgeChange = (e: Event) => {
  const target = e.target as HTMLInputElement;
  maxAge.value = Number(target.value);
};

const onTitleChange = (e: Event) => {
  const target = e.target as HTMLInputElement;
  title.value = target.value;
};
const onProbationDurationChange = (e: Event) => {
  const target = e.target as HTMLInputElement;
  probationDuration.value = Number(target.value);
};

const onVacancyChange = (e: Event) => {
  const target = e.target as HTMLInputElement;
  vacancy.value = Number(target.value);
};
const onExpiredAtChange = (e: Event) => {
  const target = e.target as HTMLInputElement;
  expiredAt.value = target.value;
};

const onSubmit = (status: ERecruitmentStatus) =>
  handleSubmit((values) => {
    if (!props.company) {
      useToast().showToast('Vui lòng cập nhật thông tin công ty trước khi tạo tin tuyển dụng', ToastType.Error);
      return;
    }
    const body: IPayload = {
      // : title.value,
      logo: logo.value,
      companyName: companyName.value,
      title: title.value,
      contentHtml: description.value,
      descriptionHtml: description.value,
      requirementHtml: jobRequirement.value,
      whyWorkingHereHtml: '',
      benefitHtml: jobBenefit.value,
      isActive: TinyInt.Yes,
      isPublish: TinyInt.No,
      degreeId: degree.value,
      experienceId: experience.value,
      genderId: gender.value,
      methodId: method.value,
      levelId: level.value,
      minSalary: minSalary.value,
      maxSalary: maxSalary.value,
      companyId: props.company?.id,
      contactName: contactName.value.value,
      contactEmail: contactEmail.value,
      contactPhones: contactPhones.value.map((phone) => phone.value),
      contactAddress: interviewAddress.value,
      occupationIds: occupations.value,
      workspaces: addresses.value,
      vacancyQuantity: vacancy.value,
      name: title.value || '',
      salaryId: -1,
      minAge: minAge.value,
      maxAge: maxAge.value,
      skills: skills.value,
      applyExpiredAt: expiredAt.value,
      probationDuration: probationDuration.value,
      salaryNegotiable: +salaryNegotiable.value,
      status,
    };
    if (!body.companyId) {
      useToast().showToast('Vui lòng cập nhật thông tin công ty trước khi tạo tin tuyển dụng', ToastType.Error);
      return;
    }
    emits('submit', body);
  })();

const mapRecruitmentToState = (recruitment: Recruitment) => {
  if (!recruitment) {
    return;
  }
  title.value = recruitment.title;
  experience.value = recruitment.jobExperienceId;
  occupations.value = recruitment.occupations?.map((occ) => occ.id) || [];
  method.value = recruitment.jobMethodId;
  level.value = recruitment.jobLevelId;
  minSalary.value = recruitment.minSalary;
  maxSalary.value = recruitment.maxSalary;
  salaryNegotiable.value = tinyintToBoolean(recruitment.salaryNegotiable);
  description.value = recruitment.descriptionHtml;
  jobRequirement.value = recruitment.requirementHtml;
  jobBenefit.value = recruitment.benefitHtml;
  logo.value = recruitment.logo;
  companyName.value = recruitment.companyName;
  if (recruitment.probationDuration) {
    probationDuration.value = recruitment.probationDuration;
    probationFormOpen.value = true;
  }
  // skills.value = ['nodejs', 'javascript']
  if (recruitment.skills?.length) {
    skills.value = recruitment.skills || [];
    skillFormOpen.value = true;
  }
  if (recruitment.minAge || recruitment.maxAge) {
    minAge.value = recruitment.minAge;
    maxAge.value = recruitment.maxAge;
    ageFormOpen.value = true;
  }
  vacancy.value = recruitment.vacancyQuantity;
  if (recruitment.applyExpiredAt) {
    expiredAt.value = moment(recruitment.applyExpiredAt).toISOString();
  }
  contactName.setValue(recruitment.contactName);
  contactEmail.value = recruitment.contactEmail;
  if (recruitment.contactPhone) {
    replaceContactPhones([recruitment.contactPhone]);
  }
  interviewAddress.value = recruitment.contactAddress;
  addresses.value = recruitment.workspaces?.map((wp) => {
    return {
      districtId: wp.districtId,
      provinceId: wp.provinceId,
      address: wp.address,
    };
  }) || [
    {
      districtId: null,
      provinceId: null,
      address: '',
    },
  ];
  if (recruitment.jobGenderId) {
    gender.value = recruitment.jobGenderId;
    genderFormOpen.value = true;
  }
  if (recruitment.jobDegreeId) {
    degree.value = recruitment.jobDegreeId;
    degreeFormOpen.value = true;
  }
};
const mapContactInfoPropsToState = (
  name: string | undefined,
  email: string | undefined,
  phones: string[] | undefined,
) => {
  if (name) {
    contactName.value.value = name;
  }
  if (email) {
    contactEmail.value = email;
  }
  if (phones?.length) {
    // contactPhones.value = phones;
    replaceContactPhones(phones);
  }
};
const mapPropsToState = () => {
  mapRecruitmentToState(props.recruitment);
  mapContactInfoPropsToState(props.defaultContactName, props.defaultContactEmail, props.defaultContactPhones);
};

const onParse = () => {
  try {
    const json: FormState = JSON.parse(rawJson.value);
    title.value = json.title;
    companyName.value = json.companyName;
    probationDuration.value = json.probationDuration;
    experience.value = json.experience;
    occupations.value = json.occupations;
    method.value = json.method;
    level.value = json.level;
    minSalary.value = json.minSalary;
    maxSalary.value = json.maxSalary;
    salaryNegotiable.value = json.salaryNegotiable;
    description.value = json.description;
    jobRequirement.value = json.jobRequirement;
    jobBenefit.value = json.jobBenefit;
    skills.value = json.skills;
    minAge.value = json.minAge;
    maxAge.value = json.maxAge;
    interviewAddress.value = json.interviewAddress;
    vacancy.value = json.vacancy;
    expiredAt.value = json.expiredAt || moment().add(1, 'month').toISOString();
    addresses.value = json.addresses?.map((item) => {
      return {
        provinceId: rawProvinceId.value || item.provinceId,
        districtId: rawDistrictId.value || item.districtId,
        address: item.address,
      };
    });
    if (!addresses.value?.length) {
      addresses.value = [
        {
          provinceId: rawProvinceId.value,
          districtId: rawDistrictId.value,
          address: '',
        },
      ];
    }
    gender.value = json.gender;
    degree.value = json.degree;
    contactName.setValue(json.contact?.name || 'supporter');
    contactEmail.value = json.contact?.email || '<EMAIL>';
    replaceContactPhones(
      json.contact?.phones?.map((phone) => phone) || [
        {
          value: '',
        },
      ],
    );
    if (probationDuration.value) {
      probationFormOpen.value = true;
    } else {
      probationFormOpen.value = false;
    }

    if (skills.value?.length) {
      skillFormOpen.value = true;
    } else {
      skillFormOpen.value = false;
    }
    if (minAge.value || maxAge.value) {
      ageFormOpen.value = true;
    } else {
      ageFormOpen.value = false;
    }
    if (gender.value) {
      genderFormOpen.value = true;
    } else {
      genderFormOpen.value = false;
    }

    if (degree.value) {
      degreeFormOpen.value = true;
    } else {
      degreeFormOpen.value = false;
    }
  } catch (error: any) {
    useToast().showToast('Vui lòng nhập đúng định dạng: ' + error?.message, ToastType.Error);
  }
};

const onShortcutKey = (e: KeyboardEvent) => {
  // short key: meta + j
  const isCmdOrCtrl = e.metaKey || e.ctrlKey;
  const isJKeyPressed = e.key === 'j';
  if (isCmdOrCtrl && isJKeyPressed) {
    e.preventDefault();
    onSubmit(ERecruitmentStatus.PENDING);
    return;
  }
};

const onInitListenKeyUp = () => {
// meta + j to submit form
  window.addEventListener('keydown', onShortcutKey, true);
}

const onRemoveListenKeyUp = () => {
  window.removeEventListener('keydown', onShortcutKey, true);
}

onMounted(() => {
  mapPropsToState();
  onInitListenKeyUp();
  // initQuillEditor();
});

onBeforeUnmount(() => {
  onRemoveListenKeyUp();
});

watch(
  () => props.defaultContactName,
  (value, oldValue) => {
    mapContactInfoPropsToState(value, undefined, undefined);
  },
);

watch(
  () => props.defaultContactEmail,
  (value, oldValue) => {
    mapContactInfoPropsToState(undefined, value, undefined);
  },
);

watch(
  () => props.defaultContactPhones,
  (value, oldValue) => {
    mapContactInfoPropsToState(undefined, undefined, value);
  },
);

watch(
  () => props.recruitment,
  (value) => {
    mapRecruitmentToState(value);
  },
);
</script>
