<template>
  <RecruitmentLink
    :id="job.id"
    :slug="job.slug"
  >
    <div class="border border-base-300 p-1 my-1.5 rounded-sm cursor-pointer bg-green-50 lg:bg-white hover:border-info">
      <div class="flex justify-between">
        <div class="flex justify-start items-center">
          <div>
            <div class="avatar border border-none mr-6 p-1">
              <div class="rounded-md w-16 lg:w-20">
                <img alt="vietlamlamdong.site" :src="job.logo ? job.logo : job.company?.logo || ''" />
              </div>
            </div>
          </div>
          <div class="my-auto">
            <div class="w-full text-xs mb-1 line-clamp-2 text-gray-800 font-light lg:text-base">
              {{ job.title }}
            </div>
            <div class="text-xs text-gray-500 font-light lg:text-sm lg:mb-2">
              {{ job.companyName || job.company?.name }}
            </div>
            <div class="flex justify-start flex-col font-light lg:flex-row">
              <div class="flex justify-start items-center mt-1">
                <span class="text-gray-700 w-5 text-left">
                  <IconMoney class="w-5 h-5" />
                </span>
                <span class="text-xs px-2 text-info lg:text-sm">
                  <template v-if="job.salaryNegotiable"> Thỏa thuận </template>
                  <template v-else>
                    {{ formatSalaryRange(job.minSalary, job.maxSalary, 1000000) }}
                  </template>
                </span>
              </div>
              <div class="flex justify-start items-center mt-1">
                <span class="text-gray-700 w-5 text-left">
                  <IconLocation class="w-5 h-5" />
                </span>
                <span class="text-xs px-2 lg:text-sm">{{ briefJobWorkspaces(job) }}</span>
              </div>
              <div class="flex justify-start items-center mt-1 lg:text-sm">
                <span class="text-gray-700 w-5 text-left">
                  <IconCalendar class="w-5 h-5" />
                </span>
                <span class="text-xs px-2 lg:text-sm">{{ formatDateOnly(job.applyExpiredAt) }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="text-info">
          <a
            href="javascript:void(0)"
            @click.prevent="onFavorite"
          >
            <IconHeartFil
              v-if="favorite"
              class="w-6 h-6"
            />
            <IconHeartOutline
              v-else
              class="w-6 h-6"
            />
          </a>
        </div>
      </div>
      <!-- <Divider /> -->
      <div class="flex justify-between items-center border-t border-gray-200 py-1 mt-2">
        <div class="flex items-center">
          <div v-if="job.urgent">
            <HotTag />
          </div>
        </div>
        <div class="flex items-center">
          <IconClock24 class="w-4 h-4 text-gray-500" />
          <span class="ml-2 text-xs font-light">
            <TimeLeft :date-time="job.applyExpiredAt" />
          </span>
        </div>
      </div>
    </div>
  </RecruitmentLink>
</template>

<script setup lang="ts">
import RecruitmentLink from '~/components/link/recruitmentLink.vue';
import { useCommonStore } from '~/store/common.store';
import type { Recruitment } from '~/types';
import Divider from '../common/Divider.vue';
import TimeLeft from '../common/TimeLeft.vue';
import IconCalendar from '../icons/IconCalendar.vue';
import IconClock24 from '../icons/IconClock24.vue';
import IconHeartFil from '../icons/IconHeartFil.vue';
import IconHeartOutline from '../icons/IconHeartOutline.vue';
import IconLocation from '../icons/IconLocation.vue';
import IconMoney from '../icons/IconMoney.vue';
import HotTag from '../job/HotTag.vue';
const { provinces, districts } = storeToRefs(useCommonStore());
const emits = defineEmits(['favorite']);

const props = defineProps({
  job: {
    type: Object as PropType<Recruitment>,
    required: true,
  },
  favorite: {
    type: Boolean as PropType<boolean>,
    default: false,
  },
});

function briefJobWorkspaces(job: Recruitment) {
  return job.workspaces
    ?.map((wp) => {
      const province = provinces.value.find((province) => province.id === wp.provinceId);
      const district = districts.value.find((district) => district.id === wp.districtId);
      return `${district?.name}, ${province?.name}`;
    })
    .splice(0, 1)
    .join(', ');
}

function formatSalary(salary: number, unit: number) {
  const salaryInUnit = salary / unit;
  return salaryInUnit.toFixed(0);
}

function formatSalaryRange(minSalary: number, maxSalary: number, unit: number) {
  return `${formatSalary(minSalary, unit)} - ${formatSalary(maxSalary, unit)} triệu`;
}

const onFavorite = () => {
  emits('favorite', {
    resumeId: props.job.id,
    favorite: !props.favorite,
  });
};
</script>
