<template>
  <div class="rounded-md shadow-md p-6 bg-white">
    <div class="flex text-sm font-light mb-2 text-nowrap">
      <NuxtLink to="/"> Trang chủ </NuxtLink>
      {{ ' / ' }}
      <div class="text-gray-400 line-clamp-1">
        <PERSON><PERSON><PERSON><PERSON> {{ total }} Việc Làm
        {{
          findOccupationByIds(filters.occupationIds || [])
            .map((occ) => occ.name)
            .join(',')
        }}
      </div>
    </div>

    <div class="text-2xl leading-10">
      <span class="font-light"><PERSON>y<PERSON><PERSON> dụng</span>
      {{ ' ' }}
      <span class="text-info font-semibold">{{ total }}</span>
      {{ ' ' }}
      <span class="font-light">việc làm</span>
      {{ ' ' }}
      <b>
        {{
          findOccupationByIds(filters.occupationIds || [])
            .map((occ) => occ.name)
            .join(', ')
        }}
      </b>
      <span> mới nhất năm <span class="text-info font-semibold">2025</span></span>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useCommonStore } from '~/store/common.store';
import { useJobBrowserStore } from '~/store/jobBrowser';
const { total, filters } = storeToRefs(useJobBrowserStore());
const { findOccupationByIds } = useCommonStore();
</script>
