<template>
  <div class="container">
    <div class="px-4 bg-primary py-2">
      <label class="input input-bordered flex items-center gap-2 !px-2 w-full">
        <IconSearch class="w-6" />
        <input
          type="text"
          class="grow"
          placeholder="Tì<PERSON> kiếm cơ hội việc làm"
          :value="filterModel.keyword"
          @input="onKeywordChange"
        />
        <button
          v-if="filterModel.keyword"
          class="btn btn-primary"
          @click="onSubmit"
        >
          Tìm
        </button>
      </label>
    </div>
    <div class="shadow-sm p-4 my-2 bg-white flex justify-between">
      <div>{{ total }} <span class="text-sm">đăng tin</span></div>
      <a
        class="text-info flex items-center"
        href="javascript:void(0)"
        @click="filterModalOpen = true"
      >
        <div class="relative">
          <div
            v-if="filterCount > 0"
            class="absolute -right-3 -top-1 w-4 h-4 text-xs text-white text-center bg-red-500 rounded-full"
          >
            <span>{{ filterCount }}</span>
          </div>
          <div class="text-xl">
            <IconFilter class="w-6" />
          </div>
        </div>
        <span class="ml-2">Lọc kết quả</span>
      </a>
    </div>
    <div
      id="recruitment_filter_modal"
      :class="['fixed top-0 left-0 w-full h-[100vh] bg-white', filterModalOpen ? 'z-[99]' : 'hidden']"
    >
      <div class="w-full h-full max-w-full bg-white relative p-0 rounded-none">
        <div class="flex justify-between py-4 px-4 items-center shadow-md">
          <div>
            <a
              href="javascript:void(0)"
              @click="filterModalOpen = false"
            >
              <IconClearRound class="w-6" />
            </a>
          </div>
          <div class="font-semibold text-lg">Lọc kết quả</div>
          <div>
            <a
              href="javascript:void(0)"
              class="text-info font-semibold text-md"
              @click="onResetFilter"
              >Xóa bộ lộc</a
            >
          </div>
        </div>
        <div class="max-h-full overflow-scroll px-4 pb-54">
          <FormItem label="Nghề nghiệp">
            <OccupationMultipleSelect v-model="filterModel.occupationIds" />
          </FormItem>
          <Divider />
          <FormItem label="Tỉnh thành">
            <ProvinceSelect v-model="filterModel.provinceId" />
          </FormItem>
          <FormItem label="Tỉnh thành">
            <DistrictSelect
              v-model="filterModel.districtId"
              :province-id="filterModel.provinceId"
            />
          </FormItem>
          <Divider />
          <FormItem label="Mức lương">
            <div class="flex flex-wrap gap-2">
              <JobFilterTag
                :active="filterModel.jobSalaryId == null"
                @click="() => (filterModel.jobSalaryId = null)"
                >Tất cả
              </JobFilterTag>
              <JobFilterTag
                v-for="salary in jobSalaries"
                :key="salary.id"
                :active="salary.id == filterModel.jobSalaryId"
                @click="() => (filterModel.jobSalaryId = salary.id)"
              >
                {{ salary.name }}
              </JobFilterTag>
            </div>
          </FormItem>
          <Divider />
          <FormItem label="Kinh nghiệm">
            <div class="flex flex-wrap gap-2">
              <JobFilterTag
                :active="filterModel.jobExperienceId == null"
                @click="() => (filterModel.jobExperienceId = null)"
                >Tất cả
              </JobFilterTag>
              <JobFilterTag
                v-for="jobExperience in jobExperiences"
                :key="jobExperience.id"
                :active="jobExperience.id == filterModel.jobExperienceId"
                @click="() => (filterModel.jobExperienceId = jobExperience.id)"
              >
                {{ jobExperience.name }}
              </JobFilterTag>
            </div>
          </FormItem>
          <Divider />
          <FormItem label="Cấp bậc">
            <div class="flex flex-wrap gap-2">
              <JobFilterTag
                :active="filterModel.jobLevelId == null"
                @click="() => (filterModel.jobLevelId = null)"
                >Tất cả
              </JobFilterTag>
              <JobFilterTag
                v-for="jobLevel in jobLevels"
                :key="jobLevel.id"
                :active="jobLevel.id == filterModel.jobLevelId"
                @click="() => (filterModel.jobLevelId = jobLevel.id)"
              >
                <span>
                  {{ jobLevel.name }}
                </span>
              </JobFilterTag>
            </div>
          </FormItem>
          <Divider />
          <FormItem label="Trình độ">
            <div class="flex flex-wrap gap-2">
              <JobFilterTag
                :active="filterModel.jobDegreeId == null"
                @click="() => (filterModel.jobDegreeId = null)"
                >Tất cả
              </JobFilterTag>
              <JobFilterTag
                v-for="jobDegree in jobDegrees"
                :key="jobDegree.id"
                :active="jobDegree.id == filterModel.jobDegreeId"
                @click="() => (filterModel.jobDegreeId = jobDegree.id)"
              >
                <span>
                  {{ jobDegree.name }}
                </span>
              </JobFilterTag>
            </div>
          </FormItem>
          <Divider />
          <FormItem label="Giới tính">
            <div class="flex flex-wrap gap-2">
              <JobFilterTag
                :active="filterModel.jobGenderId == null"
                @click="() => (filterModel.jobGenderId = null)"
                >Tất cả
              </JobFilterTag>
              <JobFilterTag
                v-for="jobGender in jobGenders"
                :key="jobGender.id"
                :active="jobGender.id == filterModel.jobGenderId"
                @click="() => (filterModel.jobGenderId = jobGender.id)"
              >
                <span>
                  {{ jobGender.name }}
                </span>
              </JobFilterTag>
            </div>
          </FormItem>
          <Divider />
          <FormItem label="Loại công việc">
            <div class="flex flex-wrap gap-2">
              <JobFilterTag
                :active="filterModel.jobMethodId == null"
                @click="() => (filterModel.jobMethodId = null)"
                >Tất cả
              </JobFilterTag>
              <JobFilterTag
                v-for="jobMethod in jobMethods"
                :key="jobMethod.id"
                :active="jobMethod.id == filterModel.jobMethodId"
                @click="() => (filterModel.jobMethodId = jobMethod.id)"
              >
                <span>
                  {{ jobMethod.name }}
                </span>
              </JobFilterTag>
            </div>
          </FormItem>
        </div>
        <div class="fixed bottom-0 left-0 w-full bg-white shadow-inner grid grid-cols-6 gap-4 px-2 py-3">
          <button
            class="btn !bg-[#f5f1ff] !text-primary w-full col-start-1 col-end-3"
            @click="filterModalOpen = false"
          >
            Hủy
          </button>
          <button
            class="btn btn-primary text-white w-full col-start-3 col-end-7"
            @click="onSubmit"
          >
            Áp dụng
          </button>
        </div>
      </div>
    </div>
    <FormItem
      label="Sắp xếp theo"
      class="px-4"
    >
      <div class="flex justify-start overflow-scroll space-x-2 hide-scrollbar">
        <JobFilterTag
          v-for="(sortItem, idx) in sortFiltersOptions"
          :key="idx"
          size="xs"
          :active="!!(sortItem.value == filterModel.sort)"
          @click="onSortChange(sortItem.value)"
        >
          {{ sortItem.name }}
        </JobFilterTag>
      </div>
    </FormItem>
  </div>
</template>
<script setup lang="ts">
import type { LocationQuery } from 'vue-router';
import { useCommonStore } from '~/store/common.store';
import { useJobBrowserStore, type IJobFilterOptions } from '~/store/jobBrowser';
import Divider from '../common/Divider.vue';
import FormItem from '../form/formItem.vue';
import OccupationMultipleSelect from '../resume/GeneralInformation/OccupationMultipleSelect.vue';
import ProvinceSelect from '../resume/ProvinceSelect.vue';
import DistrictSelect from '../resume/DistrictSelect.vue';
import JobFilterTag from './JobFilterTag.vue';
import IconSearch from '../icons/IconSearch.vue';
import IconFilter from '../icons/IconFilter.vue';
import IconClearRound from '../icons/IconClearRound.vue';
const {
  jobSalarys: jobSalaries,
  jobExperiences,
  jobLevels,
  jobDegrees,
  jobMethods,
  jobGenders,
} = storeToRefs(useCommonStore());

const emits = defineEmits<{
  (e: 'on-submit', payload: Partial<IJobFilterOptions>): void;
}>();

const { setAndFetchFilters } = useJobBrowserStore();

const { sortFiltersOptions, total, filters } = storeToRefs(useJobBrowserStore());

const router = useRouter();

const advancedFilterOpen = ref(true);

const filterModalOpen = ref(false);
const filterCount = ref(0);

const filterModel = ref({
  occupationIds: [] as number[],
  provinceId: null as number | null,
  districtId: null as number | null,
  jobSalaryId: null as number | null,
  jobExperienceId: null as number | null,
  jobLevelId: null as number | null,
  jobDegreeId: null as number | null,
  jobMethodId: null as number | null,
  jobGenderId: null as number | null,
  sort: null as string | null,
  keyword: null as string | null,
});

const mapFilterStateToModel = (filters: Partial<IJobFilterOptions>) => {
  const {
    occupationIds,
    provinceId,
    jobSalaryId,
    jobExperienceId,
    jobLevelId,
    jobDegreeId,
    jobMethodId,
    jobGenderId,
    sort,
    keyword,
  } = filters;
  if (occupationIds) {
    filterModel.value.occupationIds = occupationIds;
  } else {
    filterModel.value.occupationIds = [];
  }
  if (provinceId) {
    filterModel.value.provinceId = provinceId;
  } else {
    filterModel.value.provinceId = null;
  }
  if (jobSalaryId) {
    filterModel.value.jobSalaryId = jobSalaryId;
  } else {
    filterModel.value.jobSalaryId = null;
  }
  if (jobExperienceId) {
    filterModel.value.jobExperienceId = jobExperienceId;
  } else {
    filterModel.value.jobExperienceId = null;
  }
  if (jobLevelId) {
    filterModel.value.jobLevelId = jobLevelId;
  } else {
    filterModel.value.jobLevelId = null;
  }
  if (jobDegreeId) {
    filterModel.value.jobDegreeId = jobDegreeId;
  } else {
    filterModel.value.jobDegreeId = null;
  }
  if (jobMethodId) {
    filterModel.value.jobMethodId = jobMethodId;
  } else {
    filterModel.value.jobMethodId = null;
  }
  if (jobGenderId) {
    filterModel.value.jobGenderId = jobGenderId;
  } else {
    filterModel.value.jobGenderId = null;
  }
  if (sort) {
    filterModel.value.sort = sort;
  } else {
    filterModel.value.sort = null;
  }
  if (keyword) {
    filterModel.value.keyword = keyword;
  } else {
    filterModel.value.keyword = null;
  }

  filterCount.value = filterChangeCount();
};

function changeParamsByFilters(filters: Partial<IJobFilterOptions>) {
  const {
    occupationIds,
    provinceId,
    jobSalaryId,
    jobExperienceId,
    jobLevelId,
    jobDegreeId,
    jobMethodId,
    jobGenderId,
    page,
    limit,
    keyword,
    sort,
  } = filters;
  const rawObj: Record<string, any> = {
    occupationIds,
    provinceId,
    jobSalaryId,
    jobExperienceId,
    jobLevelId,
    jobDegreeId,
    jobMethodId,
    jobGenderId,
    page,
    limit,
    keyword,
    sort,
  };
  const validObj: Record<string, any> = {};
  Object.keys(rawObj).forEach((key) => {
    const value = rawObj[key];
    if (value !== null && value !== undefined && value !== -1) {
      validObj[key] = value;
    }
  });
  router.push({
    query: validObj,
  });
}

const onSubmit = () => {
  const payload: Partial<IJobFilterOptions> = {
    occupationIds: filterModel.value.occupationIds,
    provinceId: filterModel.value.provinceId || undefined,
    districtId: filterModel.value.districtId || undefined,
    jobSalaryId: filterModel.value.jobSalaryId || undefined,
    jobExperienceId: filterModel.value.jobExperienceId || undefined,
    jobLevelId: filterModel.value.jobLevelId || undefined,
    jobDegreeId: filterModel.value.jobDegreeId || undefined,
    jobMethodId: filterModel.value.jobMethodId || undefined,
    jobGenderId: filterModel.value.jobGenderId || undefined,
    keyword: filterModel.value.keyword || undefined,
    sort: filterModel.value.sort || undefined,
  };
  // console.log('onSubmit', payload);
  // changeParamsByFilters(payload);
  emits('on-submit', payload);
  filterModalOpen.value = false;
};

const onSortChange = (sort: string) => {
  filterModel.value.sort = sort;
  onSubmit();
};

const onReset = () => {
  filterModel.value = {
    occupationIds: [],
    provinceId: null,
    districtId: null,
    jobSalaryId: null,
    jobExperienceId: null,
    jobLevelId: null,
    jobDegreeId: null,
    jobMethodId: null,
    jobGenderId: null,
    sort: null,
    keyword: null,
  };
  setAndFetchFilters({
    occupationIds: [],
    provinceId: null,
    jobSalaryId: null,
    jobExperienceId: null,
    jobLevelId: null,
    jobDegreeId: null,
    jobMethodId: null,
    jobGenderId: null,
    keyword: null,
  });
  changeParamsByFilters({
    occupationIds: [],
    provinceId: null,
    jobSalaryId: null,
    jobExperienceId: null,
    jobLevelId: null,
    jobDegreeId: null,
    jobMethodId: null,
    jobGenderId: null,
    keyword: null,
  });
};

const parseFilterFromQueryParams = (locationQuery: LocationQuery): Partial<IJobFilterOptions> => {
  if (!locationQuery) {
    return {};
  }

  const {
    occupationIds,
    provinceId,
    districtId,
    jobSalaryId,
    jobExperienceId,
    jobLevelId,
    jobDegreeId,
    jobMethodId,
    jobGenderId,
    page,
    limit,
    keyword,
    sort,
  } = locationQuery as Record<string, any>;

  const partialFilter: Partial<IJobFilterOptions> = {};
  if (occupationIds) {
    if (Array.isArray(occupationIds)) {
      partialFilter.occupationIds = occupationIds.map((id: string) => parseInt(id)).filter((id: number) => !isNaN(id));
    } else {
      partialFilter.occupationIds = [parseInt(occupationIds)];
    }
  }

  if (provinceId) {
    partialFilter.provinceId = parseInt(provinceId);
  }

  if (districtId) {
    partialFilter.districtId = parseInt(districtId);
  }

  if (jobSalaryId) {
    partialFilter.jobSalaryId = parseInt(jobSalaryId);
  }

  if (jobExperienceId) {
    partialFilter.jobExperienceId = parseInt(jobExperienceId);
  }

  if (jobLevelId) {
    partialFilter.jobLevelId = parseInt(jobLevelId);
  }

  if (jobDegreeId) {
    partialFilter.jobDegreeId = parseInt(jobDegreeId);
  }

  if (jobMethodId) {
    partialFilter.jobMethodId = parseInt(jobMethodId);
  }

  if (jobGenderId) {
    partialFilter.jobGenderId = parseInt(jobGenderId);
  }
  if (page) {
    partialFilter.page = parseInt(page);
  }
  if (limit) {
    partialFilter.limit = parseInt(limit);
  }
  if (keyword) {
    partialFilter.keyword = keyword;
  }
  if (sort) {
    partialFilter.sort = sort;
  }
  return partialFilter;
};

const filterChangeCount = () => {
  let count = 0;
  if (filters.value.occupationIds && filters.value.occupationIds.length > 0) {
    count++;
  }
  if (filters.value.provinceId) {
    count++;
  }
  if (filters.value.districtId) {
    count++;
  }
  if (filters.value.jobSalaryId) {
    count++;
  }
  if (filters.value.jobExperienceId) {
    count++;
  }
  if (filters.value.jobLevelId) {
    count++;
  }
  if (filters.value.jobDegreeId) {
    count++;
  }
  if (filters.value.jobMethodId) {
    count++;
  }
  if (filters.value.jobGenderId) {
    count++;
  }
  if (filters.value.keyword) {
    count++;
  }
  return count;
};

const onKeywordChange = (e: Event) => {
  const target = e.target as HTMLInputElement;
  filterModel.value.keyword = target.value;
};

const mapQueryParamsToFilterModel = (locationQuery: LocationQuery) => {
  const partialFilter = parseFilterFromQueryParams(locationQuery);
  setAndFetchFilters(partialFilter);
  mapFilterStateToModel(partialFilter);
};

const onResetFilter = () => {
  filterModel.value = {
    occupationIds: [],
    provinceId: null,
    districtId: null,
    jobSalaryId: null,
    jobExperienceId: null,
    jobLevelId: null,
    jobDegreeId: null,
    jobMethodId: null,
    jobGenderId: null,
    sort: null,
    keyword: null,
  };
  filterCount.value = 0;
};

watch(
  () => useJobBrowserStore().filters,
  (value: IJobFilterOptions) => {
    mapFilterStateToModel(value);
  },
);

watch(
  () => useRoute().query,
  (value: LocationQuery) => {
    mapQueryParamsToFilterModel(value);
  },
);
</script>
