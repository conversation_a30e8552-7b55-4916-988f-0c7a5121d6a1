<template>
  <div
    :class="[
      'px-3 py-2.5 rounded-md border text-nowrap hover:cursor-pointer',
      parseSize(size),
      active
        ? 'bg-secondary/10 border-secondary text-info'
        : 'bg-gray-100 border-gray-100',
    ]"
  >
    <slot />
  </div>
</template>
<script setup lang="ts">
defineProps({
  active: {
    type: Boolean,
    default: false,
  },
  size: {
    type: String as PropType<"sm" | "md" | "xs">,
    default: "md",
  },
});

function parseSize(size: string) {
  switch (size) {
    case "sm":
      return "text-xs";
    case "md":
      return "text-sm";
    case "xs":
      return "text-xs";
  }
}
</script>
