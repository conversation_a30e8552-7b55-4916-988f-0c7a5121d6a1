<template>
  <div class="bg-white border-b border-b-base-200 lg:px-20">
    <div class="py-2.5 flex items-center justify-start gap-8 container">
      <a
        href="javascript:void(0)"
        class="text-primary text-2xl"
        @click="onOpenModal"
      >
        <IconAppList class="w-8 h-8" />
      </a>
      <div
        class="flex gap-2 justify-start items-center text-nowrap overflow-scroll hide-scrollbar"
      >
        <NuxtLink
          v-for="(occupation, idx) in findOccupationByIds(
            topRecruitmentByOccupation.map((item) => item.occupationId),
          )"
          :key="occupation.id"
          :to="`/occupations/${occupation.id}`"
          class="text-primary text-xs font-semibold"
        >
          <Dot
            v-if="idx !== 0"
            class="!text-gray-400"
          />
          {{ occupation.name }}
        </NuxtLink>
      </div>

      <div
        v-if="modalOpen"
        class="fixed w-full left-0 h-[100vh] z-[99999] overflow-auto top-16"
      >
        <div
          class="!bg-white overflow-hidden shadow-lg pb-4 w-full fixed z-[99]"
        >
          <div class="flex items-center my-2 mx-1">
            <a
              href="javascript:void(0)"
              class="text-gray-400 text-2xl"
              @click="onOpenModal"
            >
              <IconClearRound class="w-8 h-8" />
            </a>
            <div class="px-2 font-semibold text-base">
              Danh sách tất cả nghề nghiệp
            </div>
          </div>
          <div class="grid grid-cols-3 gap-4 px-3">
            <!-- <NuxtLink
              v-for="(occupation, idx) in occupations"
              :key="occupation.id"
              :to="`/tim-kiem-viec-lam?occupationIds=${occupation.id}`"
              class="text-[12px] font-light my-2 leading-6 text-black"
            >
              {{ occupation.name }}
            </NuxtLink> -->
            <a
              v-for="(occupation, idx) in occupations"
              :key="occupation.id"
              href="javascript:void(0)"
              class="text-[12px] font-light my-2 leading-6 text-black"
              @click.prevent="onRedirect(occupation.id)"
            >
              {{ occupation.name }}
            </a>
          </div>
        </div>
        <div class="bg-black opacity-50 fixed top-16 w-full inset-0" />
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { useCommonStore } from "~/store/common.store";
import type { Occupation } from "~/types";
import Dot from "../common/Dot.vue";
import IconAppList from "../icons/IconAppList.vue";
import IconClearRound from "../icons/IconClearRound.vue";

const { findOccupationByIds } = useCommonStore();

const { topRecruitmentByOccupation, occupations } =
  storeToRefs(useCommonStore());

// const props = defineProps({
//   occupations: {
//     type: Array as PropType<Occupation[]>,
//     required: true,
//   },
// });
const router = useRouter();
const modalOpen = ref(false);

// const topTenOccupations = ref<Occupation[]>([]);

// onMounted(() => {
//   topTenOccupations.value = props.occupations?.map((occupation: Occupation) => occupation).splice(0, 5) || [];
// });

const onOpenModal = () => {
  modalOpen.value = !modalOpen.value;

  if (modalOpen.value) {
    document.querySelector("html")?.style.setProperty("overflow", "hidden");
  } else {
    document.querySelector("html")?.style.setProperty("overflow", "auto");
  }
};

const onRedirect = (id: number) => {
  router.push(`/tim-kiem-viec-lam?occupationIds=${id}`);
  modalOpen.value = false;
};

// watch(() => props.occupations, (newVal: Occupation[]) => {
//   topTenOccupations.value = newVal?.map((occupation) => occupation).splice(0, 5) || [];
// });
</script>
