<template>
  <div class="container md:min-h-[500px]">
    <Spin :loading="loading">
      <JobBrowserItem
        v-if="list.length"
        v-for="job in list"
        :key="job.id"
        :job="job"
        :favorite="isFavorite(job.id)"
        @favorite="onFavorite"
      />
      <div
        v-else
        class="bg-white border-b border-b-base-200 py-8 mb-8 rounded-md lg:px-20"
      >
        <Empty description="Chúng tôi không tìm thấy việc nào phù hợp với tìm kiếm của bạn." />
      </div>
      <Pagination
        class="my-10"
        :total="total"
        :page-size="pagination.pageSize"
        :current-page="pagination.currentPage"
        :on-change="onPageChange"
      />
    </Spin>
  </div>
</template>
<script setup lang="ts">
import type { Recruitment } from '~/types';
import Pagination from '~/components/common/pagination.vue';
import JobBrowserItem from '~/components/browsers/JobBrowserItem.vue';
import Spin from '../common/Spin.vue';
import Empty from '~/components/common/Empty.vue';

const emits = defineEmits({
  onPageChange: (page: number) => true,
  onFavorite: (payload: { resumeId: number; favorite: boolean }) => true,
});

const props = defineProps({
  list: {
    type: Array as () => Recruitment[],
    default: () => [],
  },
  total: {
    type: Number,
    default: 0,
  },
  filters: {
    type: Object,
    default: () => ({}),
  },
  favoriteIds: {
    type: Array as () => number[],
    default: () => [],
  },
  pagination: {
    type: Object as () => {
      total: number;
      pageSize: number;
      currentPage: number;
    },
    default: () => ({
      total: 0,
      pageSize: 10,
      currentPage: 1,
    }),
  },
  loading: {
    type: Boolean,
    default: false,
  },
});

const onPageChange = (page: number) => {
  emits('onPageChange', page);
};

const onFavorite = ({ resumeId, favorite }: { resumeId: number; favorite: boolean }) => {
  emits('onFavorite', { resumeId, favorite });
};

const isFavorite = (resumeId: number) => {
  return props.favoriteIds.includes(resumeId);
};
</script>
