<template>
  <div class="container">
    <div class="pb-16">
      <div class="bg-primary w-full rounded-md my-4 relative">
        <div
          v-if="advancedFilterOpen"
          class="bg-white rounded-md px-6 py-4 flex justify-between text-nowrap items-center shadow-lg absolute -bottom-16 left-0"
        >
          <div class="text-sm text-gray-400 font-light mr-2">Lọc nâng cao:</div>
          <div class="grid grid-cols-6 gap-1">
            <JobExperienceSelect
              v-model="filterModel.jobExperienceId"
              :empty-select="true"
              @on-change="onSubmit"
            />
            <JobSalarySelect
              v-model="filterModel.jobSalaryId"
              :empty-select="true"
              @on-change="onSubmit"
            />
            <JobLevelSelect
              v-model="filterModel.jobLevelId"
              :empty-select="true"
              @on-change="onSubmit"
            />
            <JobDegreeSelect
              v-model="filterModel.jobDegreeId"
              :empty-select="true"
              @on-change="onSubmit"
            />
            <JobMethodSelect
              v-model="filterModel.jobMethodId"
              :empty-select="true"
              @on-change="onSubmit"
            />
            <JobGenderSelect
              v-model="filterModel.jobGenderId"
              :empty-select="true"
              @on-change="onSubmit"
            />
          </div>
          <div class="mx-2">
            <a
              href="javascript:void(0)"
              class="text-primary text-xs font-semibold"
              @click="onReset"
            >
              Xóa chọn
            </a>
            <span class="text-gray-300">|</span>
            <a
              href="javascript:void(0)"
              class="text-gray-400 text-xs font-semibold"
              @click="advancedFilterOpen = false"
            >
              Đóng</a
            >
          </div>
        </div>
        <div class="py-8 px-10">
          <div
            class="text-white"
            v-if="title || subTitle"
          >
            <div class="text-xl">
              {{ title }}
            </div>
            <div class="text-2xl my-2 font-semibold">
              {{ subTitle }}
            </div>
          </div>
          <div class="w-full flex gap-2 flex-nowrap">
            <label class="input border-transparent input-lg flex items-center gap-2 w-full">
              <input
                type="text"
                class="grow w-full text-sm"
                :value="filterModel.keyword"
                placeholder="Tìm kiếm cơ hội việc làm"
                @change="onKeywordChange"
                @keyup.enter="onSubmit"
              />
              <IconSearch class="w-6" />
            </label>
            <OccupationMultipleSelect
              v-model="filterModel.occupationIds"
              class="!w-2/10 shrink-0"
              size="lg"
              :border="false"
            />
            <div class="join !w-3/10 shrink-0">
              <ProvinceSelect
                class="join-item"
                v-model="filterModel.provinceId"
                :empty-select="true"
                size="lg"
                :bordered="false"
                @select="onProvinceSelect"
              />
              <div class="px-[1px] bg-base-300"></div>
              <DistrictSelect
                class="join-item"
                v-model="filterModel.districtId"
                :empty-select="true"
                size="lg"
                :province-id="filterModel.provinceId"
                :bordered="false"
              />
            </div>
            <a
              href="javascript:void(0)"
              class="text-nowrap bg-secondary rounded-sm px-5 text-white text-sm flex justify-center items-center w-32"
              @click="onSubmit"
            >
              Tìm kiếm
            </a>
            <a
              href="javascript:void(0)"
              class="text-nowrap bg-blue-600 rounded-sm text-white text-sm flex justify-center items-center px-5 !w-40"
              @click="advancedFilterOpen = !advancedFilterOpen"
            >
              <span>Lọc nâng cao</span>
            </a>
          </div>
        </div>
      </div>
    </div>
    <DesktopBreadcrumb />
    <br />
    <div class="flex justify-start items-center mb-4">
      <div class="text-gray-600 text-sm mr-3">Sắp xếp theo:</div>
      <div class="flex justify-start overflow-scroll space-x-2 hide-scrollbar">
        <JobFilterTag
          v-for="(sortItem, idx) in sortFiltersOptions"
          :key="idx"
          size="xs"
          :active="!!(sortItem.value == filterModel.sort)"
          @click="onSortChange(sortItem.value)"
        >
          {{ sortItem.name }}
        </JobFilterTag>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useRouter } from 'vue-router';

import JobFilterTag from '../browsers/JobFilterTag.vue';
import IconSearch from '../icons/IconSearch.vue';
import DistrictSelect from '../resume/DistrictSelect.vue';
import JobDegreeSelect from '../resume/GeneralInformation/JobDegreeSelect.vue';
import JobExperienceSelect from '../resume/GeneralInformation/JobExperienceSelect.vue';
import JobGenderSelect from '../resume/GeneralInformation/JobGenderSelect.vue';
import JobLevelSelect from '../resume/GeneralInformation/JobLevelSelect.vue';
import JobMethodSelect from '../resume/GeneralInformation/JobMethodSelect.vue';
import JobSalarySelect from '../resume/GeneralInformation/JobSalarySelect.vue';
import OccupationMultipleSelect from '../resume/GeneralInformation/OccupationMultipleSelect.vue';
import ProvinceSelect from '../resume/ProvinceSelect.vue';
import DesktopBreadcrumb from './DesktopBreadcrumb.vue';

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  subTitle: {
    type: String,
    default: '',
  },
  defaultOccupationIds: {
    type: Array as () => number[],
    default: () => [],
  },
  defaultProvinceId: {
    type: Number,
    default: null,
  },
  defaultDistrictId: {
    type: Number,
    default: null,
  },
  value: {
    type: Object as () => Partial<IJobFilterOptions>,
    default: () => ({}),
  },
});

const emits = defineEmits<{
  onSubmit: [Partial<IJobFilterOptions>];
}>();

import { useJobBrowserStore, type IJobFilterOptions } from '~/store/jobBrowser';

const { sortFiltersOptions } = storeToRefs(useJobBrowserStore());

const advancedFilterOpen = ref(true);

const filterModel = ref({
  occupationIds: [] as number[],
  provinceId: null as number | null,
  districtId: null as number | null,
  jobSalaryId: null as number | null,
  jobExperienceId: null as number | null,
  jobLevelId: null as number | null,
  jobDegreeId: null as number | null,
  jobMethodId: null as number | null,
  jobGenderId: null as number | null,
  sort: null as string | null,
  keyword: null as string | null,
});
const mapPropsToFilterModel = (value: Partial<IJobFilterOptions>) => {
  console.log('mapPropsToFilterModel', value);
  filterModel.value = Object.assign(filterModel.value, value);
  console.log('filterModel', filterModel.value);
  // onSubmit();
};

const onProvinceSelect = (value: number | string | null) => {
  filterModel.value.districtId = null;
};

const onSubmit = () => {
  const payload: Partial<IJobFilterOptions> = {
    occupationIds: filterModel.value.occupationIds,
    provinceId: filterModel.value.provinceId || undefined,
    districtId: filterModel.value.districtId || undefined,
    jobSalaryId: filterModel.value.jobSalaryId || undefined,
    jobExperienceId: filterModel.value.jobExperienceId || undefined,
    jobLevelId: filterModel.value.jobLevelId || undefined,
    jobDegreeId: filterModel.value.jobDegreeId || undefined,
    jobMethodId: filterModel.value.jobMethodId || undefined,
    jobGenderId: filterModel.value.jobGenderId || undefined,
    keyword: filterModel.value.keyword || undefined,
    sort: filterModel.value.sort || undefined,
  };
  // changeParamsByFilters(payload);
  emits('onSubmit', payload);
};

const onSortChange = (sort: string) => {
  filterModel.value.sort = sort;
  onSubmit();
};

const onReset = () => {
  filterModel.value = {
    occupationIds: [],
    provinceId: null,
    districtId: null,
    jobSalaryId: null,
    jobExperienceId: null,
    jobLevelId: null,
    jobDegreeId: null,
    jobMethodId: null,
    jobGenderId: null,
    sort: null,
    keyword: null,
  };
  onSubmit();
};

const onKeywordChange = (e: Event) => {
  const target = e.target as HTMLInputElement;
  filterModel.value.keyword = target.value;
};

onMounted(() => {
  // mapPropsToFilterModel({
  //   occupationIds: props.defaultOccupationIds,
  //   provinceId: props.defaultProvinceId,
  //   districtId: props.defaultDistrictId,
  // });
});

watch(
  () => props.value,
  (value: Partial<IJobFilterOptions>) => {
    mapPropsToFilterModel(value);
  },
  {
    immediate: true,
  },
);
</script>
