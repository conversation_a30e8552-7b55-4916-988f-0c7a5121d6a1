<template>
  <div>
    <MobileJobFilter
      v-if="isMobile"
      :show="showMobileFilter"
      @on-close="showMobileFilter = false"
      @on-submit="onFilterSubmit"
    />
    <DesktopJobFilter
      v-if="!isMobile"
      :default-occupation-ids="filterBar.defaultOccupationIds"
      :default-province-id="filterBar.defaultProvinceId"
      :default-district-id="filterBar.defaultDistrictId"
      :value="filtersState"
      :title="filterBar.title"
      :sub-title="filterBar.subTitle"
      @on-submit="onFilterSubmit"
    />
    <JobBrowserContent
      :loading="jobBrowser.getRecruitmentListPending"
      :list="recruitmentList"
      :total="total"
      :filters="filtersState"
      :favorite-ids="favoriteList.map((item) => item.recruitmentId)"
      :pagination="{
        total,
        pageSize: filtersState.limit,
        currentPage: filtersState.page,
      }"
      @on-page-change="onPageChange"
      @on-favorite="onFavorite"
    />
  </div>
</template>
<script setup lang="ts">
import { useRoute, useRouter, type LocationQuery } from 'vue-router';
import DesktopJobFilter from '~/components/browsers/DesktopJobFilter.vue';
import MobileJobFilter from '~/components/browsers/MobileJobFilter.vue';
import { useAuthStore } from '~/store/auth.store';
import { useJobBrowserStore, type IJobFilterOptions } from '~/store/jobBrowser';
import { recruitmentFavoriteStore } from '~/store/recruitmentFavorite';
import type { Occupation, Recruitment, Province } from '~/types';
import JobBrowserContent from './JobBrowserContent.vue';
import type { HttpResponse } from '~/store/httpRequest.store';
import { defaultSeo } from '~/constants/seo.constants';

export type PageType = 'occupation' | 'province' | 'normal';

const props = defineProps({
  pageType: {
    type: String as PropType<PageType> | null | undefined,
    default: 'normal',
  },
  occupationId: {
    type: Number as PropType<number | null | undefined>,
    default: undefined,
  },
  provinceId: {
    type: Number as PropType<number | null | undefined>,
    default: undefined,
  },
  districtId: {
    type: Number as PropType<number | null | undefined>,
    default: undefined,
  },
});

const showMobileFilter = ref(false);
const filterBar = ref({
  title: '',
  subTitle: '',
  defaultOccupationIds: [] as number[],
  defaultProvinceId: undefined as number | undefined,
  defaultDistrictId: undefined as number | undefined,
});

const seoMetaObj = Object.assign({ ...defaultSeo }, {});
if (props.pageType === 'occupation') {
  const url = useRuntimeConfig().public.apiUrl + `/v1/occupations/${props.occupationId}`;
  const { data: useFetchData } = await useFetch<HttpResponse<Occupation>>(url);
  const description = `Tìm Việc Làm lĩnh vực ${useFetchData.value?.data?.name} lương cao, cập nhật mới nhất hôm nay tại Vieclam24h. Các vị trí công việc còn trống bao gồm: fresher, chuyên gia, quản lý giám sát, trưởng nhóm, trưởng phòng, giám đốc trong ngành thu mua - kho van - chuoi cung ung`;
  seoMetaObj.title = `Tuyển dụng việc làm ${useFetchData.value?.data?.name}`;
  seoMetaObj.description = description;
  seoMetaObj.keywords = description;
  filterBar.value.title = 'Việc làm';
  filterBar.value.subTitle = useFetchData.value?.data?.name || '';
  if (props.occupationId) {
    filterBar.value.defaultOccupationIds = [props.occupationId];
  }
} else if (props.pageType === 'province') {
  const provinceUrl = useRuntimeConfig().public.apiUrl + `/v1/locations/provinces/${props.provinceId}`;
  const districtUrl = useRuntimeConfig().public.apiUrl + `/v1/locations/districts/${props.districtId}`;
  const [{ data: useFetchProvinceData }, { data: useFetchDistrictData }] = await Promise.all([
    useFetch<HttpResponse<Province>>(provinceUrl),
    useFetch<HttpResponse<Province>>(districtUrl),
  ]);
  let locationText = useFetchDistrictData.value?.data?.name || '';
  filterBar.value.title = 'Việc làm';
  filterBar.value.subTitle = useFetchDistrictData.value?.data?.name || '';
  if (useFetchProvinceData.value?.data) {
    if (locationText) {
      locationText += ', ';
      filterBar.value.subTitle += ', ';
    }
    filterBar.value.subTitle += useFetchProvinceData.value?.data?.name || '';
    locationText += useFetchProvinceData.value?.data?.name;
  }
  const description = `Tuyển dụng việc làm tại ${locationText} từ các doanh nghiệp uy tín hàng đầu Việt Nam thuộc ngành Kinh doanh, Kỹ thuật, Marketing, Sản xuất, Kế toán, Đào tạo,... việc làm Lâm Đồng tuyển dụng trình độ chuyên môn từ Nhân viên, Chuyên viên, Quản lý Giám sát, Giám đốc.`;
  seoMetaObj.title = `Việc làm ở ${locationText} mới nhất ${formatDate(new Date().toDateString(), 'DD/MM/YYYY')}`;
  seoMetaObj.description = description;
  seoMetaObj.keywords = description;
}

useServerSeoMeta(seoMetaObj);

const { addFavoriteRecruitment, removeFavoriteRecruitment, isFavorite, getFavoriteRecruitmentIds } =
  recruitmentFavoriteStore();

const { authenticated } = storeToRefs(useAuthStore());
const { favoriteList } = storeToRefs(recruitmentFavoriteStore());
const { openAuthPopup } = useAuthStore();

const jobBrowser = useJobBrowserStore();

const { recruitmentList, total, filters: filtersState } = storeToRefs(useJobBrowserStore());

const { isMobile } = useDevice();

export interface Filter {
  keyword?: string;
  occupationIds?: number[];
  provinceId?: number;
  districtId?: number;
  jobSalaryId?: number;
  jobExperienceId?: number;
  jobLevelId?: number;
  jobDegreeId?: number;
  jobMethodId?: number;
  jobGenderId?: number;
  page?: number;
  limit?: number;
}

const router = useRouter();
const route = useRoute();
function onPageChange(page: number) {
  jobBrowser.setAndFetchFilters({
    ...filtersState.value,
    page,
  });
  changeParamsByFilters(filtersState.value, {});
  window.scrollTo({
    top: 0,
    behavior: 'smooth',
  });
}

function changeParamsByFilters(filters: Partial<IJobFilterOptions>, sort: Record<string, string>) {
  const {
    occupationIds,
    provinceId,
    districtId,
    jobSalaryId,
    jobExperienceId,
    jobLevelId,
    jobDegreeId,
    jobMethodId,
    jobGenderId,
    page,
    limit,
    keyword,
  } = filters;
  router.push({
    query: {
      occupationIds,
      provinceId,
      districtId,
      jobSalaryId,
      jobExperienceId,
      jobLevelId,
      jobDegreeId,
      jobMethodId,
      jobGenderId,
      page,
      limit,
      keyword,
    },
  });
}

const parseFilterFromQueryParams = (
  locationQuery: LocationQuery,
  {
    occupationIds: defaultOccupationIds = [],
    provinceId: defaultProvinceId = undefined,
    districtId: defaultDistrictId = undefined,
  }: {
    occupationIds?: number[];
    provinceId?: number;
    districtId?: number;
  },
): Partial<IJobFilterOptions> => {
  if (!locationQuery) {
    return {};
  }
  const {
    occupationIds,
    provinceId,
    districtId,
    jobSalaryId,
    jobExperienceId,
    jobLevelId,
    jobDegreeId,
    jobMethodId,
    jobGenderId,
    page,
    limit,
    keyword,
    sort,
  } = locationQuery as Record<string, any>;

  const partialFilter: Partial<IJobFilterOptions> = {};
  if (occupationIds) {
    if (Array.isArray(occupationIds)) {
      partialFilter.occupationIds = occupationIds.map((id: string) => parseInt(id)).filter((id: number) => !isNaN(id));
    } else {
      partialFilter.occupationIds = [parseInt(occupationIds)];
    }
  } else if (defaultOccupationIds && defaultOccupationIds.length > 0) {
    partialFilter.occupationIds = defaultOccupationIds;
  }

  if (provinceId) {
    partialFilter.provinceId = parseInt(provinceId);
  } else if (defaultProvinceId) {
    partialFilter.provinceId = defaultProvinceId;
  }

  if (districtId) {
    partialFilter.districtId = parseInt(districtId);
  } else if (defaultDistrictId) {
    partialFilter.districtId = defaultDistrictId;
  }

  if (jobSalaryId) {
    partialFilter.jobSalaryId = parseInt(jobSalaryId);
  }

  if (jobExperienceId) {
    partialFilter.jobExperienceId = parseInt(jobExperienceId);
  }

  if (jobLevelId) {
    partialFilter.jobLevelId = parseInt(jobLevelId);
  }

  if (jobDegreeId) {
    partialFilter.jobDegreeId = parseInt(jobDegreeId);
  }

  if (jobMethodId) {
    partialFilter.jobMethodId = parseInt(jobMethodId);
  }

  if (jobGenderId) {
    partialFilter.jobGenderId = parseInt(jobGenderId);
  }
  if (page) {
    partialFilter.page = parseInt(page);
  }
  if (limit) {
    partialFilter.limit = parseInt(limit);
  }
  if (keyword) {
    partialFilter.keyword = keyword;
  }
  if (sort) {
    partialFilter.sort = sort;
  }
  return partialFilter;
};

const onFavorite = ({ resumeId, favorite }: { resumeId: number; favorite: boolean }) => {
  if (!authenticated.value) {
    openAuthPopup(true);
    return;
  }
  if (favorite) {
    addFavoriteRecruitment(resumeId);
  } else {
    removeFavoriteRecruitment(resumeId);
  }
};

const onGetFavoriteRecruitmentIds = () => {
  if (authenticated.value) {
    getFavoriteRecruitmentIds(recruitmentList.value.map((recruitment: Recruitment) => recruitment.id));
  }
};

const onFilterSubmit = (filters: Partial<IJobFilterOptions>) => {
  changeParamsByFilters(filters, {});
  // jobBrowser.setAndFetchFilters(filters);
};

onMounted(() => {
  const partialFilter = parseFilterFromQueryParams(route.query, {
    occupationIds: filterBar.value.defaultOccupationIds || [],
    provinceId: props.provinceId || undefined,
    districtId: props.districtId || undefined,
  });
  jobBrowser.setAndFetchFilters(partialFilter);
  // getFavoriteRecruitmentIds([]);
});

watch(
  () => useJobBrowserStore().getRecruitmentListSuccess,
  (success) => {
    if (success) {
      onGetFavoriteRecruitmentIds();
    }
  },
);

watch(
  () => route.query,
  (value: LocationQuery) => {
    const partialFilter = parseFilterFromQueryParams(value, {
      occupationIds: filterBar.value.defaultOccupationIds || [],
      provinceId: filterBar.value.defaultProvinceId,
      districtId: filterBar.value.defaultDistrictId,
    });
    jobBrowser.setAndFetchFilters(partialFilter);
  },
);
</script>
