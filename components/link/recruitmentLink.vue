<template>
  <NuxtLink :to="url" :target="target">
    <slot />
  </NuxtLink>
</template>
<script setup lang="ts">
import * as pageLinkUtils from '~/utils/pageLink.util';
const props = defineProps({
  slug: {
    type: String,
    required: true,
  },
  id: {
    type: Number,
    required: true,
  },
  target: {
    type: String,
    default: '_blank',
  },
});

// const basePath = '/cong-viec/';

const url = computed(() => {
  return pageLinkUtils.getJobPath(props.slug, props.id);
  // return basePath + props.slug + 'id' + props.id;
});
</script>
