<template>
  <div>
    <render>
      <slot />
    </render>
  </div>
</template>
<script setup lang="ts">
import { useSlots } from "vue";
const props = defineProps({
  layout: {
    type: String as () => "horizontal" | "vertical",
    default: "horizontal",
  },
});

console.warn("layout", props.layout);
const childrenClass = props.layout === "horizontal" ? "flex items-center" : "";

function recurseIntoFragments(element: any): any {
  if (
    element.type.toString() === "Symbol(Fragment)" &&
    element.children[0].type.toString() === "Symbol(Fragment)"
  ) {
    return recurseIntoFragments(element.children[0]);
  } else {
    return element;
  }
}

const render = () => {
  console.warn("Form layout is deprecated. Use FormItem instead.");
  const slot = useSlots().default!();
  recurseIntoFragments(slot[0]).children.forEach((element: any) => {
    console.log(element.props?.class);
    console.warn(childrenClass);
    if (element.props?.class && !element.props?.class.includes(childrenClass)) {
      element.props.class += ` ${childrenClass}`;
    } else {
      element.props.class = childrenClass;
    }
  });

  return slot;
};
</script>
