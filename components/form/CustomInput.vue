<template>
  <input
    class="input"
    :name="props.name"
    :type="props.type || 'text'"
    :placeholder="props.placeholder"
  />
</template>
<script lang="ts" setup>
import { defineProps, type InputTypeHTMLAttribute } from 'vue';
import { useField } from 'vee-validate';

const props = defineProps({
  name: {
    type: String,
    required: true,
  },
  type: {
    type: String as () => InputTypeHTMLAttribute,
    required: false,
  },
  placeholder: {
    type: String,
    required: false,
  },
});
</script>
