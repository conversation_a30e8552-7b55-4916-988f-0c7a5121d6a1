<template>
  <div :class="['my-1 form-item', props.required && error ? 'form-item-error' : null]">
    <div class="w-fit mb-2 text-sm text-gray-900 min-w-1 min-h-5">
      <span>
        {{ label }}
      </span>
      <span
        v-if="props.required"
        class="text-red-500 ml-0.5"
      >
        *
      </span>
    </div>
    <slot />
    <div class="max-h-4">
      <div
        v-if="error"
        class="label-text-alt text-error text-wrap text-xs font-light min-h-3"
      >
        {{ error }}
    </div>
    </div>
  </div>
</template>
<script setup lang="ts">
export interface FormItemRule {
  required?: boolean;
  message?: string;
  trigger?: 'blur' | 'change';
  validator?: (rule: FormItemRule, value: any) => boolean;
}
const props = defineProps({
  label: {
    type: String,
    required: true,
  },
  rules: {
    type: Array as () => FormItemRule[],
    default: () => [],
  },
  error: {
    type: String as () => string | null | undefined,
    default: null,
  },
  required: {
    type: Boolean,
    default: false,
  },
});

const required = props.rules.find((rule) => rule.required);
</script>
