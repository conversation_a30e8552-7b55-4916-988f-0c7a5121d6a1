<template>
  <div
    :class="[
      'my-2 form-item flex flex-col lg:flex-row lg:justify-start lg:items-center',
      props.required && error ? 'form-item-error' : null,
    ]"
  >
    <div class="label font-semibold text-sm min-w-48 justify-start mb-2 text-gray-800">
      <span>
        {{ label }}
      </span>
      <span
        v-if="props.required"
        class="text-red-500 pl-1"
        >*</span
      >
    </div>
    <div class="w-full">
      <slot />
      <div
        v-if="error"
        class="label"
      >
        <span class="label-text-alt text-error text-wrap text-sm font-light">{{ error }}</span>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
export interface FormItemRule {
  required?: boolean;
  message?: string;
  trigger?: 'blur' | 'change';
  validator?: (rule: FormItemRule, value: any) => boolean;
}
const props = defineProps({
  label: {
    type: String,
    required: true,
  },
  rules: {
    type: Array as () => FormItemRule[],
    default: () => [],
  },
  error: {
    type: String as () => string | null | undefined,
    default: null,
  },
  required: {
    type: Boolean,
    default: false,
  },
  extraClass: {
    type: String,
    default: '',
  },
});

const required = props.rules.find((rule) => rule.required);
</script>
