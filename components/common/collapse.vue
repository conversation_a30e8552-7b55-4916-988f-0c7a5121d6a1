<template>
  <Spin :loading="loading">
    <div class="my-4 w-full">
      <div
        :class="[
          'bg-white rounded-md shadow-lg border-base-200',
          {
            border: isRequiredInfoVisible,
            'border-error/80': isRequiredInfoVisible,
          },
        ]"
      >
        <div
          v-if="isRequiredInfoVisible"
          :class="['badge relative -top-4 left-2 text-xs font-semibold text-white', 'badge-error']"
        >
          {{ requiredText }}
        </div>

        <div class="flex py-4 px-4 justify-between border-b-2 border-gray-100">
          <div
            class="flex items-center w-full cursor-pointer"
            @click="collapsed = !collapsed"
          >
            <div class="w-full">
              <div class="font-bold text-lg">{{ title }}</div>
            </div>
            <div
              v-if="$slots.extra"
              class="w-full text-right"
            >
              <slot name="extra" />
            </div>
          </div>
          <div class="items-center flex">
            <div
              v-if="showEdit"
              class="flex justify-start text-primary text-sm text-nowrap items-center font-semibold cursor-pointer"
              @click="emit('onEdit')"
            >
              <Icon
                name="i-tdesign:edit"
                class="w-16px h-16px"
              />
              <span class="px-2">{{ editTitle }}</span>
            </div>
          </div>
        </div>
        <div
          v-if="collapsed"
          class="p-4"
        >
          <slot />
        </div>
      </div>
      <div
        v-if="collapsed && showAdding"
        class="flex items-center ml-2 text-primary space-x-2 my-6 cursor-pointer"
        @click="emit('onAdding')"
      >
        <IconPlus class="w-4 h-4" />
        <span class="font-medium text-sm">{{ addingText }}</span>
      </div>
    </div>
  </Spin>
</template>
<script setup lang="ts">
import { defineProps, ref } from 'vue';
import Spin from './Spin.vue';
import IconPlus from '../icons/IconPlus.vue';
import type { CanBeNil } from '~/types';

const emit = defineEmits(['action', 'onAdding', 'onEdit']);

const collapsed = ref(true);

const isRequiredInfoVisible = computed(() => {
  return props.required && !props.valid;
});

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  expanded: Boolean,
  actionText: {
    type: String,
    default: 'Thêm',
  },
  loading: Boolean,
  addingText: {
    type: String,
    default: 'Thêm',
  },
  showAdding: {
    type: Boolean,
    default: true,
  },
  showEdit: {
    type: Boolean,
    default: false,
  },
  editTitle: {
    type: String,
    default: 'Chỉnh sửa',
  },
  required: {
    type: Boolean,
    default: false,
  },
  requiredText: {
    type: String,
    default: 'Bắt buộc',
  },
  valid: {
    type: Boolean,
    default: false,
  },
});
</script>
