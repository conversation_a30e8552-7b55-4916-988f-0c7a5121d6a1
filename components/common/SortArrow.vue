<template>
  <div>
    <IconCaretUpFilled :class="['w-2 h-2', direction === 'asc' ? 'text-primary' : 'text-gray-300']" />
    <IconCaretDownFilled
      :class="['w-2 h-2', direction === 'desc' ? 'text-primary' : 'text-gray-300']"
    />
  </div>
</template>
<script setup lang="ts">
import IconCaretDownFilled from '../icons/IconCaretDownFilled.vue';
import IconCaretUpFilled from '../icons/IconCaretUpFilled.vue';

type Direction = 'asc' | 'desc';

defineProps({
  direction: {
    type: String as PropType<Direction>,
    default: undefined,
  },
});
</script>
