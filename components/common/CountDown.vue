<template>
  <span class="mx-2 countdown font-mono text-sm text-info">
    <span
      :style="`--value: ${value};`"
      aria-live="polite"
      aria-label="59"
    >
      {{ value }}
    </span>
  </span>
</template>
<script setup lang="ts">  
const model = defineModel('value', {
  type: Number,
  default: 60,
});

const countDownInterval = setInterval(() => {
  if (model.value > 0) {
    model.value--;
  } else {
    clearInterval(countDownInterval);
  }
}, 1000);

</script>
