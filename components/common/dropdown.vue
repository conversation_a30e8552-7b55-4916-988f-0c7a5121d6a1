<template>
  <div class="dropdown">
    <button
      tabindex="0"
      role="button"
      class="select select-bordered rounded-none w-80 align-middle flex items-center"
    >
      {{ placeholder }}
    </button>
    <ul
      tabindex="0"
      class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-80 max-h-80 overflow-y-scroll hide-scrollbar text-wrap block"
    >
      <slot />
    </ul>
  </div>
</template>
<script setup lang="ts">
import { defineProps } from "vue";
defineProps({
  items: Array,
  placeholder: String,
});
</script>
