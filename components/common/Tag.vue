<template>
  <span :class="[color, 'px-2 py-1 text-sm font-light rounded-full my-[1px] flex items-center']">
    <slot />
    <a
      v-if="clear"
      href="javascript:void(0)"
      class="ml-1"
      @click="onClick"
    >
      <IconClearRound class="w-4 h-4" />
    </a>
  </span>
</template>
<script setup lang="ts">
import IconClearRound from '../icons/IconClearRound.vue';
defineProps({
  clear: {
    type: Boolean,
    default: false,
  },
  color: {
    type: String,
    default: 'bg-slate-100',
  },
});
const emit = defineEmits(['click']);
const onClick = () => {
  emit('click');
};
</script>
