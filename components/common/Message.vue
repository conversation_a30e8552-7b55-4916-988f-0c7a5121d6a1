<template>
  <div
    id="global_toast"
    class="rounded-md fixed top-0 bg-transparent flex flex-col items-center z-[99999] left-[50%] translate-x-[-50%]"
  >
    <TransitionGroup name="slide-fade">
      <div
        v-for="(toastItem, idx) in items"
        :key="'toast_' + idx"
        class="flex justify-start bg-white my-2 items-center px-4 py-3 rounded-lg shadow-xl w-fit"
      >
        <IconCheck
          v-if="toastItem.type === ToastType.Success"
          class="w-5 h-5 text-success mr-1"
        />
        <IconDismissCircleFil
          v-else-if="toastItem.type === ToastType.Error"
          class="w-5 h-5 text-error mr-1"
        />
        <IconWarningCircleFil
          v-else-if="toastItem.type === ToastType.Warning"
          class="w-5 h-5 text-warning mr-1"
        />
        <IconInformationFil
          v-else-if="toastItem.type === ToastType.Info"
          class="w-5 h-5 text-info mr-1"
        />
        <span class="text-sm font-medium">{{ toastItem.message }}</span>
      </div>
    </TransitionGroup>
  </div>
</template>
<script lang="ts" setup>
import IconCheck from '../icons/IconCheck.vue';
import IconDismissCircleFil from '../icons/IconDismissCircleFil.vue';
import IconWarningCircleFil from '../icons/IconWarningCircleFil.vue';
import IconInformationFil from '../icons/IconInformationFil.vue';
import { useToast, ToastType } from '~~/store/toast';
const { items } = storeToRefs(useToast());
</script>

<style>
/* Slide & Fade Animations */
.slide-fade-enter-active {
  transition: all 0.3s ease-out;
}
.slide-fade-leave-active {
  transition: all 0.5s ease-out;
  opacity: 0;
}
.slide-fade-enter-from {
  transform: translateY(-100%);
  opacity: 0;
}
</style>
