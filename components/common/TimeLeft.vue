<template>
  <div
    :class="[
      {
        'text-error': timeLeftInDays <= 0,
      },
    ]"
  >
    {{ timeLeftInDays > 0 ? 'Còn ' : '' }}
    {{ timeLeftInDays > 0 ? timeLeftInDays : 'Hết hạn' }}
    <span v-if="timeLeftInDays > 0">ngày</span>
  </div>
</template>
<script setup lang="ts">
const props = defineProps({
  dateTime: {
    type: String,
    required: true,
  },
});

const timeLeftInDays = computed(() => {
  const now = new Date();
  const dateTime = new Date(props.dateTime);
  const timeLeft = dateTime.getTime() - now.getTime();
  const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
  return days;
});
</script>
