<template>
  <div class="relative">
    <div class="carousel w-full">
      <div
        v-for="(item, idx) in list"
        :id="'carousel_' + idx.toString()"
        :key="idx"
        class="carousel-item w-full"
      >
        <img
          :src="item"
          class="w-full object-cover h-40 md:h-full"
        />
      </div>
    </div>
    <!--  <div class="flex w-full justify-center gap-2 py-2 absolute left-0 bottom-1">
      <a href="javascript:void(0)"
        :class="['rounded-full w-2 h-2', idx == active ? 'bg-gray-200' : 'bg-primary/80']"
        v-for="(item, idx) in list" :key="idx" @click="onClick(
          'carousel_' + idx.toString()
        )"></a>
    </div> -->
  </div>
</template>
<script setup lang="ts">
defineProps({
  list: {
    type: Array as () => string[],
    default: () => ['/images/banners/banner-03.png'],
  },
});

const active = ref(0);
const onClick = (id: string) => {
  try {
    const element = document.getElementById(id);
    if (!element) return;
    active.value = Number(id.split('_')[1]);
    element?.scrollIntoView({ behavior: 'smooth' });
  } catch (error) {
    console.error(error);
  }
};
</script>
