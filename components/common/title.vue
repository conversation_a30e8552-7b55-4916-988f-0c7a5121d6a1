<template>
  <div class="my-4">
    <div class="flex items-center">
      <slot name="icon"></slot>
      <div :class="[props.textClass, 'mx-3']">
        {{ props.title }}
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { defineProps } from 'vue';

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  icon: {
    type: String,
    default: '',
  },
  textClass: {
    type: String,
    default: 'text-2xl text-gray-700',
  },
});
</script>
