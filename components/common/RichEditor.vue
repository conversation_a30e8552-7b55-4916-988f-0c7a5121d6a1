<template>
  <ClientOnly fallback-tag="span">
    <template #fallback>
      <div class="skeleton w-full h-11"></div>
    </template>
    <QuillEditor
      ref="richEditorRef"
      v-model:content="model"
      theme="snow"
      :toolbar="quillEditorToolBarOptions"
      content-type="html"
    />
  </ClientOnly>
</template>

<script lang="ts" setup>
import type { QuillEditor } from '@vueup/vue-quill';
import type { CanBeNil } from '~/types';

const model = defineModel({
  type: String as () => CanBeNil<string>,
  default: '',
});
const richEditorRef = ref<InstanceType<typeof QuillEditor> | null>(null);
const quillEditorToolBarOptions = [
  'bold',
  'underline',
  'italic',
  {
    list: 'ordered',
  },
  { list: 'bullet' },
  { list: 'check' },
  { align: '' },
  { align: 'center' },
];

const init = () => {
  // setTimeout(() => {
  //   if (richEditorRef.value) {
  //     richEditorRef.value.getQuill().root.style.fontSize = '14px';
  //   }
  // }, 1000);
  nextTick(() => {
    if (richEditorRef.value) {
      richEditorRef.value.getQuill().root.style.fontSize = '14px';
    }
  });
};
onMounted(() => {
  init();
});
</script>
