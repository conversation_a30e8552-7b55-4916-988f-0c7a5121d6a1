<template>
  <div :class="['w-full h-full relative']">
    <div v-if="loading" class="absolute left-0 bg-base-100/40 opacity-80 top-0 bottom-0 right-0 z-[99999]"></div>
    <span
      v-if="loading"
      class="loading loading-spinner w-12 text-primary absolute left-[50%] top-[50%] -translate-x-1/2 -translate-y-1/2 z-[999999]"
    />
    <slot />
  </div>
</template>
<script setup lang="ts">
defineProps({
  loading: {
    type: Boolean,
    required: false,
    default: false,
  },
});
</script>
