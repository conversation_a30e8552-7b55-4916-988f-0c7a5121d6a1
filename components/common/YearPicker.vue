<template>
  <select
    v-model="model"
    :class="['select select-info w-full max-w-xs', model ?? 'select-error']"
    placeholder="Chọn năm"
  >
    <option :selected="true">Năm</option>
    <option
      v-for="i in years"
      :key="i"
      :selected="model === i"
    >
      {{ i }}
    </option>
  </select>
</template>
<script setup lang="ts">
const years = Array.from(
  {
    length: new Date().getFullYear() - 1950 + 1,
  },
  (_, i) => 1950 + i,
).sort((a, b) => b - a);

const model = defineModel({
  required: true,
});

const emit = defineEmits(['update:modelValue']);

const onChange = (e: Event) => {
  // const value = (e.target as HTMLSelectElement).value;
  // if (value === null) {
  //   model.value = null;
  //   return;
  // } else {
  //   model.value = parseInt(value);
  // }
  emit('update:modelValue', model.value);
};
</script>
