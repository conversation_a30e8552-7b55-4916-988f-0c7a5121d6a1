<template>
  <div
    class="dropdown w-full"
    @keydown="handleKeyDown"
  >
    <div
      ref="buttonRef"
      :class="[
        'input flex items-center !pr-8 w-full overflow-hidden',
        sizeInputMap[size],
        border ? 'input-bordered' : '!border-transparent focus:shadow-none focus-within:shadow-none',
      ]"
      @click="onClick"
    >
      <div
        v-if="valueBadges.length"
        class="space-x-0.5 flex justify-start cursor-pointer"
      >
        <div
          v-for="(badge, index) in valueBadges"
          :key="index"
          :class="[
            'bg-gray-200 text-xs max-w-28 text-nowrap py-1 rounded-full flex justify-start items-center relative',
            {
              'pr-6 pl-2': badge.type === 'value',
              'px-3': badge.type === 'count',
            },
          ]"
        >
          <div class="overflow-hidden text-ellipsis">{{ badge?.name }}</div>
          <button
            v-if="badge.type === 'value'"
            class="btn btn-ghost btn-circle btn-xs absolute right-1 h-full flex items-center"
            @click="remove(index)"
          >
            <IconsIconX class="h-3 w-3 stroke-2" />
          </button>
        </div>
      </div>
      <input
        ref="inputRef"
        class="grow w-full min-w-0.5 text-sm border-info"
        :placeholder="placeholder && !model.length ? placeholder : ''"
        @input="handleSearch"
      />
      <span class="text-red-800 absolute right-0 px-2 w-4 h-4">
        <IconSearch />
      </span>
    </div>
    <ul
      :id="componentID"
      ref="dropdownRef"
      popover
      :class="[
        'job-dropdown !z-50 mt-1 menu p-2 shadow bg-white rounded-box w-full max-h-80 overflow-y-scroll hide-scrollbar text-wrap block invisible hide-scrollbar',
      ]"
      :style="{
        width: dropdownContentMaxWidth,
        top: dropdownContentTop,
        left: dropdownContentLeft,
      }"
    >
      <li
        v-for="item in searchKeyWork.length > 0 ? searchItems : items"
        :key="`dropdown-item-${item.id}`"
      >
        <label class="cursor-pointer">
          <input
            type="checkbox"
            :checked="isSelected(item)"
            class="checkbox checkbox-info rounded-sm checkbox-xs"
            @click="(e) => handleSelect(e, item)"
          />
          <span class="label-text font-light">{{ item.name }}</span>
        </label>
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue';
import IconSearch from '../icons/IconSearch.vue';

interface Item {
  id: number;
  name: string;
}
const props = defineProps({
  items: {
    type: Array as () => Item[],
    required: true,
  },
  placeholder: {
    type: String,
    default: '',
  },
  border: {
    type: Boolean,
    default: true,
  },
  size: {
    type: String as () => 'sm' | 'md' | 'lg',
    default: 'md',
  },
  // meaning of maxSize is the maximum number of items that can be selected
  maxSize: {
    type: Number,
    default: 5,
  },
  // meaning of maxTextShow is the maximum number of items that can be shown in the input, left the rest as +n
  maxTextShow: {
    type: Number,
    default: 2,
  },
});

const componentID = ref<string>();
const buttonRef = ref<HTMLElement | null>(null);
const dropdownRef = ref<HTMLElement | null>(null);
const inputRef = ref<HTMLElement | null>(null);

const dropdownContentMaxWidth = ref<string>('200px');
const dropdownContentTop = ref<string>('0px');
const dropdownContentLeft = ref<string>('0px');

const sizeInputMap = {
  sm: 'input-sm',
  md: 'input-md',
  lg: 'input-lg',
};

const model = defineModel<number[]>('value', {
  type: Array,
  default: () => [],
});

const searchItems = ref<Item[]>([]);
const searchKeyWork = ref('');
// const values = ref<Item[]>([]);
// const placeholder = 'Lọc theo nghề nghiệp';

const valueBadges = computed<
  {
    name: string;
    type: 'value' | 'count';
  }[]
>(() => {
  const badges: {
    name: string;
    type: 'value' | 'count';
  }[] = [];
  if (!model.value) {
    return badges;
  }
  const maxIdxToShow = Math.min(props.maxTextShow, model.value?.length);
  const leftCount = model.value.length - maxIdxToShow;
  for (let i = 0; i < maxIdxToShow; i++) {
    const item = props.items?.find((item) => item.id === model.value[i]);
    if (item) {
      badges.push({
        name: item.name,
        type: 'value',
      });
    }
  }
  if (leftCount > 0) {
    badges.push({
      name: `+${leftCount}`,
      type: 'count',
    });
  }
  return badges;
});

const isSelected = (item: Item) => {
  return model.value?.some((valueItem) => valueItem === item.id) || false;
};

const handleSelect = (e: Event, item: Item) => {
  if (!item) {
    return;
  }
  const index = model.value?.findIndex((value) => value === item.id) || -1;
  focusInput(true);
  if (index === -1) {
    if (model.value?.length >= props.maxSize) {
      e.preventDefault();
      return;
    }
    if (Array.isArray(model.value) === false) {
      model.value = [item.id];
    } else {
      model.value.push(item.id);
    }
  } else {
    model.value?.splice(index, 1);
  }
  // if (model.value) {
  model.value = [...model.value];
  // }
};

const focusInput = (focus: boolean) => {
  if (focus) {
    inputRef.value?.focus();
  } else {
    inputRef.value?.blur();
  }
};

const setDropdownPosition = async () => {
  // await nextTick(() => {
  if (buttonRef.value) {
    const buttonWidth = buttonRef.value.getBoundingClientRect().width;
    dropdownContentMaxWidth.value = `${buttonWidth}px`;
    dropdownContentTop.value =
      buttonRef.value.getBoundingClientRect().top + window.pageYOffset + buttonRef.value.clientHeight + 'px';
    dropdownContentLeft.value = buttonRef.value.getBoundingClientRect().left + 'px';
  }
  // });
};

const insertDropdownToBody = async () => {
  await nextTick(() => {
    if (dropdownRef.value) {
      // insert dropdownRef to body
      document.body.append(dropdownRef.value);
    } else {
      console.log('dropdownRef failed ==>', componentID.value);
    }
  });
};

const onClick = (e: Event) => {
  e.preventDefault();
  const isOpening = dropdownRef.value?.classList.contains('visible');
  focusInput(!isOpening);
  openDropdownContent(!isOpening);
};
const remove = (idx: number) => {
  model.value.splice(idx, 1);
};

const openDropdownContent = (open: boolean) => {
  if (open) {
    setDropdownPosition();
    dropdownRef.value?.classList.add('visible');
    dropdownRef.value?.classList.remove('invisible');
  } else {
    dropdownRef.value?.classList.add('invisible');
    dropdownRef.value?.classList.remove('visible');
  }
};

const handleOutsideClick = (event: Event) => {
  const buttonClicked = buttonRef.value?.contains(event.target as Node);
  if (
    dropdownRef.value &&
    !dropdownRef.value.contains(event.target as Node) &&
    !dropdownRef.value.classList.contains('invisible') &&
    !buttonClicked
  ) {
    openDropdownContent(false);
  }
};

const popValue = () => {
  model.value.pop();
};

const handleKeyDown = (event: KeyboardEvent) => {
  // console.log('handleKeyDown ====>', event.key);
  if (event.key === 'Backspace' && model.value?.length && !searchKeyWork.value) {
    popValue();
  }
};

const handleSearch = (event: Event) => {
  const target = event.target as HTMLInputElement;
  searchKeyWork.value = target.value;
  if (props.items) {
    searchItems.value = props.items.filter((item) => item.name.toLowerCase().includes(target.value.toLowerCase()));
  }
};

onMounted(async () => {
  document.addEventListener('click', handleOutsideClick);
  insertDropdownToBody();
  await setDropdownPosition();
});

onBeforeMount(() => {
  componentID.value = `dropdown-search-select-${Math.floor(Math.random() * 1000000)}`;
});

onBeforeUnmount(() => {
  document.removeEventListener('click', handleOutsideClick);
});
</script>
