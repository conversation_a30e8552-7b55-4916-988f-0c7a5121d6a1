<template>
  <div>
    <button
      :id="ID"
      :popovertarget="`${ID}-popover`"
      class="input input-border justify-between w-full"
      :style="`anchor-name: --${ID}`"
    >
      {{ model ? model : props.placeholder }}
      <button class="btn w-4 h-4 btn-circle" @click="onClear">
        <IconsIconClearRound class="w-4 h-4" />
      </button>
    </button>

    <div
      :id="`${ID}-popover`"
      popover
      class="dropdown bg-base-100 rounded-box shadow-lg"
      :style="`position-anchor: --${ID}`"
    >
      <ClientOnly fallback="Loading...">
        <calendar-date
          class="cally"
          locale="vi"
          :value="model"
          :disabled="props.disabled"
          :min="props.min"
          :max="props.max"
          @change="onChange"
        >
          <template #previous>
            <svg
              aria-label="Previous"
              class="fill-current size-4"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
            >
              <path d="M15.75 19.5 8.25 12l7.5-7.5"></path>
            </svg>
          </template>
          <template #next>
            <svg
              aria-label="Next"
              class="fill-current size-4"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
            >
              <path d="m8.25 4.5 7.5 7.5-7.5 7.5"></path>
            </svg>
          </template>
          <calendar-month></calendar-month>
        </calendar-date>
      </ClientOnly>
    </div>
  </div>
</template>
<script setup lang="ts">
import type { CanBeNil } from '~/types';

const ID = 'date-picker-' + Math.random().toString(36).substring(2, 15);
const emits = defineEmits(['onChange']);

useHead({
  script: [
    {
      src: '/js/cally.js',
      type: 'module',
      async: false,
    },
  ],
});
const model = defineModel({
  type: String as () => CanBeNil<string>,
  default: null,
});
const props = defineProps({
  placeholder: {
    type: String,
    default: 'Chọn ngày',
  },
  min: {
    type: String as () => CanBeNil<string>,
    default: null,
  },
  max: {
    type: String as () => CanBeNil<string>,
    default: null,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});

const setValue = (value: CanBeNil<string>) => {
  model.value = value;
  emits('onChange', value);
};
const onChange = (event: Event) => {
  const target = event.target as HTMLButtonElement;
  setValue(target.value);
  // model.value = target.value;
  // emits('onChange', target.value);
};

const onClear = () => {
  setValue(null);
};
</script>
