<template>
  <div class="dropdown w-full">
    <input
      type="text"
      class="input input-bordered w-full"
      :placeholder="placeholder"
    />
    <ul class="p-2 shadow menu dropdown-content z-[1] bg-white rounded-box w-full">
      <li
        v-for="item in items"
        :key="item[props.key]"
        :class="isActive(item) ?? 'bg-primary'"
        @click="onClick(item.id)"
      >
        <a href="javascript:void(0)">{{ item[displayField] }}</a>
      </li>
    </ul>
  </div>
</template>
<script setup lang="ts">
import { defineProps } from 'vue';

const props = defineProps({
  items: Array<Record<string, any>>,
  placeholder: String,
  value: {
    type: Number as PropType<number | null>,
    default: null,
  },
  key: {
    type: String,
    default: 'id',
  },
  displayField: {
    type: String,
    default: 'name',
  },
  onChange: Function as PropType<(id: number) => void>,
  size: {
    type: String as PropType<'sm' | 'md' | 'lg'>,
    default: 'sm',
  },
});

const isActive = (item: Record<string, any>) => {
  return item[props.key] === props.value;
}

function onClick(id: number) {
  props.onChange?.(id);
}
</script>
