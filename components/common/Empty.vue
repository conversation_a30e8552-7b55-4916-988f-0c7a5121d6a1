<template>
  <div class="w-full">
    <div class="text-center text-gray-800">
      <div class="flex justify-center">
        <div :class="['text-gray-400']">
          <slot name="image">
            <template v-if="image">
              <img
                :src="image"
                :style="imageStyle"
                alt="Empty"
              />
            </template>
            <IconEmpty v-else />
          </slot>
        </div>
      </div>
      <div :class="['mt-2', textClass]">
        <slot name="description">
          <p
            v-if="description"
            class="font-light"
          >
            {{ description }}
          </p>
        </slot>
      </div>
      <div class="mt-2">
        <slot></slot>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import IconEmpty from '../icons/IconEmpty.vue';
const props = defineProps({
  description: {
    type: String,
    default: 'Không tìm thấy thông tin',
  },
  image: {
    type: String,
    default: '',
  },
  imageStyle: {
    type: Object,
    default: () => ({
      width: '220px',
    }),
  },
  size: {
    type: String as () => 'sm' | 'md' | 'lg',
    default: 'md',
  },
});

const sizeClass = computed(() => {
  switch (props.size) {
    case 'sm':
      return 'h-12';
    case 'md':
      return 'h-16';
    case 'lg':
      return 'h-20';
    default:
      return 'h-16';
  }
});

const textClass = computed(() => {
  switch (props.size) {
    case 'sm':
      return 'text-sm';
    case 'md':
      return 'text-base';
    case 'lg':
      return 'text-lg';
    default:
      return 'text-base';
  }
});
</script>
