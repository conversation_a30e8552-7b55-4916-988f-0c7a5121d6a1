<template>
  <ul
    v-if="list && list.length && open"
    tabindex="0"
    class="job-dropdown menu bg-white z-[999] w-full min-w-56 p-2 shadow mt-2 max-h-80 block overflow-y-scroll hide-scrollbar"
  >
    <li
      v-for="item in list"
      :key="item[idKey]"
      :class="['hover:bg-primary/5']"
      @click="emit('select', item)"
    >
      <a
        href="javascript:void(0)"
        class="rounded-none active:bg-red-50"
        >{{ item[valueKey] }}</a
      >
    </li>
  </ul>
</template>
<script setup lang="ts">
import { defineEmits, defineProps, type PropType } from "vue";
const emit = defineEmits(["select"]);
defineProps({
  list: {
    type: Array as PropType<Array<any>>,
    default: () => [],
  },
  valueKey: {
    type: String,
    default: "name",
  },
  idKey: {
    type: String,
    default: "id",
  },
  activeId: {
    type: Number as PropType<number>,
    default: 0,
  },
  open: {
    type: Boolean as PropType<boolean>,
    default: false,
  },
});

// const model = defineModel('model', {
//   type: Object as unknown as PropType<any>,
// })

// const onSelect = (item: { id: number, name: string }) => {
//   model.value = item;
// };
</script>
