<template>
  <HorizontalFormItem label="Ảnh đại diện">
    <div class="flex justify-start items-center">
      <div class="mr-8">
        <div class="avatar relative group">
          <div class="w-24 rounded-full">
            <img alt="vietlamlamdong.site" :src="model ? model : '/images/default_avatar.png'" />
          </div>
          <button
            class="btn btn-ghost btn-circle text-error btn-xs absolute top-0 right-0 hidden group-hover:block"
            @click="() => (model = null)"
          >
            <IconsIconTrash class="w-6 h-6" />
          </button>
        </div>
      </div>
      <div>
        <button
          class="btn btn-ghost"
          @click="uploadFile"
        >
          <span>Tải ảnh lên</span>
        </button>
        <input
          id="input_upload_avatar"
          type="file"
          class="hidden"
          accept="image/jpg,image/jpeg,image/png"
          @change="onFileChange"
        />
        <p class="font-light text-xs text-gray-400 my-3">
          Định dạng .JPG, .JPEG, .PNG dung lượng thấp hơn 300 KB với kích thước tối thiểu 300x300 px
        </p>
      </div>
    </div>
  </HorizontalFormItem>
</template>
<script lang="ts" setup>
import type { HttpResponse } from '~/store/httpRequest.store';
import { useToast } from '~/store/toast';
import type { File } from '~/types';

const props = defineProps({
  defaultLogo: {
    type: String,
    default: null,
  },
});

const model = defineModel({
  type: String as () => string | null | undefined,
  default: null,
});

const uploadFile = () => {
  const input = document.getElementById('input_upload_avatar');
  if (input) {
    input.click();
  }
};

const onFileChange = async (e: Event) => {
  const target = e.target as HTMLInputElement;
  const file = target.files?.[0];
  if (file) {
    const reader = new FileReader();
    reader.onload = (e) => {
      model.value = e.target?.result as string;
    };
    reader.readAsDataURL(file);

    const config = useRuntimeConfig();
    const baseUrl = config.public.apiUrl;
    const formData = new FormData();
    formData.append('file', file);
    try {
      const response = await fetch(`${baseUrl}/v1/files/upload`, {
        method: 'POST',
        body: formData,
        headers: {
          Authorization: `Bearer ${getAccessToken()}`,
        },
      });
      const responseData: HttpResponse<File> = await response.json();

      if (responseData.success) {
        model.value = responseData.data?.url;
      }
    } catch (error: any) {
      console.error(error);
      useToast().error(error?.message || 'Tải ảnh lên thất bại');
    }
  }
};
onMounted(() => {
  model.value = props.defaultLogo;
});
</script>
