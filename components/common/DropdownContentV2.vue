<!-- <template>
  <ul
    popover
    :id="componentID"
    ref="dropdownRef"
    :class="[
      'menu border-base-300 border bg-white z-[9999] mt-1 rounded-md p-2 shadow-md max-h-80 overflow-y-scroll hide-scrollbar absolute',
      {
        hidden: !dropdownOpen,
        // hidden: false,
      },
    ]"
    :style="{
      width: dropdownContentMaxWidth,
      top: dropdownContentTop,
      left: dropdownContentLeft,
    }"
  >
    <li
      v-for="(item, idx) in filteredList"
      v-if="filteredList.length"
      :key="getKey(item)"
      @click="onSelect(item)"
    >
      <label
        :class="[
          {
            'menu-active': isActive(item),
            'menu-focus': idx === upDownSelectedIndex,
          },
        ]"
      >
        {{ getValue(item) }}
      </label>
    </li>
    <div
      v-else
      class="text-sm text-gray-400 text-center py-5"
    >
      Dữ liệu rỗng
    </div>
  </ul>
</template>
<script setup lang="ts"></script> -->
