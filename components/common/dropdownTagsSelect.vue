<template>
  <div class="dropdown w-full">
    <label
      ref="buttonRef"
      tabindex="0"
      :class="[
        'input input-bordered flex items-center rounded-none !pr-8 w-full min-h-12 h-auto flex-wrap py-2',
        border ? 'input-bordered' : '!border-white',
      ]"
      @click.prevent="onOpen"
    >
      <div class="flex justify-start gap-1 flex-wrap w-full">
        <Tag
          v-for="(itemId, idx) in model"
          ref="tagRemoveBtnRef"
          :key="idx"
          class="mr-1"
          :clear="true"
          @click="handleRemove(itemId)"
          >{{ items?.find((item) => item.id == itemId)?.name }}</Tag
        >

        <input
          ref="searchInputRef"
          type="text"
          class="w-auto"
          :value="searchKeywords"
          :placeholder="model.length === 0 ? placeholder : ''"
          @input="handleSearch"
        />
      </div>
    </label>
    <div
      ref="dropdownRef"
      tabindex="0"
      :class="[
        'job-dropdown !z-[99999] menu p-2 shadow bg-white rounded-box w-full !max-h-96 overflow-y-scroll  hide-scrollbar text-wrap flex flex-col translate-y-full -bottom-1 invisible',
      ]"
    >
      <ul>
        <template v-if="searchList.length || items?.length">
          <li
            v-for="item in searchList.length ? searchList : items"
            :key="item.id"
            :class="['hover:bg-blue-100/60', isActive(item) ? 'bg-blue-100/80' : null]"
            @click="handleSelect(item)"
          >
            <a
              href="javascript:void(0)"
              class="rounded-none active:bg-red-50"
              >{{ item.name }}</a
            >
          </li>
        </template>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { removeUnicode } from '@/utils/common.js';
import { defineProps } from 'vue';
import Tag from './Tag.vue';

interface Item {
  id: number;
  name: string;
}
const props = defineProps({
  items: {
    type: Array as () => Item[],
    default: () => [],
  },
  placeholder: {
    type: String,
    default: 'Lọc theo nghề nghiệp',
  },
  border: {
    type: Boolean,
    default: true,
  },
});

const buttonRef = ref<HTMLElement | null>(null);
const dropdownRef = ref<HTMLElement | null>(null);
const tagRemoveBtnRef = ref<InstanceType<typeof Tag>[]>();
const searchInputRef = ref<HTMLInputElement | null>(null);

const model = defineModel<number[]>({
  type: Array,
  default: () => [],
});

const searchList = ref<Item[]>([]);
const searchKeywords = ref('');
// const placeholder = 'Lọc theo nghề nghiệp';

const handleSelect = (item: Item) => {
  const index = model.value?.findIndex((value) => value === item.id);
  if (index === -1) {
    if (Array.isArray(model.value) === false) {
      model.value = [];
    }
    model.value.push(item.id);
  } else {
    model.value?.splice(index, 1);
  }
  resetSearch();
  if (model.value) {
    model.value = [...model.value];
  }
};

const handleRemove = (id: number) => {
  const index = model.value?.findIndex((value) => value === id);
  if (index !== -1) {
    model.value?.splice(index, 1);
    if (model.value) {
      model.value = [...model.value];
    }
  }
};

const onOpen = (e: Event) => {
  const tagRemoveBtnClicked = tagRemoveBtnRef.value?.some((tag) => tag.$el.contains(e.target as Node));
  if (tagRemoveBtnClicked) {
    return;
  }
  openDropdown();
};

const openDropdown = () => {
  if (dropdownRef.value) {
    dropdownRef.value.classList.toggle('invisible');
  }
};

const closeDropdown = () => {
  if (dropdownRef.value) {
    dropdownRef.value.classList.add('invisible');
  }
};

const handleOutsideClick = (event: Event) => {
  const buttonClicked = buttonRef.value?.contains(event.target as Node);
  const tagRemoveBtnClicked = tagRemoveBtnRef.value?.some((tag) => tag.$el.contains(event.target as Node));
  const searchInputClicked = searchInputRef.value?.contains(event.target as Node);
  const dropdownContentClicked = dropdownRef.value?.contains(event.target as Node);
  const dropdownContentInvisible = dropdownRef.value?.classList.contains('invisible');

  if (tagRemoveBtnClicked) {
    return;
  }

  if (
    dropdownRef.value &&
    !dropdownContentClicked &&
    !searchInputClicked &&
    !dropdownContentInvisible &&
    !buttonClicked
  ) {
    dropdownRef.value.classList.add('invisible');
  }
};

const handleSearch = (event: Event) => {
  const target = event.target as HTMLInputElement;
  searchKeywords.value = target.value;
  openDropdown();
  if (props.items) {
    searchList.value = props.items.filter((item) => {
      return removeUnicode(item.name.toLowerCase()).includes(removeUnicode(target.value.toLowerCase()));
    });
  }
};

const resetSearch = () => {
  searchKeywords.value = '';
  searchList.value = [];
};

const isActive = (item: Item) => {
  return model.value.includes(item.id);
};

onMounted(() => {
  document.addEventListener('click', handleOutsideClick);
});

onBeforeUnmount(() => {
  document.removeEventListener('click', handleOutsideClick);
});
</script>
