<template>
  <div class="block">
    <div
      class=""
      @click="onOpen"
    >
      <slot />
    </div>
    <dialog
      id="confirm-modal"
      :class="[
        'modal',
        {
          'modal-open': open,
        },
      ]"
    >
      <div class="modal-box">
        <div class="flex items-center mb-3">
          <IconsIconInformationFil class="w-6 h-6 text-warning" />
          <h3 class="text-lg font-bold px-2">
            {{ props.title }}
          </h3>
        </div>
        <p class="text-wrap font-light">
          {{ props.content }}
        </p>
        <div class="modal-action">
          <form
            method="dialog"
            class="space-x-2"
          >
            <button
              class="btn"
              @click="onCancel"
            >
              {{ props.textCancel }}
            </button>
            <button
              class="btn btn-primary"
              @click="onOk"
            >
              {{ props.textOk }}
            </button>
          </form>
        </div>
      </div>
    </dialog>
  </div>
</template>
<script setup lang="ts">
const emits = defineEmits(['ok', 'cancel']);

const open = ref(false);
const props = defineProps({
  title: {
    type: String,
    default: 'Xác nhận',
  },
  content: {
    type: String,
    default: 'Bạn có chắc chắn muốn thực hiện hành động này?',
  },
  textOk: {
    type: String,
    default: 'Đồng ý',
  },
  textCancel: {
    type: String,
    default: 'Hủy',
  },
});

const onOpen = () => {
  open.value = true;
};
const onClose = () => {
  open.value = false;
};

const onOk = () => {
  emits('ok');
  open.value = false;
};
const onCancel = () => {
  emits('cancel');
  open.value = false;
};
</script>
