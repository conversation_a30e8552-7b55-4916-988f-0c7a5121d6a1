<template>
  <div class="form-control">
    <label class="label cursor-pointer">
      <input
        type="checkbox"
        :checked="modelValue"
        class="rounded-md checkbox checkbox-info checkbox-sm [--chkfg:white]"
        @change="onChange"
      />
      <span class="label-text ml-2 !text-xs">{{ text }}</span>
    </label>
  </div>
</template>
<script setup lang="ts">
import { defineModel, defineEmits } from "vue";
const model = defineModel({
  type: Boolean,
  default: false,
});

defineProps({
  text: String,
});

const onChange = (e: Event) => {
  const checked = (e.target as HTMLInputElement).checked;
  model.value = checked;
};
</script>
