<template>
  <div
    v-if="totalPage > 1"
    class="flex justify-center"
  >
    <div class="space-x-1 flex items-center">
      <button
        :class="[
          'btn btn-sm btn-outline btn-circle btn-soft btn-info',
          {
            'btn-disabled': currentPage === 1,
          },
        ]"
        :disabled="currentPage === 0"
        @click="prevPage()"
      >
        <IconChevronLeft16Filled class="w-6" />
      </button>
      <button
        v-for="page of pageList"
        :key="'page' + page"
        :class="[
          'btn btn-circle btn-sm btn-info',
          {
            'btn-outline': isActive(page) === false,
          },
        ]"
        @click="gotoPage(page)"
      >
        {{ page }}
      </button>
      <button
        :class="[
          'btn btn-sm btn-soft btn-circle btn-info',
          {
            'btn-disabled': currentPage === totalPage,
          },
        ]"
        :disabled="currentPage === totalPage"
        @click="nextPage()"
      >
        <IconChevronRight16Filled class="w-6" />
      </button>
    </div>
  </div>
</template>
<script setup lang="ts">
import IconChevronLeft16Filled from '@/components/icons/IconChevronLeft16Filled.vue';
import IconChevronRight16Filled from '@/components/icons/IconChevronRight16Filled.vue';

const props = defineProps<{
  total: number;
  pageSize: number;
  currentPage: number;
  onChange: (page: number) => void;
}>();
function isActive(page: number) {
  return props.currentPage === page;
}

// const totalPage = Math.ceil(props.total / props.pageSize);
const totalPage = ref<number>(0);

const pageList = ref<number[]>([]);

pageList.value = generatePageList();
function calcPageStart() {
  return props.currentPage > 3 ? props.currentPage - 3 : 1;
}

function calcPageEnd() {
  return props.currentPage + 3 > 5 ? props.currentPage + 3 : 5;
}

function generatePageList() {
  console.log('generatePageList', props.currentPage, 'totalPage', totalPage.value);
  const _pageList: number[] = [];
  const _startPageIdx = calcPageStart();
  const _endPageIdx = calcPageEnd();
  for (let i = _startPageIdx; i <= _endPageIdx && i <= totalPage.value; i++) {
    _pageList.push(i);
  }
  return _pageList;
}

function nextPage() {
  const nextPage = props.currentPage + 1;
  if (nextPage > totalPage.value) return;
  props.onChange(nextPage);
}

function prevPage() {
  const prevPage = props.currentPage - 1;
  if (prevPage < 1) return;
  props.onChange(prevPage);
}

function gotoPage(page: number) {
  if (page < 1 || page > totalPage.value) return;
  props.onChange(page);
}

function calcTotalPage(total: number, pageSize: number) {
  return Math.ceil(total / pageSize);
}

watch(
  () => props.currentPage,
  () => {
    pageList.value = generatePageList();
  },
);

watch(
  () => props.total,
  (value: number, oldValue: number) => {
    if (value === oldValue) return;
    totalPage.value = calcTotalPage(props.total, props.pageSize);
    pageList.value = generatePageList();
    // console.log('totalPage', totalPage, 'limit:', props.pageSize, 'total: ', props.total);
  },
);

watch(
  () => props.total,
  () => {
    totalPage.value = calcTotalPage(props.total, props.pageSize);
    pageList.value = generatePageList();
  },
);

onMounted(() => {
  totalPage.value = calcTotalPage(props.total, props.pageSize);
  pageList.value = generatePageList();
});
</script>
