<template>
  <select
    v-model="model"
    :class="['select select-info w-full max-w-xs', model ?? 'select-error']"
    placeholder="<PERSON><PERSON>n tháng"
    @change="onChange"
  >
    <option :selected="true">Tháng</option>
    <option
      v-for="i in 12"
      :key="i"
      :value="i"
      :selected="model === i"
    >
      {{ i }}
    </option>
  </select>
</template>
<script setup lang="ts">
import { defineEmits } from 'vue';

const emit = defineEmits(['update:modelValue']);

const model = defineModel({
  required: true,
  type: Number as PropType<number | null>,
  default: null,
});

const onChange = (e: Event) => {
  // const value = (e.target as HTMLSelectElement).value;
  emit('update:modelValue', model.value);
};
</script>
