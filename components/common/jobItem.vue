<template>
  <RecruitmentLink
    :id="job.id"
    :slug="job.slug"
  >
    <div class="card card-side card-border border-base-300 rounded-md hover:border-info bg-white group">
      <div class="card-body p-2">
        <div class="flex justify-between items-center">
          <div
            class="tooltip tooltip-primary"
            :data-tip="job.title"
          >
            <div class="font-normal text-gray-700 text-base line-clamp-1 group-hover:text-info">{{ job.title }}</div>
          </div>
          <div>
            <div
              class="text-info cursor-pointer"
              @click="onFavorite"
            >
              <IconHeartFil
                v-if="isFavorite"
                class="w-6 h-6"
              />
              <IconHeartOutline
                v-else
                class="w-6 h-6"
              />
            </div>
          </div>
        </div>
        <div class="flex">
          <div class="avatar">
            <div class="w-16 h-16 rounded">
              <img
                :src="job.logo ? job.logo : job.company?.logo || ''"
                :alt="job.companyName || job.company?.name || 'vieclamlamdong.site'"
                class="object-cover"
              />
            </div>
          </div>
          <div class="px-4">
            <p class="font-light uppercase text-sm text-gray-500 line-clamp-1">
              {{ job.companyName || job.company?.name }}
            </p>
            <div class="flex justify-start items-center mt-1">
              <IconMoney class="w-5 h-5 text-gray-400" />
              <span class="text-sm px-2 text-info">
                <template v-if="job.salaryNegotiable"> Thỏa thuận </template>
                <template v-else>
                  {{ formatSalaryRange(job.minSalary, job.maxSalary, 1000000) }}
                </template>
              </span>
            </div>
            <div class="flex justify-start items-center mt-1">
              <!-- <Icon
                name="i-fluent:location-16-regular"
                class="w-4 h-4"
              /> -->
              <IconLocation class="w-5 h-5 text-gray-400" />
              <span class="text-sm px-2 line-clamp-1 text-gray-600">{{ briefJobWorkspaces(job) }}</span>
            </div>
          </div>
        </div>
        <Divider class="!my-0.5" />
        <div class="flex justify-between items-center">
          <div class="flex items-center">
            <div v-if="job.urgent">
              <HotTag />
            </div>
          </div>
          <div class="flex items-center">
            <IconClock24 class="w-4 h-4 text-gray-500" />
            <span class="ml-2 text-xs font-light">
              <!-- {{ formatDateOnly(job.applyExpiredAt) }} -->
              <TimeLeft :date-time="job.applyExpiredAt" />
            </span>
          </div>
        </div>
      </div>
    </div>
  </RecruitmentLink>
</template>
<script setup lang="ts">
import { defineProps } from 'vue';
import HotTag from '~/components/job/HotTag.vue';
import RecruitmentLink from '~/components/link/recruitmentLink.vue';
import type { Province, Recruitment } from '~/types';
import IconClock24 from '../icons/IconClock24.vue';
import IconHeartFil from '../icons/IconHeartFil.vue';
import IconHeartOutline from '../icons/IconHeartOutline.vue';
import IconLocation from '../icons/IconLocation.vue';
import IconMoney from '../icons/IconMoney.vue';
import Divider from './Divider.vue';
import TimeLeft from './TimeLeft.vue';

const emits = defineEmits(['favorite']);

const props = defineProps({
  job: {
    type: Object as () => Recruitment,
    required: true,
  },
  provinces: {
    type: Array as () => Province[],
    default: () => [],
  },
  districts: {
    type: Array as () => Province[],
    default: () => [],
  },
  isFavorite: {
    type: Boolean,
    default: false,
  },
});

function formatSalary(salary: number, unit: number) {
  const salaryInUnit = salary / unit;
  return salaryInUnit.toFixed(0);
}

function formatSalaryRange(minSalary: number, maxSalary: number, unit: number) {
  return `${formatSalary(minSalary, unit)} - ${formatSalary(maxSalary, unit)} triệu`;
}

function getProvinceName(provinceId: number) {
  const province = props.provinces?.find((p) => p.id === provinceId);
  return province?.name || '';
}

function getDistrictName(districtId: number) {
  const district = props.districts?.find((p) => p.id === districtId);
  return district?.name || '';
}

function getProvinceNames(provinceIds: number[]) {
  return provinceIds.map((id) => getProvinceName(id));
}

// function getProvinceNamesByJob(job: Recruitment) {
//   return getProvinceNames(job.workspaces?.map((w) => w.provinceId) || []);
// }

function getDistrictAndProvinceNameByJob(job: Recruitment): string[] {
  if (!job.workspaces) {
    return [];
  }
  return job.workspaces?.map((w) => {
    return `${getDistrictName(w.districtId)}, ${getProvinceName(w.provinceId)}`;
  });
}

function briefJobWorkspaces(job: Recruitment) {
  // const provinceNames = getProvinceNamesByJob(job);
  const provinceNames = getDistrictAndProvinceNameByJob(job);
  const leftProvinceCount = provinceNames.length - 2;
  if (leftProvinceCount <= 0) {
    return provinceNames.join(', ');
  }
  return provinceNames.slice(0, 2) + ', ' + leftProvinceCount + '+';
}

const onFavorite = (e: Event) => {
  e.preventDefault();
  if (!props.job.id) {
    return;
  }

  emits('favorite', props.job.id, !props.isFavorite);
};
</script>
