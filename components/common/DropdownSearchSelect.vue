<template>
  <div
    :class="[
      'w-full cursor-pointer input',
      bordered ? 'input-bordered' : '!border-white hover:border-transparent hover:ring-transparent',
      sizeClass,
    ]"
    ref="inputSelectRef"
    @keyup="onKeyUp"
  >
    <!-- <ClientOnly> -->
    <button
      :class="['!cursor-pointer w-full justify-between flex items-center gap-2']"
      @click.left="onOpen"
    >
      <input
        v-if="showSearch"
        type="text"
        class="w-full !h-10 text-sm"
        :value="inputFocus ? searchKeyword : findItemAndGetNameById(model)"
        :placeholder="findItemAndGetNameById(model) || placeholder"
        @input="onSearchKeywordChange"
        @focus="onSearchInputFocus"
        @focusout="onSearchInputFocusOut"
      />
      <div
        v-else
        class="w-full flex justify-start flex-wrap items-center cursor-pointer text-ellipsis overflow-hidden"
      >
        <div
          v-if="findItemAndGetNameById(model)"
          class="text-ellipsis overflow-hidden text-sm"
        >
          {{ findItemAndGetNameById(model) }}
        </div>
        <div
          v-else
          class="text-gray-400 text-sm line-clamp-1"
        >
          {{ placeholder }}
        </div>
      </div>
      <a
        href="javascript:void(0)"
        class="w-6 h-6"
      >
        <IconArrowDown class="w-6 h-6 text-primary" />
      </a>
    </button>

    <ul
      :id="componentID"
      ref="dropdownRef"
      popover
      :class="[
        'menu flex-col flex-nowrap border-base-300 border bg-white z-[9999] mt-1 rounded-md p-2 shadow-md max-h-80 overflow-y-scroll hide-scrollbar absolute',
        {
          hidden: !dropdownOpen,
          // hidden: false,
        },
      ]"
      :style="{
        width: dropdownContentMaxWidth,
        top: dropdownContentTop,
        left: dropdownContentLeft,
      }"
    >
      <template v-if="filteredList.length">
        <li
          v-for="(item, idx) in filteredList"
          :key="getKey(item)"
          class="w-full"
          @click="onSelect(item)"
        >
          <label
            :class="[
              {
                'menu-active': isActive(item),
                'menu-focus': idx === upDownSelectedIndex,
              },
            ]"
          >
            {{ getValue(item) }}
          </label>
        </li>
      </template>
      <div
        v-else
        class="text-sm text-gray-400 text-center py-5"
      >
        Dữ liệu rỗng
      </div>
    </ul>
    <!-- </ClientOnly> -->
  </div>
</template>
<script setup lang="ts">
import { defineEmits, defineProps, type PropType } from 'vue';
import IconArrowDown from '../icons/IconArrowDown.vue';

const componentID = ref<string>();

const emit = defineEmits<{
  (event: 'select', value: SelectIdType): void;
}>();
export type SelectIdType = number | string | null;
export interface ISelectItem {
  id: SelectIdType;
  name: string;
  [key: string]: any;
}

const props = defineProps({
  list: {
    type: Array as PropType<ISelectItem[]>,
    default: () => [],
  },
  valueKey: {
    type: String,
    default: 'name',
  },
  idKey: {
    type: String,
    default: 'id',
  },
  activeId: {
    type: Number as PropType<number>,
    default: 0,
  },
  open: {
    type: Boolean as PropType<boolean>,
    default: false,
  },
  placeholder: {
    type: String,
    default: null,
  },
  showSearch: {
    type: Boolean,
    default: false,
  },
  bordered: {
    type: Boolean,
    default: true,
  },
  value: {
    type: [Number, String] as PropType<number | string>,
    default: undefined,
  },
  size: {
    type: String as () => 'sm' | 'md' | 'lg',
    default: 'md',
  },
});

const inputSelectRef = ref<HTMLElement | null>(null);
const dropdownRef = ref<HTMLElement | null>(null);
const searchKeyword = ref<string>('');
const searchList = ref<ISelectItem[]>([]);
const inputFocus = ref<boolean>(false);
const upDownSelectedIndex = ref<number>(-1);
const dropdownOpen = ref<boolean>(false);

const dropdownContentMaxWidth = ref<string>('200px');
const dropdownContentTop = ref<string>('0px');
const dropdownContentLeft = ref<string>('0px');
const sizeInputMap = {
  sm: 'input-sm',
  md: 'input-md',
  lg: 'input-lg',
};

const model = defineModel<number | null | string>({
  type: Number || String,
  default: null,
});

const filteredList = computed<ISelectItem[]>(() => {
  if (searchKeyword.value.length) {
    if (searchList.value?.length) {
      return searchList.value;
    } else {
      return [];
    }
  } else {
    return props.list || [];
  }
  // return searchList.value.length ? searchList.value : props.list || [];
});

const sizeClass = computed(() => {
  return sizeInputMap[props.size];
});

const onOpen = () => {
  // console.log('onOpen ===> ', componentID.value);
  setDropdownPosition();
  if (dropdownRef.value) {
    // console.log('open dropdown ===> ', componentID);
    dropdownOpen.value = !dropdownOpen.value;
  }
};

const isDropdownVisible = () => {
  return dropdownRef.value && !dropdownRef.value.classList.contains('hidden');
};

const getKey = (item: ISelectItem) => {
  const key = props.idKey;
  if (typeof item === 'object' && Object.prototype.hasOwnProperty.call(item, key)) {
    return item[key];
  }
};

const getValue = (item: ISelectItem) => {
  const key = props.valueKey;
  if (typeof item === 'object' && Object.prototype.hasOwnProperty.call(item, key)) {
    return item[key];
  }
};

const resetUpDownSelectedIndex = () => {
  upDownSelectedIndex.value = -1;
};

const onSearchListByKeyword = (keyword: string) => {
  if (!props.list) return [];

  return props.list.filter((item) => {
    return removeUnicode(getValue(item).toLowerCase()).includes(removeUnicode(keyword.toLowerCase()));
  });
};

const onSearchKeywordChange = (event: Event) => {
  const target = event.target as HTMLInputElement;
  searchKeyword.value = target.value;
  // console.log('onSearchKeywordChange ===> ', searchKeyword.value);
  searchList.value = onSearchListByKeyword(searchKeyword.value);
  if (!isDropdownVisible()) {
    onOpen();
  }
};

const onSearchInputFocusOut = () => {
  inputFocus.value = false;
  // searchKeyword.value = '';
  // searchList.value = [];
};

const onSearchInputFocus = () => {
  inputFocus.value = true;
};

const isActive = (item: ISelectItem) => {
  return getKey(item) === model.value;
};

const findItemById = (id: SelectIdType) => {
  if (!props.list) return null;

  return (
    props.list.find((listItem) => {
      return getKey(listItem) === id;
    }) || null
  );
};

const findItemAndGetNameById = (id: SelectIdType) => {
  const item = findItemById(id);
  return item ? getValue(item) : null;
};

const onSelect = (item: ISelectItem) => {
  // console.log('onSelect ===> ', item);
  model.value = getKey(item);
  emit('select', model.value);
  searchKeyword.value = '';
  searchList.value = [];
  hideDropdown();
};

const hideDropdown = () => {
  dropdownOpen.value = false;
};

const handleOutsideClick = (event: Event) => {
  const buttonClicked = inputSelectRef.value?.contains(event.target as Node);
  const _isDropdownVisible = isDropdownVisible();
  if (!_isDropdownVisible || buttonClicked) {
    return;
  }

  const clickOnDropdownContent = dropdownRef.value?.contains(event.target as Node);
  if (!clickOnDropdownContent) {
    hideDropdown();
    return;
  }
};

const onMapPropsToState = () => {
  if (props.value != undefined) {
    model.value = props.value;
  }
};

const onKeyUp = (event: KeyboardEvent) => {
  const _isDropDownVisible = isDropdownVisible();
  if (!_isDropDownVisible) {
    return;
  }
  // event.preventDefault()
  const isUpKey = event.key === 'ArrowUp';
  const isDownKey = event.key === 'ArrowDown';
  const isEnterKey = event.key === 'Enter';
  if (isUpKey) {
    upDownSelectedIndex.value = upDownSelectedIndex.value > 0 ? upDownSelectedIndex.value - 1 : 0;
    if (upDownSelectedIndex.value < -1) {
      resetUpDownSelectedIndex();
    }
  } else if (isDownKey) {
    upDownSelectedIndex.value = upDownSelectedIndex.value + 1;
    const isGreaterThanMax = upDownSelectedIndex.value >= filteredList.value?.length;
    if (isGreaterThanMax) {
      upDownSelectedIndex.value = filteredList.value?.length - 1;
    }
  } else if (isEnterKey) {
    if (!filteredList.value) {
      return;
    }
    const selectedItem = filteredList.value[upDownSelectedIndex.value];
    if (selectedItem) {
      onSelect(selectedItem);
    }
  }
};

const insertDropdownToBody = async () => {
  await nextTick(() => {
    if (dropdownRef.value) {
      // insert dropdownRef to body
      document.body.append(dropdownRef.value);
    } else {
      console.log('dropdownRef failed ==>', componentID.value);
    }
  });
};

const setDropdownPosition = async () => {
  // await nextTick(() => {
  if (inputSelectRef.value) {
    const buttonWidth = inputSelectRef.value.getBoundingClientRect().width;
    dropdownContentMaxWidth.value = `${buttonWidth}px`;
    dropdownContentTop.value =
      inputSelectRef.value.getBoundingClientRect().top + window.pageYOffset + inputSelectRef.value.clientHeight + 'px';
    dropdownContentLeft.value = inputSelectRef.value.getBoundingClientRect().left + 'px';
  }
  // });
};

onMounted(async () => {
  document.addEventListener('click', handleOutsideClick);
  await insertDropdownToBody();
  await setDropdownPosition();
  onMapPropsToState();
});

onBeforeMount(() => {
  componentID.value = `dropdown-search-select-${Math.floor(Math.random() * 1000000)}`;
});

onBeforeUnmount(() => {
  document.removeEventListener('click', handleOutsideClick);
});

// watch(
//   () => model.value,
//   (value) => {
//     console.log('DropdownSearchSelect model changed', value);
//   },
// );
watch(
  () => props.value,
  (value) => {
    if (value != undefined) {
      model.value = value;
    }
  },
);
</script>
