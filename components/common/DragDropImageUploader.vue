<template>
  <section
    :class="[
      'w-full border border-dashed border-info p-4 rounded-md',
      {
        'bg-info-content': active,
      },
    ]"
    @dragenter.prevent="toggleActive"
    @dragleave.prevent="toggleActive"
    @dragover.prevent
    @drop.prevent="onDrop"
    @change="selectedFile"
  >
    <div class="w-full text-center">
      <div class="flex justify-center">
        <IconCloudUpload class="w-12 h-12 text-primary" />
      </div>
      <p class="text-sm font-light text-gray-400 my-3">
        Kéo và thả ảnh vào đây hoặc
        <label
          for="dropZoneFile"
          class="text-primary font-semibold cursor-pointer"
          >chọn ảnh</label
        >
      </p>
      <input
        id="dropZoneFile"
        type="file"
        class="hidden"
        accept="image/jpg,image/jpeg,image/png"
      />
      <div class="w-full flex justify-center items-center" v-if="model">
        <div
          class="avatar w-24"
          v-if="model"
        >
          <img
            :src="model"
            alt="Ảnh"
          />
        </div>
        <button class="btn btn-ghost btn-circle" @click="() => (model = null)">
          <IconTrash />
        </button>
      </div>
    </div>
  </section>
</template>
<script setup lang="ts">
import { ref } from 'vue';

import IconCloudUpload from '../icons/IconCloudUpload.vue';
import type { HttpResponse } from '~/store/httpRequest.store';
import { useToast } from '~/store/toast';
import IconTrash from '../icons/IconTrash.vue';
const active = ref(false);
import type { File as IFile } from '~/types';

const model = defineModel({
  type: String as () => string | null | undefined,
  default: null,
});

const toggleActive = () => {
  active.value = !active.value;
};

const onDrop = (e: DragEvent) => {
  toggleActive();
  if (!e.dataTransfer) return;

  // Check if we have files in the drop (direct file drag-and-drop)
  const dropzoneFile = e.dataTransfer?.files?.[0];
  if (dropzoneFile) {
    onUpload(dropzoneFile);
    return;
  }

  // Try to get image data from dataTransfer items
  const items = e.dataTransfer.items;
  if (items) {
    for (let i = 0; i < items.length; i++) {
      if (items[i].kind === 'file') {
        const file = items[i].getAsFile();
        if (file && file.type.startsWith('image/')) {
          onUpload(file);
          return;
        }
      }
    }
  }

  // Try to extract image from HTML
  try {
    const data = e.dataTransfer?.getData('text/html');
    if (!data) {
      throw new Error('No HTML data found');
    }

    // Try to extract image URL
    const url = data?.match(/src="([^"]+)"/)?.[1];
    if (!url) {
      throw new Error('No image URL found in HTML');
    }
    const config = useRuntimeConfig();
    const baseUrl = config.public.apiUrl;
    const proxyUrl = `${baseUrl}/v1/proxy`;
    fetch(proxyUrl, {
      method: 'POST',
      body: JSON.stringify({ url }),
      headers: {
        'Content-Type': 'application/json',
      },
    })
      .then((response) => response.blob())
      .then((blob) => {
        const filename = 'image.' + (url.split(';')[0].split('/')[1] || 'jpg');
        const file = new File([blob], filename, { type: blob.type || 'image/jpeg' });
        onUpload(file);
      })
      .catch((error) => {
        console.error('Error processing data URL:', error);
        useToast().error('Không thể xử lý ảnh. Vui lòng tải ảnh lên từ máy tính của bạn.');
      });
  } catch (error) {
    console.error('Error processing dropped content:', error);
    useToast().error('Không hỗ trợ loại nội dung này. Vui lòng kéo thả file ảnh trực tiếp.');
  }
};

const onUpload = async (file: File | null | undefined) => {
  if (!file) return;

  const reader = new FileReader();
  reader.onload = (e) => {
    model.value = e.target?.result as string;
  };
  reader.readAsDataURL(file);

  const config = useRuntimeConfig();
  const baseUrl = config.public.apiUrl;
  const formData = new FormData();
  formData.append('file', file);
  try {
    const response = await fetch(`${baseUrl}/v1/files/upload`, {
      method: 'POST',
      body: formData,
      headers: {
        Authorization: `Bearer ${getAccessToken()}`,
      },
    });
    const responseData: HttpResponse<IFile> = await response.json();

    if (responseData.success) {
      model.value = responseData.data?.url;
    }
  } catch (error: any) {
    console.error(error);
    useToast().error(error?.message || 'Tải ảnh lên thất bại');
  }
};

const selectedFile = (e: Event) => {
  const target = e.target as HTMLInputElement;
  const file = target.files?.[0];
  onUpload(file);
};
</script>
