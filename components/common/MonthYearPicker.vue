<template>
  <div class="grid grid-cols-2 gap-4">
    <MonthPicker
      v-model="model.month"
      @update:model-value="onMonthChange"
    />
    <YearPicker
      v-model="model.year"
      @update:model-value="onYearChange"
    />
  </div>
</template>
<script setup lang="ts">
import MonthPicker from './MonthPicker.vue';
import YearPicker from './YearPicker.vue';

const emit = defineEmits(['update:modelValue']);

const model = defineModel({
  required: true,
  type: Object as PropType<{
    month: number | null;
    year: number | null;
  }>,
  default: () => ({
    month: -1,
    year: -1,
  }),
});

const onMonthChange = (month: number) => {
  emit('update:modelValue', {
    month,
    year: model.value.year,
  });
};

const onYearChange = (year: number) => {
  emit('update:modelValue', {
    month: model.value.month,
    year,
  });
};
</script>
