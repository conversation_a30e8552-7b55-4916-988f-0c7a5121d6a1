<template>
  <input
    type="text"
    class="input input-bordered w-full"
    value="123123"
    @keydown="onInput"
  />
</template>
<script setup lang="ts">
import { defineModel } from "vue";
// const model = defineModel<string>({
//   type: String,
// });

const value = ref("");

const isNumber = (value: string) => {
  return /^\d+$/.test(value);
};

const onInput = (e: KeyboardEvent) => {};
</script>
