<template>
  <div class="flex justify-start items-center">
    <div class="flex justify-start mr-8">Chia sẻ:</div>
    <div class="flex justify-start gap-4">
      <button
        class="btn btn-primary btn-circle btn-xs"
        @click="onShareFacebook"
      >
        <IconFacebook />
      </button>
      <button
        class="btn btn-circle btn-xs btn-primary"
        @click="onCopyUrl"
      >
        <IconLink class="w-6 h-6 stroke-2" />
      </button>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { useToast } from '~/store/toast';
import IconFacebook from '../icons/IconFacebook.vue';
import IconLink from '../icons/IconLink.vue';
import type { Recruitment } from '~/types';
import * as commonUtils from '~/utils/common';

const props = defineProps({
  link: {
    type: String,
    required: true,
  },
});
const onShareFacebook = () => {
  const facebookShareUrl = `https://www.facebook.com/share_channel/?type=reshare&link=${encodeURIComponent(
    props.link,
  )}&app_id=966242223397117&source_surface=external_reshare&display=popup&hashtag=#`;
  console.log('facebookShareUrl', facebookShareUrl);
  window.open(facebookShareUrl, '_blank', 'width=600,height=400');
};

const onCopyUrl = () => {
  const url = window.location.href;
  commonUtils.copyToClipboard(url);
  useToast().success('Đã sao chép đường dẫn');
};
</script>
