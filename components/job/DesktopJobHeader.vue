<template>
  <div class="border border-gray-100 shadow-md rounded-md py-4 px-10 flex justify-start bg-white">
    <div class="flex justify-start items-center">
      <!-- <div class="w-32 h-32 rounded-sm overflow-hidden">
        <img alt="vietlamlamdong.site" :src="(job.logo || job?.company?.logo) || ''" />
      </div> -->
      <div class="avatar">
        <div class="w-32 rounded">
          <img alt="vietlamlamdong.site" :src="job.logo || job?.company?.logo || ''" />
        </div>
      </div>
    </div>
    <div class="ml-7 text-nowrap w-full">
      <NuxtLink
        :to="'/danh-sach-tin-tuyen-dung' + job?.company?.slug + 'id-' + job?.company?.id"
        class="text-lg font-light text-gray-500"
      >
        {{ job.companyName || job?.company?.name }}
      </NuxtLink>
      <p class="text-2xl mt-4 font-semibold leading-snug text-wrap">{{ job?.title }}</p>
      <div class="flex justify-start my-4">
        <div
          v-if="job?.jobSalaryId"
          class="flex justify-start items-center"
        >
          <div class="w-8 h-8">
            <img alt="vietlamlamdong.site" :src="'/images/icon-coin.png'" />
          </div>
          <div class="text-gray-500 text-sm font-light px-2">Mức lương:</div>
          <div class="text-info">
            <span>{{ findSalaryById(job.jobSalaryId)?.name }}</span>
          </div>
        </div>
        <div
          v-if="job?.jobSalaryId"
          class="border-l border-base-200 mx-5"
        />
        <div
          v-if="job?.applyExpiredAt"
          class="flex justify-start items-center"
        >
          <div class="w-8 h-8">
            <img alt="vietlamlamdong.site" :src="'/images/icon-schedual.png'" />
          </div>
          <div class="text-gray-500 text-sm font-light px-2">Hạn nộp hồ sơ:</div>
          <div class="font-light text-base">
            {{ formatDateOnly(job?.applyExpiredAt) }}
          </div>
        </div>
        <div class="border-l border-base-200 mx-5" />
        <div class="flex justify-start items-center">
          <div class="w-8 h-8 flex-none">
            <img alt="vietlamlamdong.site" :src="'/images/icon-map.png'" />
          </div>
          <div class="text-gray-500 text-sm font-light px-2">Khu vực tuyển:</div>
          <div class="font-light text-base text-wrap">
            {{ getWorkspacesToProvinceNames(job?.workspaces, 2) }}
          </div>
        </div>
      </div>
      <div class="flex justify-between items-center my-4">
        <div class="flex justify-start gap-4">
          <ApplyJobButton :job="job" />
          <button
            class="btn !bg-[#F5F1FF] font-light px-4 text-xl"
            @click="onFavorite(job.id)"
          >
            <IconHeartFil
              v-if="isFavorite(job.id)"
              class="w-6 h-6 text-primary"
            />
            <IconHeartOutline
              v-else
              class="w-6 h-6 text-primary"
            />
          </button>
        </div>
        <div
          v-if="job?.updatedAt"
          class="flex justify-start gap-4 items-center"
        >
          <div class="flex justify-start items-center">
            <span class="text-gray-400 w-5 text-left mr-2">
              <IconCalendar class="w-6 h-6" />
            </span>
            <span class="text-xs mr-1">Ngày cập nhật:</span>
            <span class="text-xs font-semibold">{{ formatDate(job.updatedAt) }}</span>
          </div>
          <div class="flex justify-start items-center">
            <span class="text-gray-400 w-5 text-left mr-2">
              <IconEye class="w-6 h-6" />
            </span>
            <span class="text-xs mr-1">Lượt xem:</span>
            <span class="text-xs font-semibold">{{ formatNumber(job?.totalViews) }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { useAuthStore } from '~/store/auth.store';
import { useCommonStore } from '~/store/common.store';
import { appliedRecruitmentStore } from '~/store/recruitmentApply.js';
import { recruitmentFavoriteStore } from '~/store/recruitmentFavorite';
import { useResumeStore } from '~/store/resume';
import type { Recruitment, Workspace } from '~/types';
import { formatDate } from '~/utils/dateFormatter.utils.js';
import IconCalendar from '../icons/IconCalendar.vue';
import IconEye from '../icons/IconEye.vue';
import IconHeartFil from '../icons/IconHeartFil.vue';
import IconHeartOutline from '../icons/IconHeartOutline.vue';
import ApplyJobButton from './ApplyJobButton.vue';

const { findProvinceByIds, findProvinceById, findSalaryById, findDistrictById } = useCommonStore();

const { addFavoriteRecruitment, removeFavoriteRecruitment, isFavorite } = recruitmentFavoriteStore();

const { apply, getByRecruitmentId, isApplied } = appliedRecruitmentStore();

const { openAuthPopup } = useAuthStore();

const { profile, authenticated } = storeToRefs(useAuthStore());

const { getAttachedResumes, getOnlineResumes } = useResumeStore();
const { onlineResumes, offlineResumes } = storeToRefs(useResumeStore());
const { applyPending, applySuccess, applyError, applyErrorMessage } = storeToRefs(appliedRecruitmentStore());

const props = defineProps({
  job: {
    type: Object as () => Recruitment,
    required: true,
  },
});

const resumeSelectedId = ref<number | null>(null);

function getWorkspacesToProvinceNames(
  workspaces: Workspace[] | undefined | null,
  max?: number,
): string | null | undefined {
  if (!workspaces) {
    return null;
  }
  // let provinces = findProvinceByIds(workspaces.map((wp) => wp.provinceId));
  let districtProvinces = workspaces.map((wp) => {
    const district = findDistrictById(wp.districtId);
    const province = findProvinceById(wp.provinceId);
    return `${district?.name}, ${province?.name}`;
  });
  if (max) {
    districtProvinces = districtProvinces.slice(0, max);
  }
  return districtProvinces?.join(', ');
}

function openLoginModalIfNotAuthenticated() {
  if (!authenticated.value) {
    openAuthPopup(true);
    return true;
  }
  return false;
}

async function onOpenApplyModal() {
  if (openLoginModalIfNotAuthenticated()) {
    return;
  }

  await getAttachedResumes();
  await getOnlineResumes();

  const modal = document.getElementById('submit_job_modal');
  if (modal) {
    (modal as any).showModal();
  }
}

async function onApply() {
  const recruitmentId = props.job.id;
  const resumeId = resumeSelectedId.value;
  if (!resumeId) {
    return;
  }
  apply(recruitmentId, resumeId);
}

function onFavorite(id: number) {
  if (openLoginModalIfNotAuthenticated()) {
    return;
  }
  if (!isFavorite(id)) {
    addFavoriteRecruitment(id);
  } else {
    removeFavoriteRecruitment(id);
  }
}

onMounted(() => {
  if (props.job.id && authenticated.value) {
    getByRecruitmentId(props.job.id);
  }
});

watch(
  () => appliedRecruitmentStore().applySuccess,
  (value) => {
    if (value) {
      const modal = document.getElementById('submit_job_modal');
      if (modal) {
        (modal as any).close();
      }
    }
  },
);
</script>
