<template>
  <div
    v-if="company"
    id="job_detail_company_overview"
    class="bg-white shadow-md rounded-md px-4 lg:px-10 py-4"
  >
    <div class="flex justify-between items-center flex-wrap">
      <h1 class="text-lg font-semibold my-0 lg:my-6">
        {{ company?.name }}
      </h1>
      <NuxtLink
        :to="'/danh-sach-tin-tuyen-dung' + company?.slug + 'id-' + company?.id"
        class="text-info text-sm flex items-center"
      >
        Xem trang công ty
        <span>
          <IconChevronRight16Filled class="w-6" />
        </span>
      </NuxtLink>
    </div>
    <div>
      <div
        v-if="company?.provinceId"
        class="text-sm flex justify-start my-2"
      >
        <div class="text-gray-600 w-32 text-nowrap mr-4 flex items-center">
          <IconLocation class="w-6 h-6 mr-2" />
          <span class="font-light"><PERSON><PERSON><PERSON> đ<PERSON>ể<PERSON>:</span>
        </div>
        <span class="font-semibold">{{ company?.address }}</span>
      </div>
      <div
        v-if="company?.employeeSizeId"
        class="text-sm flex justify-start my-2"
      >
        <div class="text-gray-600 w-32 text-nowrap mr-4 flex items-center">
          <IconGroup class="w-6 h-6 mr-2" />
          <span class="font-light">Quy mô:</span>
        </div>
        <span class="font-semibold">{{ findEmployeeSizeById(company?.employeeSizeId)?.name }} nguời</span>
      </div>
      <div
        class="font-light"
        v-html="company?.overview"
      ></div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import type { Company, Recruitment } from '~/types';
import { useCommonStore } from '~/store/common.store';
import IconChevronRight16Filled from '~/components/icons/IconChevronRight16Filled.vue';
import IconLocation from '~/components/icons/IconLocation.vue';
import IconGroup from '~/components/icons/IconGroup.vue';

const { findEmployeeSizeById } = useCommonStore();

defineProps({
  company: {
    type: Object as () => Company,
    required: true,
  },
});
</script>
