<template>
  <div class="bg-transparent mb-10">
    <div class="flex justify-start">
      <div class="border-t-2 border-secondary w-full max-w-1/2 lg:max-w-56 bg-white">
        <div class="text-info py-4 text-center">Chi tiết tuyển dụng</div>
      </div>
      <div
        class="py-4 bg-gray-100 text-center w-full max-w-1/2 lg:max-w-56 hover:cursor-pointer"
        @click="onMoveCompanySession"
      >
        Công ty
      </div>
    </div>
    <div class="bg-white shadow-md rounded-md p-4 lg:p-10">
      <h1 class="text-2xl font-semibold mb-4">Thông tin chung</h1>
      <div class="bg-blue-50 rounded-sm px-5">
        <div class="grid grid-cols-1 gap-6 py-4 sm:grid-cols-2 md:grid-cols-3">
          <div
            v-if="job?.createdAt"
            class="flex justify-start items-center"
          >
            <div class="bg-blue-100 rounded-full flex justify-center items-center mr-4 w-10 h-10">
              <IconCalendar class="text-xl text-blue-500 w-6 h-6" />
            </div>
            <div>
              <div class="text-sm text-gray-500 font-light">Ngày đăng</div>
              <div class="text-sm">
                {{ formatDate(job?.createdAt) }}
              </div>
            </div>
          </div>
          <div
            v-if="job?.probationDuration"
            class="flex justify-start items-center"
          >
            <div class="bg-blue-100 rounded-full flex justify-center items-center mr-4 w-10 h-10">
              <IconCalendarCheck class="text-xl text-blue-500 w-6 h-6" />
            </div>
            <div>
              <div class="text-sm text-gray-500 font-light">Thời gian thử việc</div>
              <div class="text-sm">
                {{ Math.ceil(job?.probationDuration / 30) }}
                tháng
              </div>
            </div>
          </div>
          <div
            v-if="job?.jobLevelId"
            class="flex justify-start items-center"
          >
            <div class="bg-blue-100 rounded-full flex justify-center items-center mr-4 w-10 h-10">
              <IconMedalOutline class="text-xl text-blue-500 w-6 h-6" />
            </div>
            <div>
              <div class="text-sm text-gray-500 font-light">Cấp bậc</div>
              <div class="text-sm">
                {{ findJobLevelById(job?.jobLevelId)?.name }}
              </div>
            </div>
          </div>
        </div>
        <div class="h-[1px] bg-blue-200 w-full hidden lg:block" />
        <div class="grid grid-cols-1 gap-6 py-4 lg:grid-cols-3">
          <div
            v-if="job?.vacancyQuantity"
            class="flex justify-start items-center"
          >
            <div class="bg-blue-100 rounded-full flex justify-center items-center mr-4 w-10 h-10">
              <IconPeopleCommunicationOutline class="text-xl text-blue-500 w-6 h-6" />
            </div>
            <div>
              <div class="text-sm text-gray-500 font-light">Số lượng ứng tuyển</div>
              <div class="text-sm">
                {{ job.vacancyQuantity }}
              </div>
            </div>
          </div>
          <div
            v-if="job?.jobMethodId"
            class="flex justify-start items-center"
          >
            <div class="bg-blue-100 rounded-full flex justify-center items-center mr-4 w-10 h-10">
              <!-- <IconHelmetSafety class="text-xl text-blue-500 w-6 h-6" /> -->
              <IconClock24 class="text-xl text-blue-500 w-6 h-6" />
            </div>
            <div>
              <div class="text-sm text-gray-500 font-light">Hình thức làm việc</div>
              <div class="text-sm">
                {{ findJobMethodById(job?.jobMethodId)?.name }}
              </div>
            </div>
          </div>
          <div
            v-if="job?.jobGenderId"
            class="flex justify-start items-center"
          >
            <div class="bg-blue-100 rounded-full flex justify-center items-center mr-4 w-10 h-10">
              <IconGender class="text-xl text-blue-500 w-6 h-6" />
            </div>
            <div>
              <div class="text-sm text-gray-500 font-light">Yêu cầu giới tính</div>
              <div class="text-sm">
                {{ job?.jobGenderId === 1 ? 'Nam' : 'Nữ' }}
              </div>
            </div>
          </div>
        </div>
        <div class="h-[1px] bg-blue-200 w-full hidden lg:block" />
        <div class="grid grid-cols-1 gap-6 py-4 lg:grid-cols-3">
          <div
            v-if="job.jobDegreeId"
            class="flex justify-start items-center"
          >
            <div class="bg-blue-100 rounded-full flex justify-center items-center mr-4 w-10 h-10">
              <IconHatGraduation class="text-xl text-blue-500 w-6 h-6" />
            </div>
            <div>
              <div class="text-sm text-gray-500 font-light">Yêu cầu bằng cấp</div>
              <div class="text-sm">
                {{ findDegreeById(job.jobDegreeId)?.name }}
              </div>
            </div>
          </div>
          <div
            v-if="job.jobExperienceId"
            class="flex justify-start items-center"
          >
            <div class="bg-blue-100 rounded-full flex justify-center items-center mr-4 w-10 h-10">
              <IconUserCertification class="text-xl text-blue-500 w-6 h-6" />
            </div>
            <div>
              <div class="text-sm text-gray-500 font-light">Yêu cầu kinh nghiệm</div>
              <div class="text-sm">
                {{ findExperienceById(job.jobExperienceId)?.name }}
              </div>
            </div>
          </div>
          <div
            v-if="job?.minAge && job?.maxAge"
            class="flex justify-start items-center"
          >
            <div class="bg-blue-100 rounded-full flex justify-center items-center mr-4 w-10 h-10">
              <IconFamily class="text-xl text-blue-500 w-6 h-6" />
            </div>
            <div>
              <div class="text-sm text-gray-500 font-light">Độ tuổi</div>
              <div class="text-sm">{{ job.minAge }} - {{ job.maxAge }} tuổi</div>
            </div>
          </div>
        </div>
        <div
          v-if="job.occupations"
          class="h-[1px] bg-blue-200 w-full hidden lg:block"
        />
        <div
          v-if="job.occupations"
          class="grid grid-cols-1 gap-6 py-4"
        >
          <div class="flex justify-start items-center">
            <div class="bg-blue-100 rounded-full flex justify-center items-center mr-4 w-10 h-10 flex-shrink-0">
              <IconSuitcase class="text-xl text-blue-500 w-6 h-6" />
            </div>
            <div>
              <div class="text-sm text-gray-500 font-light">Ngành nghề</div>
              <div class="text-sm text-info text-wrap">
                <div
                  v-for="(occ, idx) in job.occupations"
                  :key="idx"
                >
                  {{ occ.name }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <h1 class="text-2xl font-semibold my-4">Mô tả công việc</h1>
      <div
        v-if="job?.descriptionHtml"
        class="break-words text-[14px] my-6 leading-6"
        v-html="job?.descriptionHtml"
      />

      <h1
        v-if="job?.requirementHtml"
        class="text-2xl font-semibold my-6"
      >
        Yêu cầu công việc
      </h1>
      <div
        v-if="job?.descriptionHtml"
        class="break-words text-[14px] mb-2 leading-6"
        v-html="job?.requirementHtml"
      />

      <h1
        v-if="job?.benefitHtml"
        class="text-2xl font-semibold my-6"
      >
        Quyền lợi
      </h1>
      <div
        v-if="job?.benefitHtml"
        class="break-words text-[14px] mb-2 leading-6"
        v-html="job?.benefitHtml"
      />

      <template v-if="job.company?.isBot">
        <h1
          v-if="job?.benefitHtml"
          class="text-2xl font-semibold my-6"
        >
          Liên hệ
        </h1>
        <div class="grid grid-cols-1 gap-2 text-gray-600 font-light">
          <div class="flex justify-start items-center">
            <div class="max-w-44 w-full flex items-center">
              <IconUserProfile class="h-4 w-auto" />
              <span class="ml-2">Tên người liên hệ:</span>
            </div>
            <span>
              {{ job?.contactName }}
            </span>
          </div>
          <div class="flex justify-start items-center">
            <div class="max-w-44 w-full flex items-center">
              <IconPhoneOutline class="h-4 w-auto" />
              <span class="ml-2">Số điện thoại:</span>
            </div>
            <span class="text-info">
              <NuxtLink
                v-for="phone in job?.contactPhones"
                :key="'contact-phone-' + phone"
                :to="`tel:${job?.contactPhone}`"
              >
                {{ job?.contactPhone }}
              </NuxtLink>
            </span>
          </div>
          <div class="flex justify-start items-center">
            <div class="max-w-44 w-full flex items-center text-sm">
              <IconMail class="h-4 w-auto" />
              <span class="ml-2">Mail:</span>
            </div>
            <span class="text-info">
              <NuxtLink :to="`mailto:${job?.contactEmail}`">
                {{ job?.contactEmail }}
              </NuxtLink>
            </span>
          </div>
        </div>
      </template>

      <h1 class="text-2xl font-semibold my-6">Địa điểm làm việc</h1>
      <div v-if="job?.workspaces?.length">
        <div
          v-for="(wp, idx) in job.workspaces"
          :key="idx"
          class="text-sm mb-1 flex justify-start"
        >
          <span>
            <IconLocation class="text-info text-sm mr-1" />
          </span>
          <span class="text-info">{{ findProvinceById(wp.provinceId)?.name }},</span>
          <span>{{ ' ' }}</span>
          <span>{{ findDistrictById(wp.districtId)?.name }}</span>
          <span>{{ ' ' }}</span>
          <span>{{ wp.address }}</span>
        </div>
      </div>

      <CommonDivider class="my-6" />
      <JobSocialSharing :link="useRoute().fullPath" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useCommonStore } from '~/store/common.store';
import type { Recruitment } from '~/types';
import IconCalendar from '../icons/IconCalendar.vue';
import IconCalendarCheck from '../icons/IconCalendarCheck.vue';
import IconClock24 from '../icons/IconClock24.vue';
import IconFamily from '../icons/IconFamily.vue';
import IconGender from '../icons/IconGender.vue';
import IconHatGraduation from '../icons/IconHatGraduation.vue';
import IconLocation from '../icons/IconLocation.vue';
import IconMedalOutline from '../icons/IconMedalOutline.vue';
import IconPeopleCommunicationOutline from '../icons/IconPeopleCommunicationOutline.vue';
import IconSuitcase from '../icons/IconSuitcase.vue';
import IconUserCertification from '../icons/IconUserCertification.vue';
import JobSocialSharing from './JobSocialSharing.vue';
import IconPhoneOutline from '../icons/IconPhoneOutline.vue';
import IconMail from '../icons/IconMail.vue';
import IconUserProfile from '../icons/IconUserProfile.vue';

const {
  findProvinceById,
  findDistrictById,
  findExperienceById,
  findJobLevelById,
  findJobMethodById,
  findDegreeById,
  findByOccupationId,
} = useCommonStore();

defineProps({
  job: {
    type: Object as () => Recruitment,
    required: true,
  },
});

const onMoveCompanySession = () => {
  window.scrollTo(0, 0);
  // move to id
  const element = document.getElementById('job_detail_company_overview');
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' });
  }
};
</script>
