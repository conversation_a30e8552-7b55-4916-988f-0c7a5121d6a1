<template>
  <div
    v-if="recruitment && isDisplay"
    class="bg-white w-full fixed bottom-0 left-0 py-2 px-4 border-t border-base-200"
  >
    <div class="flex items-center space-x-2">
      <div>
        <button
          class="btn btn-soft font-light px-4 text-xl"
          @click="onFavorite(recruitment.id)"
        >
          <IconHeartFil
            v-if="isFavorite(recruitment.id)"
            class="w-6 h-6 text-primary"
          />
          <IconHeartOutline
            v-else
            class="w-6 h-6 text-primary"
          />
        </button>
      </div>
      <div class="w-full">
        <!-- <button class="btn btn-primary w-full">N<PERSON><PERSON> hồ sơ</button> -->
        <ApplyJobButton :job="recruitment" />
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { useAuthStore } from '~/store/auth.store';
import { recruitmentFavoriteStore } from '~/store/recruitmentFavorite';
import type { Recruitment } from '~/types';
import IconHeartFil from '../icons/IconHeartFil.vue';
import IconHeartOutline from '../icons/IconHeartOutline.vue';
import ApplyJobButton from './ApplyJobButton.vue';

const useAuth = useAuthStore();
const { isFavorite, addFavoriteRecruitment, removeFavoriteRecruitment } = recruitmentFavoriteStore();

defineProps({
  recruitment: {
    type: Object as () => Recruitment,
    required: true,
  },
  isDisplay: {
    type: Boolean,
    required: true,
  },
});

function openLoginModalIfNotAuthenticated() {
  if (!useAuth.authenticated) {
    useAuth.openAuthPopup(true);
    return true;
  }
  return false;
}

function onFavorite(id: number) {
  if (openLoginModalIfNotAuthenticated()) {
    return;
  }
  if (!isFavorite(id)) {
    addFavoriteRecruitment(id);
  } else {
    removeFavoriteRecruitment(id);
  }
}
</script>
