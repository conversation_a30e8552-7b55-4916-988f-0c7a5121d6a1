<template>
  <div
    v-if="job"
    class="px-3 bg-white py-4 shadow-sm"
  >
    <div class="flex justify-start items-center">
      <div class="avatar">
        <div class="w-16 rounded">
          <img alt="vietlamlamdong.site" :src="job?.company?.logo ?? ''" />
        </div>
      </div>
      <div class="text-sm font-light text-wrap ml-3">
        {{ job.companyName || job?.company?.name }}
      </div>
    </div>
    <div class="text-sm font-semibold my-2">{{ job.title }}</div>
    <div class="my-2">
      <div
        v-if="job?.jobSalaryId"
        class="flex justify-start items-center mb-3"
      >
        <div class="w-6 h-6">
          <img alt="vietlamlamdong.site" :src="'/images/icon-coin.png'" />
        </div>
        <div class="px-2">
          <div class="text-gray-500 text-xs font-light mb-1"><PERSON><PERSON><PERSON> <PERSON>ng:</div>
          <div class="text-info text-xs">
            <span>{{ findSalaryById(job.jobSalaryId)?.name }}</span>
          </div>
        </div>
      </div>
      <div
        v-if="job.applyExpiredAt"
        class="flex justify-start items-center mb-3"
      >
        <div class="w-6 h-6">
          <img alt="vietlamlamdong.site" :src="'/images/icon-schedual.png'" />
        </div>
        <div class="px-2">
          <div class="text-gray-500 text-xs font-light mb-1">
            Hạn nộp hồ sơ:
          </div>
          <div class="text-xs">
            <span>{{ formatDateOnly(job.applyExpiredAt) }}</span>
          </div>
        </div>
      </div>
      <div
        v-if="job.workspaces"
        class="flex justify-start items-center flex-1"
      >
        <div class="w-6 h-6">
          <img alt="vietlamlamdong.site" :src="'/images/icon-map.png'" />
        </div>
        <div class="px-2 flex-1">
          <div class="text-gray-500 text-xs font-light mb-1">
            Địa điểm làm việc:
          </div>
          <div class="text-xs">
            <span>{{ getWorkspacesToProvinceNames(job.workspaces) }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { useCommonStore } from "~/store/common.store";
import type { Recruitment, Workspace } from "~/types";
import { formatDateOnly } from "~/utils/dateFormatter.utils";

const {
  findProvinceByIds,
  findProvinceById,
  findDistrictById,
  findSalaryById,
  findJobLevelById,
  findEmployeeSizeById,
} = useCommonStore();

defineProps({
  job: {
    type: Object as () => Recruitment,
    required: true,
  },
});

function getWorkspacesToProvinceNames(
  workspaces: Workspace[] | undefined | null,
): string | null | undefined {
  if (!workspaces) {
    return null;
  }
  const provinces = findProvinceByIds(workspaces.map((wp) => wp.provinceId));
  return provinces?.map((p) => p.name).join(", ");
}
</script>
