<template>
  <div>
    <button
      :class="[
        'btn-primary w-full btn text-white font-light px-10',
        {
          'btn-disabled': isApplied(job.id) || job.company?.isBot,
        },
      ]"
      aria-disabled="true"
      @click="onOpenApplyModal"
    >
      <IconPaperPlaneOutline class="w-6" />
      {{ isApplied(job.id) ? 'Đã ứng tuyển' : 'Ứng tuyển' }}
    </button>

    <dialog
      id="apply_job_modal"
      :class="['modal', { 'modal-open': openModal }]"
    >
      <div class="modal-box bg-white w-11/12 max-w-2xl">
        <Spin :loading="loading">
          <p class="font-light text-sm text-gray-500">Ứng tuyển vào vị trí</p>
          <form method="dialog">
            <button
              class="btn btn-sm btn-circle !btn-ghost absolute right-2 top-2"
              @click="onCloseApplyModal"
            >
              ✕
            </button>
          </form>
          <div>
            <div class="text-lg font-bold my-1">{{ job.title }}</div>
            <div class="text-xs font-light text-gray-500">
              {{ job.company?.name }}
            </div>
          </div>
          <div
            v-if="offlineResumes.length || onlineResumes.length"
            class="my-2"
          >
            <div
              v-for="resume of offlineResumes"
              :key="resume.id"
              :class="[
                'flex items-center mb-2 border-2 text-sm border-gray-300 px-3 py-2 rounded-md',
                {
                  'bg-blue-100 !border-blue-300': resumeSelectedId === resume.id,
                },
              ]"
            >
              <div class="mr-2">
                <input
                  type="radio"
                  name="radio-2"
                  class="radio radio-primary"
                  :checked="resumeSelectedId === resume.id"
                  :disabled="resume.completed === TinyInt.No"
                  @click="resumeSelectedId = resume.id"
                />
              </div>
              <div class="flex justify-between items-center w-full">
                <div>
                  <div class="font-semibold text-sm flex items-center">
                    <span>{{ resume.expectPosition }}</span>
                    <div
                      v-if="resume.completed === TinyInt.No"
                      class="badge badge-soft badge-error badge-sm ml-2"
                    >
                      Hồ sơ chưa hoàn thiện
                    </div>
                  </div>
                  <div class="flex justify-start">
                    <img
                      :src="'/icons/pdf_file.svg'"
                      class="w-4 h-4"
                    />
                    <div class="flex flex-col justify-start text-gray-500 font-light text-sm md:flex-row">
                      <span>Hồ sơ đính kèm</span>
                      <span> • {{ formatDate(resume.createdAt) }}</span>
                      <span v-if="resume.cvFileUrl">
                        •
                        <NuxtLink
                          class="text-primary font-semibold"
                          :to="resume.cvFileUrl"
                          target="_blank"
                          >Xem hồ sơ
                        </NuxtLink>
                      </span>
                    </div>
                  </div>
                </div>
                <div>
                  <NuxtLink
                    class="btn btn-soft btn-primary btn-xs !text-sm md:btn-sm"
                    :to="`/ho-so-cua-ban/cap-nhat-ho-so?resume_id=${resume.id}`"
                    target="_blank"
                    >Cập nhật</NuxtLink
                  >
                </div>
              </div>
            </div>
            <div
              v-for="resume of onlineResumes"
              :key="resume.id"
              :class="[
                'flex items-center mb-2 border-2 text-sm border-gray-300 px-3 py-2 rounded-md',
                {
                  'bg-blue-100 !border-blue-500': resumeSelectedId === resume.id,
                },
              ]"
            >
              <div class="mr-2">
                <input
                  type="radio"
                  name="radio-2"
                  class="radio radio-primary"
                  :checked="resumeSelectedId === resume.id"
                  :disabled="resume.completed === TinyInt.No"
                  @click="resumeSelectedId = resume.id"
                />
              </div>

              <div class="flex justify-between items-center w-full">
                <div>
                  <div class="font-semibold text-sm flex items-center">
                    <span>{{ resume.expectPosition }}</span>
                    <div
                      v-if="resume.completed === TinyInt.No"
                      class="badge badge-soft badge-error badge-sm ml-2"
                    >
                      Hồ sơ chưa hoàn thiện
                    </div>
                  </div>
                  <div class="flex justify-start">
                    <img
                      :src="'/icons/online_resume.svg'"
                      class="w-4 h-4"
                    />
                    <div class="flex flex-col text-gray-500 font-light text-sm md:flex-row md:items-center">
                      <span>Hồ sơ online</span>
                      <span> • {{ formatDate(resume.createdAt) }}</span>
                      <span v-if="resume.cvFileUrl">
                        •
                        <NuxtLink
                          class="text-primary font-semibold"
                          :to="resume.cvFileUrl"
                          target="_blank"
                          >Xem hồ sơ</NuxtLink
                        ></span
                      >
                    </div>
                  </div>
                </div>
                <div>
                  <NuxtLink
                    class="btn btn-soft btn-primary btn-xs !text-sm md:btn-sm"
                    :to="{
                      path: '/ho-so-cua-ban/ho-so-dinh-kem',
                      query: { resume_id: resume.id },
                    }"
                    target="_blank"
                    >Cập nhật</NuxtLink
                  >
                </div>
              </div>
            </div>
          </div>
          <div v-else>
            <Empty description="Bạn chưa có CV nào" />
            <div class="text-center my-3">
              <NuxtLink
                class="btn btn-primary"
                target="_blank"
                to="/ho-so-cua-ban"
              >
                <IconsIconPlus />
                Tạo ngay
              </NuxtLink>
            </div>
          </div>
          <div v-if="profile">
            <FormItem
              label="Họ và tên"
              :required="true"
            >
              <input
                type="text"
                class="input w-full"
                disabled
                :value="profile.fullName"
              />
            </FormItem>
            <FormItem
              label="Email"
              :required="true"
            >
              <input
                type="text"
                class="input w-full"
                disabled
                :value="profile.emailAddress"
              />
            </FormItem>
            <FormItem
              label="Số điện thoại"
              :required="true"
            >
              <input
                type="text"
                class="input w-full"
                disabled
                :value="profile.phoneNumber"
              />
            </FormItem>
          </div>
          <Divider />
          <div class="modal-action">
            <div class="w-full">
              <div class="flex justify-start items-start">
                <CheckBox
                  label="Gửi hồ sơ đính kèm"
                  :model-value="true"
                />
                <span class="text-gray-500 text-sm font-light text-wrap">
                  Bằng việc nhấn nút nộp hồ sơ tôi đồng ý chia sẻ thông tin cá nhân của mình với nhà tuyển dụng theo các
                  <span class="text-blue-500">Điều khoản sử dụng,Chính sách bảo mật</span>và
                  <span class="text-blue-500">Chính sách dữ liệu cá nhân</span> của Việc Làm 24h
                </span>
              </div>
              <br />
              <div class="flex items-center justify-end">
                <Confirm
                  title="Xác nhận nộp hồ sơ"
                  content="Bạn có chắc chắn muốn nộp hồ sơ này không?"
                  @ok="onApply"
                >
                  <button
                    class="btn btn-primary w-42"
                    :disabled="applyPending || !resumeSelectedId"
                  >
                    <span
                      v-if="applyPending"
                      class="loading loading-spinner"
                    ></span>
                    Nộp hồ sơ
                  </button>
                </Confirm>
              </div>
            </div>
          </div>
        </Spin>
      </div>
    </dialog>
  </div>
</template>
<script setup lang="ts">
import { useAuthStore } from '~/store/auth.store';
import { appliedRecruitmentStore } from '~/store/recruitmentApply.js';
import { useResumeStore } from '~/store/resume';
import { TinyInt, type CanBeNil, type Recruitment } from '~/types';
import Spin from '../common/Spin.vue';
import IconPaperPlaneOutline from '../icons/IconPaperPlaneOutline.vue';
import CheckBox from '../common/CheckBox.vue';
import { useToast } from '~/store/toast';
import Confirm from '../common/Confirm.vue';
import Empty from '../common/Empty.vue';

const { applyPending, applyErrorMessage, applyError, applySuccess } = storeToRefs(appliedRecruitmentStore());
const { apply, isApplied, getByRecruitmentId } = appliedRecruitmentStore();
const { onlineResumes, offlineResumes, getResumePending, getOnlineResumesPending, getAttachedResumesPending } =
  storeToRefs(useResumeStore());
const { profile, authenticated } = storeToRefs(useAuthStore());
const { openAuthPopup } = useAuthStore();
const { getAttachedResumes, getOnlineResumes } = useResumeStore();

const props = defineProps({
  job: {
    type: Object as () => Recruitment,
    required: true,
  },
});

const openModal = ref(false);
const resumeSelectedId = ref<CanBeNil<number>>(null);
const loading = computed(() => {
  return getResumePending.value || getOnlineResumesPending.value || getAttachedResumesPending.value;
});

async function onApply() {
  const recruitmentId = props.job.id;
  const resumeId = resumeSelectedId.value;
  if (!resumeId) {
    return;
  }
  apply(recruitmentId, resumeId);
}

function openLoginModalIfNotAuthenticated() {
  if (!authenticated.value) {
    openAuthPopup(true);
    return true;
  }
  return false;
}

async function getAppliedJobByJobId() {
  if (!props.job?.id) {
    return;
  }

  if (!authenticated) {
    return;
  }
  // setTimeout(async () => {
  await getByRecruitmentId(props.job.id);
  // }, 5000);
}

async function setDefaultResume() {
  if (resumeSelectedId.value) {
    return;
  }
  if (onlineResumes.value?.length) {
    resumeSelectedId.value = onlineResumes.value.find((resume) => resume.completed === TinyInt.Yes)?.id;
  } else if (offlineResumes.value?.length) {
    resumeSelectedId.value = offlineResumes.value.find((resume) => resume.completed === TinyInt.Yes)?.id;
  } else {
    resumeSelectedId.value = null;
  }
}

async function onOpenApplyModal() {
  if (openLoginModalIfNotAuthenticated()) {
    return;
  }

  await getAttachedResumes();
  await getOnlineResumes();

  // const modal = document.getElementById('apply_job_modal');
  // if (modal) {
  //   (modal as any).showModal();
  // }

  openModal.value = true;

  setDefaultResume();
}

const onCloseApplyModal = () => {
  openModal.value = false;
};

onMounted(() => {
  getAppliedJobByJobId();
});

watch(
  () => props.job,
  (newJob, oldJob) => {
    if (newJob && newJob.id !== oldJob?.id) {
      getAppliedJobByJobId();
    }
  },
);

watch(
  () => applyError.value,
  (error) => {
    if (error) {
      useToast().error(applyErrorMessage.value || 'Có lỗi xảy ra trong quá trình ứng tuyển. Vui lòng thử lại sau.');
    }
  },
);

watch(
  () => applySuccess.value,
  (success) => {
    if (success) {
      useToast().success('Ứng tuyển thành công');
      // const modal = document.getElementById('apply_job_modal');
      // if (modal) {
      //   (modal as any).close();
      // }

      onCloseApplyModal();
    }
  },
);
</script>
