<template>
  <div
    v-if="resume"
    class="border my-4 border-gray-100 bg-white rounded-md py-4 px-6 flex items-center justify-start flex-col md:flex-row md:justify-between"
  >
    <div class="flex justify-start items-center w-full md:w-auto">
      <img
        v-if="resume.type === ResumeType.online"
        src="~/assets/icons/online_resume.svg"
      />
      <img
        v-else
        src="~/assets/icons/pdf_file.svg"
      />
      <span class="font-semibold text-base ml-2">
        {{ resume.expectPosition }}
      </span>
      <div
        v-if="!resume.status && resume.completed"
        class="badge badge-warning uppercase ml-2"
      >
        Chờ duyệt
      </div>
      <div
        v-if="!resume.completed"
        class="badge badge-soft badge-error uppercase ml-2"
      >
        Chưa hoàn thành
      </div>
    </div>
    <div class="flex justify-start items-center text-nowrap w-full flex-col md:w-auto md:flex-row">
      <div class="h-auto flex items-center text-sm mr-4 my-2">
        <!-- <div class="border-none w-[2px] bg-gray-200 h-6" /> -->
        <!-- <div class="mx-2">
          <span class="text-gray-400 mr-2">Lượt xem:</span>
          <span>{{ resume.totalView }}</span>
        </div> -->
      </div>

      <button
        class="btn btn-soft btn-primary mb-2 w-full md:w-auto md:mr-2 md:mb-0"
        @click="onEdit"
      >
        <IconPen class="w-4 h-4" />
        {{ resume.completed ? 'Cập nhật' : 'Tiếp tục hoàn thành' }}
      </button>
      <Confirm @ok="onDelete">
        <button class="btn btn-soft btn-error w-full md:w-auto">
          <IconTrash class="w-4 h-4" />
          Xóa
        </button>
      </Confirm>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ResumeStatus, ResumeType, TinyInt, type Resume } from '~/types';
import IconPen from '../icons/IconPen.vue';
import IconTrash from '../icons/IconTrash.vue';
import { useResumeStore } from '~/store/resume';
import Confirm from '../common/Confirm.vue';

const { updateGeneralInfo, deleteResume } = useResumeStore();

const props = defineProps({
  resume: {
    type: Object as () => Resume,
    required: true,
  },
});

const onEdit = () => {
  if (!props.resume) return;
  const isOnlineResume = props.resume.type === ResumeType.online;
  if (isOnlineResume) {
    return useRouter().push({
      path: 'ho-so-cua-ban/cap-nhat-ho-so',
      query: {
        resume_id: props.resume.id,
      },
    });
  } else {
    return useRouter().push({
      path: 'ho-so-cua-ban/ho-so-dinh-kem',
      query: {
        resume_id: props.resume.id,
      },
    });
  }
};

const onDelete = async () => {
  if (!props.resume) return;

  await deleteResume(props.resume.id);
};

const onSearchAllow = (e: Event) => {
  /* if (!props.resume) return;

  updateGeneralInfo(props.resume.id, {
    isSearchAllowed:
  }) */
  const allow = (e.target as HTMLInputElement).checked;
  console.log('onSearchAllow: ', allow);
  if (!props.resume) return;

  updateGeneralInfo(props.resume.id, {
    isSearchAllowed: allow ? TinyInt.Yes : TinyInt.No,
  });
};
</script>
