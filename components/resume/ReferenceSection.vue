<template>
  <Collapse
    v-if="resume"
    title="Thông tin người tham khảo"
    adding-text="Thêm người tham khảo"
    :expanded="expanded"
    action-text="Thêm"
    :show-adding="!addingFormOption"
    :loading="useResume.updateRefsPending || useResume.createRefsPending"
    @action="onExpand"
    @on-adding="onOpenAddingForm"
  >
    <template v-if="resume?.refs?.length">
      <ReferenceItem
        v-for="(item, idx) in resume.refs"
        :key="idx"
        :value="item"
        @on-delete="onDelete(item.id)"
        @on-edit="onEdit(item)"
      />
    </template>
    <Empty
      v-else-if="!addingFormOption"
      description="Thông tin người tham khảo rỗng"
      size="sm"
    />
    <RefForm
      v-if="addingFormOption"
      :default-value="{
        name: editRef?.fullName || '',
        phone: editRef?.phone || '',
        company: editRef?.companyName || '',
        position: editRef?.position || '',
      }"
      :ok-text="editRef ? 'Cập nhật' : 'Thêm'"
      :cancel-text="editRef ? 'Hủy' : 'Đóng'"
      @on-ok="onOk"
      @on-cancel="onCancel"
    />
  </Collapse>
</template>
<script setup lang="ts">
import { useResumeStore } from '~/store/resume';
import type { Resume, ResumeRef } from '~/types/resume.interface';
import Collapse from '../common/collapse.vue';
import ReferenceItem from './ReferenceItem.vue';
import RefForm, { type IRefFormModel } from './RefForm.vue';
import Empty from '../common/Empty.vue';
const { resume } = storeToRefs(useResumeStore());
const useResume = useResumeStore();

const editRef = ref<ResumeRef | null>(null);
const addingFormOption = ref<boolean>(false);
const expanded = ref<boolean>(true);

const onEdit = (ref: ResumeRef) => {
  if (addingFormOption.value) {
    return;
  }
  editRef.value = ref;
  addingFormOption.value = true;
  expanded.value = true;
};

const onOk = (ref: IRefFormModel) => {
  if (!resume.value) return;
  if (editRef.value) {
    onUpdate(ref);
  } else {
    onAdd(ref);
  }
};

const onAdd = (ref: IRefFormModel) => {
  if (!resume.value) {
    return;
  }
  const newRef: ResumeRef = {
    fullName: ref.name,
    phone: ref.phone,
    companyName: ref.company,
    position: ref.position,
    resumeId: resume.value?.id,
    id: new Date().valueOf(),
  };
  useResume.addReference(resume.value.id, newRef);
};

const onExpand = () => {
  expanded.value = !expanded.value;
};

const onCancel = () => {
  editRef.value = null;
  addingFormOption.value = false;
};

const onUpdate = (ref: IRefFormModel) => {
  if (!ref || !editRef.value || !resume.value?.id) return;

  const id = editRef.value.id;
  const resumeId = editRef.value.resumeId;

  useResume.updateReference(resume.value.id, id, {
    fullName: ref.name,
    phone: ref.phone,
    companyName: ref.company,
    position: ref.position,
    resumeId: resumeId,
  });

  editRef.value = null;
};

const onDelete = (id: number) => {
  if (!resume.value) return;
  useResume.removeReference(resume.value.id, id);
};

const onOpenAddingForm = () => {
  if (!editRef.value) {
    addingFormOption.value = true;
  }
};

watch(
  () => useResumeStore().createRefsSuccess,
  (value) => {
    if (value) {
      addingFormOption.value = false;
    }
  },
);
</script>
