<template>
  <div class="dropdown w-full">
    <label class="!h-auto input input-bordered w-full flex items-center gap-2 flex-wrap py-2">
      <div class="flex flex-wrap w-full">
        <Tag
          v-for="(tag, idx) in model"
          :key="idx"
          class="mr-1"
          :clear="true"
          @click="onRemoveTag(tag, idx)"
          >{{ tag }}</Tag
        >
        <input
          v-model="keyword"
          type="text"
          class="max-w-full hover:outline-none outline-0 text-sm"
          placeholder="Nhập tên kỹ năng mềm hoặc cứng"
          @keydown.enter="addTag"
        />
      </div>
    </label>
    <DropDownContent
      :list="list"
      id-key="id"
      value-key="name"
    />
  </div>
</template>
<script setup lang="ts">
import { defineModel } from 'vue';

import DropDownContent from '~/components/common/DropDownContent.vue';
import Tag from '~/components/common/Tag.vue';

const list = ref([]);
const keyword = ref('');

const model = defineModel({
  type: Array as () => string[],
  default: [],
});

const onRemoveTag = (tag: string, idx: number) => {
  model.value = model.value.filter((item) => item !== tag && item !== model.value[idx]);
};

const addTag = () => {
  if (keyword.value) {
    const newTag = keyword.value.trim();
    const isExist = model.value?.some((tag) => tag === newTag);
    if (!isExist) {
      if (!model.value) {
        model.value = [];
      }
      model.value.push(newTag);
    }
    keyword.value = '';
  }
};
</script>
