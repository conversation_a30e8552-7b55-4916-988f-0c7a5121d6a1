<template>
  <DropdownSearchSelect
    v-model:model-value="model"
    :list="emptySelect ? [emptyValue, ...jobSalaries] : jobSalaries"
    id-key="id"
    value-key="name"
    placeholder="<PERSON><PERSON><PERSON> mức lương"
    @select="onSelect"
  />
</template>
<script setup lang="ts">
import { defineEmits, defineModel } from "vue";
import DropdownSearchSelect from "~/components/common/DropdownSearchSelect.vue";
import { useCommonStore } from "~/store/common.store";

const emptyValue = {
  id: -1,
  name: "<PERSON><PERSON>t cả mức lương",
};

const emits = defineEmits(["onChange"]);
const { jobSalarys: jobSalaries } = storeToRefs(useCommonStore());
const { findSalaryById } = useCommonStore();
const model = defineModel({
  default: null,
  type: Number as () => number | null,
});

const props = defineProps({
  emptySelect: {
    type: Boolean,
    default: false,
  },
});

const onSelect = (id: number) => {
  // model.value = id;
  emits("onChange", id);
};
</script>
