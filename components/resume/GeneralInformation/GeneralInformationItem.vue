<template>
  <div class="my-2">
    <div class="text-xs text-gray-500 mb-2">{{ label }}</div>
    <div
      v-if="content"
      class="text-sm font-semibold"
    >
      {{ content }}
    </div>
    <div
      v-else
      class="text-sm font-semibold"
    >
      <PERSON><PERSON><PERSON> c<PERSON>p nhật
    </div>
  </div>
</template>
<script setup lang="ts">
import { defineProps } from 'vue';
defineProps({
  label: String,
  content: {
    type: String as () => string | undefined | null,
    required: false,
    default: '',
  },
});
</script>
