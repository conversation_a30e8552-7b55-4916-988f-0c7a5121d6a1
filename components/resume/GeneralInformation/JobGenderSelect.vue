<template>
  <DropdownSearchSelect
    :list="emptySelect ? [emptyValue, ...jobGenders] : jobGenders"
    id-key="id"
    value-key="name"
    placeholder="Chọn giới tính"
    @select="onSelect"
  />
</template>
<script setup lang="ts">
import { defineEmits } from "vue";
import DropdownSearchSelect from "~/components/common/DropdownSearchSelect.vue";
import { useCommonStore } from "~/store/common.store";

const { jobGenders } = storeToRefs(useCommonStore());

const emptyValue = {
  id: -1,
  name: "Tất cả giới tính",
};

const emits = defineEmits(["onChange"]);
// const model = defineModel({
//   default: null,
//   type: Number as () => number | null,
// });

const props = defineProps({
  emptySelect: {
    type: Boolean,
    default: false,
  },
});

const onSelect = (id: number) => {
  // model.value = id;
  emits("onChange", id);
};
</script>
