<template>
  <DropdownSearchSelect
    :list="emptySelect ? [emptyValue, ...jobMethods] : jobMethods"
    id-key="id"
    value-key="name"
    placeholder="<PERSON><PERSON><PERSON> ph<PERSON><PERSON>ng thức làm việc"
    @select="onSelect"
  />
</template>
<script setup lang="ts">
import { defineEmits, defineModel } from "vue";
import DropdownSearchSelect from "~/components/common/DropdownSearchSelect.vue";
import { useCommonStore } from "~/store/common.store";

const { jobMethods } = storeToRefs(useCommonStore());

const emits = defineEmits(["onChange"]);
// const model = defineModel({
//   default: -1,
//   type: Number as () => number | null,
// });

const emptyValue = {
  id: -1,
  name: "Tất cả loại công việc",
};

const props = defineProps({
  emptySelect: {
    type: Boolean,
    default: false,
  },
});

const onSelect = (id: number) => {
  // model.value = id;
  // open.value = false;
  emits("onChange", id);
};
</script>
