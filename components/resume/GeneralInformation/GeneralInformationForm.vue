<template>
  <div>
    <div class="grid grid-cols-1 gap-4">
      <button class="btn btn-primary btn-outline" @click="setDefaultValue">set default value</button>
      <FormItem
        :required="true"
        label="Vị trí mong muốn"
        :error="errors.expectPosition"
      >
        <input
          v-model="expectPosition"
          type="text"
          class="input input-bordered w-full"
        />
      </FormItem>
    </div>
    <div class="grid grid-cols-1 gap-4">
      <FormItem
        :required="true"
        label="Nghề nghiệp"
        :error="errors.occupationIds"
      >
        <OccupationMultipleSelect v-model="occupationIds" />
      </FormItem>
    </div>
    <div class="grid grid-cols-1 gap-2 lg:grid-cols-2">
      <FormItem
        :required="true"
        label="Cấp bật hiện tại"
        :error="errors.currentLevelId"
      >
        <JobLevelSelect v-model="currentLevelId" />
      </FormItem>
      <FormItem
        :required="true"
        label="Cấp bậc mong muốn"
        :error="errors.expectLevelId"
      >
        <JobLevelSelect v-model="expectLevelId" />
      </FormItem>
      <FormItem
        :required="true"
        label="Mức lương mong muốn"
        :error="errors.expectSalaryId"
      >
        <JobSalarySelect v-model="expectSalaryId" />
      </FormItem>
      <FormItem
        :required="true"
        label="Trình độ học vấn"
      >
        <JobDegreeSelect v-model="jobEducationId" />
      </FormItem>
      <FormItem
        :required="true"
        label="Số năm kinh nghiệm"
        :error="errors.jobExperienceId"
      >
        <JobExperienceSelect v-model="jobExperienceId" />
      </FormItem>
      <FormItem
        :required="true"
        label="Địa điểm làm việc"
        :error="errors.jobProvinceIds"
      >
        <ProvinceMultipleSelect v-model="jobProvinceIds" />
      </FormItem>
      <FormItem
        :required="true"
        label="Hình thức làm việc"
        :error="errors.jobMethodId"
      >
        <JobMethodSelect v-model="jobMethodId" />
      </FormItem>
      <FormItem
        :required="true"
        label="Mục tiêu nghề nghiệp"
        :error="errors.careerObjective"
      >
        <textarea
          v-model="careerObjective"
          class="textarea textarea-info textarea-bordered w-full"
        />
      </FormItem>
    </div>
    <div class="grid grid-cols-1 gap-4">
      <FormItem
        label="Kỹ năng mềm & cứng"
        :error="errors.jobSkills"
      >
        <SkillMultipleTagsSelect v-model:model-value="jobSkills" />
      </FormItem>
    </div>
    <div class="flex justify-center md:justify-end space-x-2">
      <CancelButton @click="onCancel" />
      <OkButton
        :disable="true"
        text="Lưu thông tin"
        @click="onSubmit"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
import { defineEmits } from 'vue';
import { toTypedSchema } from '@vee-validate/yup';
import { useForm } from 'vee-validate';
import { array, number, object, string } from 'yup';

import CancelButton from '~/components/common/CancelButton.vue';
import OkButton from '~/components/common/OkButton.vue';
import FormItem from '~/components/form/formItem.vue';
import type { Resume } from '~/types/resume.interface';
import JobDegreeSelect from './JobDegreeSelect.vue';
import JobExperienceSelect from './JobExperienceSelect.vue';
import JobLevelSelect from './JobLevelSelect.vue';
import JobMethodSelect from './JobMethodSelect.vue';
import JobSalarySelect from './JobSalarySelect.vue';
import OccupationMultipleSelect from './OccupationMultipleSelect.vue';
import ProvinceMultipleSelect from './ProvinceMultipleSelect.vue';
import SkillMultipleTagsSelect from './SkillMultipleTagsSelect.vue';
import type { IsNil } from '~/types';

export interface IGeneralInformationFormModel {
  expectPosition: string;
  currentLevelId: number | null;
  expectLevelId: number | null;
  expectSalaryId: number | null;
  occupationIds: number[];
  jobEducationId: number | null;
  jobExperienceId: number | null;
  jobMethodId: number | null;
  jobSkills: string[];
  jobProvinceIds: number[];
  careerObjective: string | null;
}

const emit = defineEmits(['submit', 'cancel']);


const initValues = {
  expectPosition: 'Backend developer',
  currentLevelId: 2,
  expectLevelId: 2,
  expectSalaryId: 2,
  occupationIds: [1, 2, 23],
  jobEducationId: 4,
  jobExperienceId: 3,
  jobMethodId: 2,
  jobSkills: ['nodejs', 'backend'],
  jobProvinceIds: [1, 2, 3],
  careerObjective: `Sáu HC vàng của Trung Quốc ngày 10/8 tới từ Wang Liuyi, Wang Qianyi bơi nghệ thuật đôi nữ, Li Qian quyền Anh hạng dưới 75kg nữ, Cao Yuan nhảy cầu cứng 10m nam, Ding Xinyi, Guo Q`,
};

const props = defineProps({
  defaultValue: {
    type: Object as () => Resume | undefined | null,
    default: () => null,
  },
});

interface FormState {
  expectPosition: string;
  currentLevelId: number | null;
  expectLevelId: number | null;
  expectSalaryId: number | null;
  occupationIds: number[];
  jobEducationId: number | null;
  jobExperienceId: number | null;
  jobMethodId: number | null;
  jobSkills: string[];
  jobProvinceIds: number[];
  careerObjective: string | null;
}
const { values, handleSubmit, errors, defineField, setValues } = useForm<FormState>({
  validationSchema: toTypedSchema(
    object({
      expectPosition: string().required('Vui lòng nhập vị trí mong muốn'),
      currentLevelId: number().required('Vui lòng chọn cấp bậc hiện tại'),
      expectLevelId: number().required('Vui lòng chọn cấp bậc hiện tại'),
      expectSalaryId: number().required('Vui lòng chọn cấp bậc hiện tại'),
      occupationIds: array()
        .min(
          1,
          'Vui lòng chọn ít nhất 1 ngành nghề',
        )
        .of(number())
        .required('Vui lòng chọn ngành nghề'),
      jobEducationId: number().required('Vui lòng chọn trình độ học vấn'),
      jobExperienceId: number().required('Vui lòng chọn trình độ học vấn'),
      jobMethodId: number().required('Vui lòng chọn trình độ học vấn'),
      jobSkills: array().nullable().of(string()).required('Vui lòng chọn kỹ năng'),
      jobProvinceIds: array().of(number()).required('Vui lòng chọn địa điểm làm việc'),
      careerObjective: string().required('Vui lòng nhập mục tiêu nghề nghiệp'),
    }),
  ),
  initialValues: initValues,
});

const [expectPosition] = defineField('expectPosition');
const [currentLevelId] = defineField('currentLevelId');
const [expectLevelId] = defineField('expectLevelId');
const [expectSalaryId] = defineField('expectSalaryId');
const [occupationIds] = defineField('occupationIds');
const [jobEducationId] = defineField('jobEducationId');
const [jobExperienceId] = defineField('jobExperienceId');
const [jobMethodId] = defineField('jobMethodId');
const [jobSkills] = defineField('jobSkills');
const [jobProvinceIds] = defineField('jobProvinceIds');
const [careerObjective] = defineField('careerObjective');

const onCancel = () => {
  emit('cancel');
};
const setDefaultValue = () => {
  setValues(initValues)
}

const onSubmit = handleSubmit((values) => {
  const formValue: IGeneralInformationFormModel = {
    expectPosition: values.expectPosition,
    currentLevelId: values.currentLevelId || null,
    expectLevelId: values.expectLevelId || null,
    expectSalaryId: values.expectSalaryId || null,
    occupationIds: values.occupationIds || [],
    jobEducationId: values.jobEducationId || null,
    jobExperienceId: values.jobExperienceId || null,
    jobMethodId: values.jobMethodId,
    jobSkills: values.jobSkills || [],
    jobProvinceIds: values.jobProvinceIds || [],
    careerObjective: values.careerObjective || null,
  };
  emit('submit', formValue);
});

const mapDefaultValueToState = (defaultValue: Resume | IsNil) => {
  if (!defaultValue) {
    return;
  }
  expectPosition.value = defaultValue.expectPosition;
  currentLevelId.value = defaultValue.currentLevelId;
  expectLevelId.value = defaultValue.expectLevelId;
  expectSalaryId.value = defaultValue.expectSalary;
  occupationIds.value = defaultValue.occupationIds;
  jobEducationId.value = defaultValue.educationId;
  jobExperienceId.value = defaultValue.experienceId;
  jobMethodId.value = defaultValue.typeOfEmploymentId;
  jobSkills.value = defaultValue.softSkills;
  jobProvinceIds.value = defaultValue.provinceIds;
  careerObjective.value = defaultValue.careerObjective;
};

onMounted(() => {
  mapDefaultValueToState(props.defaultValue);
});
</script>
