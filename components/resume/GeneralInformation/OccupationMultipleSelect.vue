<template>
  <DropdownSelect
    v-if="useCommon.occupations"
    v-model:value="model"
    placeholder="Chọn ngành nghề"
    :items="useCommon.occupations"
    :border="border"
    :size="size"
    :max-size="10"
    :max-text-show="2"
  />
</template>

<script setup lang="ts">
import { defineModel, watch } from 'vue';

import DropdownSelect from '~/components/common/dropdownSelect.vue';
import { useCommonStore } from '~/store/common.store';

const useCommon = useCommonStore();

defineProps({
  border: {
    type: Boolean,
    default: true,
  },
  size: {
    type: String as () => 'sm' | 'md' | 'lg',
    default: 'md',
  },
});

const model = defineModel({
  type: Array as () => number[],
});

const value = ref<number[]>([]);

watch(
  () => value.value,
  () => {
    model.value = value.value;
  },
);
</script>
