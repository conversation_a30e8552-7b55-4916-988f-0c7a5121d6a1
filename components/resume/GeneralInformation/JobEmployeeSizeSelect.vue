<template>
  <DropdownSearchSelect
    :list="emptySelect ? [emptyValue, ...employeeSizes] : employeeSizes"
    id-key="id"
    value-key="name"
    placeholder="Chọn quy mô công ty"
    :show-search="true"
    @select="onSelect"
  />
</template>
<script setup lang="ts">
import { defineEmits } from 'vue';
import DropdownSearchSelect from '~/components/common/DropdownSearchSelect.vue';
import { useCommonStore } from '~/store/common.store';
const emits = defineEmits(['onChange']);
const { employeeSizes } = storeToRefs(useCommonStore());
// const model = defineModel<number | null>({
//   default: null,
//   type: Number as () => number | null,
// });

const props = defineProps({
  emptySelect: {
    type: Boolean,
    default: false,
  },
});

const emptyValue = {
  id: -1,
  name: '<PERSON><PERSON><PERSON> cả quy mô công ty',
};
const onSelect = (id: number) => {
  // model.value = id;
  // open.value = false;
  emits('onChange', id);
};
</script>
