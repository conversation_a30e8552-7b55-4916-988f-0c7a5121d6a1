<template>
  <DropdownSelect
    v-model:value="model"
    :items="provinces"
    :size="'md'"
    :max-size="3"
    :max-text-show="3"
  />
</template>
<script setup lang="ts">
import DropdownSelect from '~/components/common/dropdownSelect.vue';
import { useCommonStore } from '~/store/common.store';

const { provinces } = storeToRefs(useCommonStore());

const model = defineModel({
  type: Array as () => number[],
  default: [],
});
</script>
