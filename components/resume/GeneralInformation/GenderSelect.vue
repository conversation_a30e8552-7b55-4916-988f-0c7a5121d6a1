<template>
  <DropdownSearchSelect
    v-model:value="model"
    :list="emptySelect ? [emptyValue, ...genders] : genders"
    id-key="id"
    value-key="name"
    placeholder="Chọn giới tính"
    @select="onSelect"
  />
</template>
<script setup lang="ts">
import { defineEmits, defineModel } from "vue";
import DropdownSearchSelect from "~/components/common/DropdownSearchSelect.vue";
import { Gender } from "~/types";

const emptyValue = {
  id: "-1",
  name: "Tất cả giới tính",
};

const emits = defineEmits(["onChange"]);
const model = defineModel({
  default: null,
  type: String as () => string | null,
});

const genders = [
  { id: Gender.male, name: "Nam" },
  { id: Gender.female, name: "Nữ" },
];

const props = defineProps({
  emptySelect: {
    type: Boolean,
    default: false,
  },
});

const onSelect = (gender: Gender) => {
  model.value = gender;
  // open.value = false;
  console.log(model.value, gender);
  emits("onChange", gender);
};
</script>
