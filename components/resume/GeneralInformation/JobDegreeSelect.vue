<template>
  <DropdownSearchSelect
    :list="emptySelect ? [emptyValue, ...jobDegrees] : jobDegrees"
    id-key="id"
    value-key="name"
    placeholder="Chọn trình độ"
    @select="onSelect"
  />
</template>
<script setup lang="ts">
import DropdownSearchSelect from "~/components/common/DropdownSearchSelect.vue";
import { useCommonStore } from "~/store/common.store";

const { jobDegrees } = storeToRefs(useCommonStore());

const emptyValue = {
  id: -1,
  name: "Tất cả trình độ",
};
const emits = defineEmits(["onChange"]);
const props = defineProps({
  emptySelect: {
    type: Boolean,
    default: false,
  },
  value: {
    type: Number as () => number | null,
    default: null,
  },
});

const onSelect = (id: number) => {
  emits("onChange", id);
};
</script>
