<template>
  <div class="grid grid-cols-1 gap-4 lg:grid-cols-2">
    <GeneralInformationItem
      label="Vị trí mong muốn"
      :content="value?.expectPosition"
    />
    <GeneralInformationItem
      label="Cấp bậc hiện tại"
      :content="findJobLevelById(value?.currentLevelId)?.name"
    />
    <GeneralInformationItem
      label="Cấp bậc mong muốn"
      :content="findJobLevelById(value?.expectLevelId)?.name"
    />
    <GeneralInformationItem
      label="Mức lương mong muốn"
      :content="findSalaryById(value?.expectSalary)?.name"
    />
    <GeneralInformationItem
      label="Trình độ học vấn"
      :content="findDegreeById(value?.educationId)?.name"
    />
    <GeneralInformationItem
      label="Số năm kinh nghiệm"
      :content="findExperienceById(value?.experienceId)?.name"
    />
    <GeneralInformationItem
      label="Ngh<PERSON> nghiệp"
      :content="
        findOccupationByIds(value?.occupationIds)
          .map((item) => item.name)
          .join(', ')
      "
    />
    <GeneralInformationItem
      label="Địa điểm làm việc"
      :content="
        findProvinceByIds(value?.provinceIds)
          ?.map((item) => item.name)
          ?.join(', ')
      "
    />
    <GeneralInformationItem
      label="Hình thức làm việc"
      :content="findJobMethodById(value?.typeOfEmploymentId)?.name"
    />
    <GeneralInformationItem
      v-if="value?.careerObjective"
      label="Mục tiêu nghề nghiệp"
      :content="value?.careerObjective"
    />
    <GeneralInformationItem
      label="Kỹ năng mềm / kỹ năng cứng"
      :content="value?.softSkills?.join(', ')"
    />
  </div>
</template>
<script setup lang="ts">
import { defineProps } from "vue";
import { useCommonStore } from "~/store/common.store";
import type { Resume } from "~/types/resume.interface";
import GeneralInformationItem from "./GeneralInformationItem.vue";

const {
  jobLevels,
  findJobLevelById,
  findSalaryById,
  findDegreeById,
  findExperienceById,
  findOccupationByIds,
  findJobMethodById,
  findProvinceByIds,
} = useCommonStore();

defineProps({
  value: {
    type: Object as () => Resume | null | undefined,
    required: false,
    default: () => null,
  },
});
</script>
