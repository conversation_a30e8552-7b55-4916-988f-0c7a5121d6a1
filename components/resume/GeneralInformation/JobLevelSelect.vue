<template>
  <DropdownSearchSelect
    v-model:model-value="model"
    :list="emptySelect ? [emptyValue, ...jobLevels] : jobLevels"
    id-key="id"
    value-key="name"
    placeholder="<PERSON><PERSON><PERSON> mức độ thành thạo"
    @select="onSelect"
  />
</template>
<script setup lang="ts">
import { defineEmits, defineModel } from "vue";
import DropdownSearchSelect from "~/components/common/DropdownSearchSelect.vue";
import { useCommonStore } from "~/store/common.store";
import type { JobLevel } from "~/types";
const emits = defineEmits(["onChange"]);
const { jobLevels } = storeToRefs(useCommonStore());
const model = defineModel<number | null>({
  default: null,
  type: Number as () => number | null,
});

const props = defineProps({
  emptySelect: {
    type: Boolean,
    default: false,
  },
});

const emptyValue = {
  id: -1,
  name: "<PERSON><PERSON><PERSON> c<PERSON> mứ<PERSON> l<PERSON>ơ<PERSON>",
};

// const open = ref(false);

// const findValue = (id: number | null) => {
//   return findJobLevelById(id)?.name || (props.emptySelect ? emptyValue.name : undefined);
// };

const onSelect = (id: number) => {
  // model.value = id;
  // open.value = false;
  emits("onChange", id);
};
</script>
