<template>
  <DropdownSearchSelect
    :list="[...jobExperiences]"
    id-key="id"
    value-key="name"
    placeholder="<PERSON>ức độ thành thạo"
    @select="onSelect"
  />
</template>
<script setup lang="ts">
import { defineEmits, defineModel } from "vue";
import DropdownSearchSelect from "~/components/common/DropdownSearchSelect.vue";
import { useCommonStore } from "~/store/common.store";

const { jobExperiences } = storeToRefs(useCommonStore());

const emptyValue = {
  id: -1,
  name: "<PERSON><PERSON>t cả kinh nghiệ<PERSON>",
};

const emits = defineEmits(["onChange"]);

const props = defineProps({
  emptySelect: {
    type: Boolean,
    default: false,
  },
});

const onSelect = (id: number) => {
  emits("onChange", id);
};
</script>
