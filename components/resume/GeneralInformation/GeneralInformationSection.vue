<template>
  <Collapse
    title="Thông tin chung"
    :show-adding="false"
    :show-edit="true"
    :required="props.required"
    :valid="props.valid"
    :loading="useResume.updatePending"
    @on-edit="formOpen = !formOpen"
  >
    <GeneralInformationDetail
      v-if="!formOpen"
      :value="resume"
    />
    <GeneralInformationForm
      v-if="formOpen"
      :default-value="resume"
      @submit="onSubmit"
      @cancel="onCancel"
    />
  </Collapse>
</template>
<script setup lang="ts">
import Collapse from '~/components/common/collapse.vue';
import { useResumeStore } from '~/store/resume';
import { ResumeType, type CanBeNil, type Resume } from '~/types';
import GeneralInformationDetail from './GeneralInformationDetail.vue';
import GeneralInformationForm, { type IGeneralInformationFormModel } from './GeneralInformationForm.vue';
import type { PropType } from 'vue';
const props = defineProps({
  resume: {
    type: Object as () => CanBeNil<Resume>,
    required: false,
    default: null,
  },
  formOpenAtStart: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String as unknown as PropType<ResumeType>,
    required: true,
  },
  required: {
    type: Boolean,
    default: false,
  },
  valid: {
    type: Boolean,
    default: false,
  },
});
const useResume = useResumeStore();
const formOpen = ref(false);

const onSubmit = (value: IGeneralInformationFormModel) => {
  if (props.resume && props.resume.id) {
    useResume.updateGeneralInfo(props.resume.id, {
      type: props.resume.type,
      expectPosition: value.expectPosition,
      expectSalary: value.expectSalaryId,
      expectLevelId: value.expectLevelId,
      currentLevelId: value.currentLevelId,
      educationId: value.jobEducationId,
      experienceId: value.jobExperienceId,
      softSkills: value.jobSkills,
      provinceIds: value.jobProvinceIds,
      occupationIds: value.occupationIds,
      careerObjective: value.careerObjective,
      typeOfEmploymentId: value.jobMethodId,
    });
  } else {
    useResume.createResume({
      type: props.type,
      expectPosition: value.expectPosition,
      expectSalary: value.expectSalaryId,
      expectLevelId: value.expectLevelId,
      currentLevelId: value.currentLevelId,
      educationId: value.jobEducationId,
      experienceId: value.jobExperienceId,
      softSkills: value.jobSkills,
      provinceIds: value.jobProvinceIds,
      occupationIds: value.occupationIds,
      careerObjective: value.careerObjective,
      typeOfEmploymentId: value.jobMethodId,
    });
  }
};

const onCancel = () => {
  formOpen.value = false;
};

onMounted(() => {
  // if (!props.resume) {
  //   formOpen.value = true;
  // }
  if (props.formOpenAtStart) {
    formOpen.value = true;
  }
});

watch(
  () => useResume.createSuccess,
  (value, oldValue) => {
    if (value && value !== oldValue && useResume.resume) {
      let path = '/ho-so-cua-ban/cap-nhat-ho-so';
      if (useResume.resume.type === ResumeType.online) {
        path = '/ho-so-cua-ban/cap-nhat-ho-so';
      } else if (useResume.resume.type === ResumeType.offline) {
        path = '/ho-so-cua-ban/ho-so-dinh-kem';
      }
      useRouter().push({
        path,
        query: {
          resume_id: useResume.resume.id,
        },
      });
      formOpen.value = false;
    }
  },
  // { immediate: true },
);

watch(
  () => useResume.updateSuccess,
  (value, oldValue) => {
    if (value && value !== oldValue) {
      formOpen.value = false;
    }
  },
  // { immediate: true },
);
</script>
