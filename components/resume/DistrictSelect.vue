<template>
  <DropdownSearchSelect
    :list="emptySelect ? [emptyValue, ...matchedDistricts] : matchedDistricts"
    id-key="id"
    value-key="name"
    placeholder="Chọn quận/huyện"
    :show-search="true"
    :bordered="bordered"
    @select="onSelect"
  />
</template>
<script setup lang="ts">
import { useCommonStore } from '~/store/common.store';
import DropdownSearchSelect from '../common/DropdownSearchSelect.vue';

const { districts } = storeToRefs(useCommonStore());
const emits = defineEmits(['select']);

// const model = defineModel({
//   type: Number as () => number | null,
//   default: null,
// });

const props = defineProps({
  emptySelect: {
    type: Boolean,
    default: false,
  },
  provinceId: {
    type: Number as () => number | null,
    default: null,
  },
  bordered: {
    type: Boolean,
    default: true,
  },
});

const matchedDistricts = computed(() => {
  if (props.provinceId === -1) {
    return districts.value;
  }
  return districts.value.filter((district) => district.parentId === props.provinceId);
});

const emptyValue = {
  id: -1,
  name: 'Tất cả quận/huyện',
};

const onSelect = (id: number | string | null) => {
  // model.value = id;
  emits('select', id);
};
</script>
