<template>
  <DropdownSearchSelect
    :list="emptySelect ? [emptyValue, ...provinces] : provinces"
    id-key="id"
    value-key="name"
    placeholder="Chọn tỉnh thành"
    :bordered="bordered"
    :show-search="true"
    :size="size"
    @select="emits('select', $event)"
  />
</template>
<script setup lang="ts">
import { useCommonStore } from '~/store/common.store';
import DropdownSearchSelect from '../common/DropdownSearchSelect.vue';

const emits = defineEmits(['select']);
const { provinces } = storeToRefs(useCommonStore());

const props = defineProps({
  emptySelect: {
    type: Boolean,
    default: false,
  },
  size: {
    type: String as () => 'sm' | 'md' | 'lg',
    default: 'md',
  },
  bordered: {
    type: Boolean,
    default: true,
  },
});

const emptyValue = {
  id: -1,
  name: 'Toàn quốc',
};
</script>
