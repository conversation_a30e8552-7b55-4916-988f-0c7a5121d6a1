<template>
  <Collapse
    title="Tin học"
    adding-text="Thêm tin học văn phòng"
    :show-adding="true"
    :loading="useResume.updateTechPending || useResume.createTechPending"
    @on-adding="openEditForm"
  >
    <template v-if="useResume.resume?.techs?.length && !addingFormOption">
      <div class="flex justify-between space-x-2">
        <div class="space-x-2 flex flex-wrap">
          <Tag
            v-for="(skill, idx) in useResume.resume?.techs?.[0]?.skills"
            :key="idx"
            >{{ skill }}</Tag
          >
        </div>
        <EditButton @click="openEditForm" />
      </div>
    </template>
    <Empty
      v-else-if="!addingFormOption"
      description="Thông tin tin học văn phòng rỗng"
      size="sm"
    />
    <Divider />
    <ITForm
      v-if="addingFormOption"
      :skills="useResume.resume?.techs?.[0]?.skills"
      @on-submit="onUpdate"
      @on-cancel="onCancelUpdatingForm"
    />
  </Collapse>
</template>
<script setup lang="ts">
import { useResumeStore } from '~/store/resume';
import Collapse from '../common/collapse.vue';
import EditButton from '../common/EditButton.vue';
import Tag from '../common/Tag.vue';
import ITForm, { type ITFormModel } from './ITForm.vue';
import type { Resume, ResumeTech } from '~/types';
import Empty from '../common/Empty.vue';

const useResume = useResumeStore();
// const { resume } = storeToRefs(useResumeStore());
// const { updateITSkills, createITSkills } = useResumeStore();
// const techs = resume.value?.techs;
// const tech = techs?.[0] as ResumeTech | undefined;

// const tech = ref<ResumeTech | null>(null);

const addingFormOption = ref<boolean>(false);
const updateValue = ref<ITFormModel | null>(null);

const openEditForm = () => {
  const skills = useResume.resume?.techs?.[0]?.skills || [];
  updateValue.value = { softwares: skills };
  addingFormOption.value = true;
};

const onUpdate = (model: ITFormModel) => {
  if (!useResume.resume) return;

  const firstTech = useResume.resume?.techs?.[0];
  if (firstTech) {
    useResume.updateITSkills(firstTech.resumeId, firstTech.id, {
      skills: model.softwares,
    });
  } else {
    useResume.createITSkills(useResume.resume.id, {
      skills: model.softwares,
    });
  }
};

const onCancelUpdatingForm = () => {
  addingFormOption.value = false;
};

/* watch(
  () => useResumeStore().resume,
  (val: Resume | null) => {
    if (val) {
      if (val.techs?.length) {
        tech.value = val.techs?.[0];
      }
    }
  },
);

watch(
  () => useResumeStore().createTechSuccess,
  (val: boolean, oldValue: boolean) => {
    if (val && !oldValue) {
      addingFormOption.value = false;
    }
  },
);

watch(
  () => useResumeStore().updateTechSuccess,
  (val: boolean, oldValue: boolean) => {
    if (val && !oldValue) {
      addingFormOption.value = false;
    }
  },
); */
</script>
