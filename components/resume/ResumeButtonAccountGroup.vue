<template>
  <div class="space-x-2">
    <button
      class="btn btn-xs !btn-ghost"
      @click="deleteItem"
    >
      <IconTrash class="w-4" />
      Xóa
    </button>
    <button
      class="btn btn-xs !btn-ghost"
      @click="editItem"
    >
      <IconPen class="w-4" />
      Sửa
    </button>
  </div>
</template>
<script setup lang="ts">
import IconTrash from '../icons/IconTrash.vue';
import IconPen from '../icons/IconPen.vue';
const emit = defineEmits(['onDelete', 'onEdit']);

const deleteItem = () => {
  emit('onDelete');
};

const editItem = () => {
  emit('onEdit');
};
</script>
