<template>
  <div
    :class="[
      'text-blue-400 bg-blue-50 rounded-md px-8 py-8 flex justify-between items-center mb-6',
      alertOpen ? 'block' : 'hidden',
    ]"
  >
    <div class="flex justify-between items-center">
      <div class="mr-10">
        <IconQuestion16Filled class="w-8" />
      </div>
      <ul class="list-disc text-xs font-semibold">
        <li class="py-1">Bạn có thể tạo tối đa 02 hồ sơ.</li>
        <li class="py-1"><PERSON><PERSON>t “Cho phép tìm kiếm” sẽ tăng tối đa cơ hội được Nhà tuyển dụng liên hệ với bạn.</li>
        <li class="py-1">Bật “Cho phép tìm kiếm” sẽ tăng tối đa cơ hội được Nhà tuyển dụng liên hệ với bạn.</li>
      </ul>
    </div>
    <button
      class="justify-end text-gray-600"
      @click="alertOpen = false"
    >
      <IconClearRound class="w-8" />
    </button>
  </div>
</template>
<script setup lang="ts">
import IconQuestion16Filled from '../icons/IconQuestion16Filled.vue';
import IconClearRound from '../icons/IconClearRound.vue';
const alertOpen = ref(true);
</script>
