<template>
  <div ref="refForm">
    <div class="grid lg:grid-cols-2 lg:gap-4">
      <FormItem
        label="Họ và tên"
        :required="true"
        :error="errorModel.name"
      >
        <input
          v-model="model.name"
          type="text"
          class="input input-bordered w-full"
          placeholder="Nhập họ và tên"
        />
      </FormItem>
      <FormItem
        label="Số điện thoại"
        :required="true"
        :error="errorModel.phone"
      >
        <input
          v-model="model.phone"
          type="text"
          class="input input-bordered w-full"
          placeholder="Nhập số điện thoại"
        />
      </FormItem>
      <FormItem
        label="Tên công ty"
        :required="true"
        :error="errorModel.company"
      >
        <textarea
          class="textarea w-full textarea-bordered"
          :value="model.company"
          placeholder="Nhập tên công ty"
          @change="onCompanyChange"
        />
      </FormItem>
      <FormItem
        label="<PERSON><PERSON><PERSON> vụ"
        :required="true"
        :error="errorModel.position"
      >
        <input
          v-model="model.position"
          type="text"
          class="input input-bordered w-full"
          placeholder="Nhập chức vụ"
        />
      </FormItem>
    </div>
    <div class="flex justify-center md:justify-end space-x-2">
      <CancelButton
        :title="props.cancelText"
        @click="emit('onCancel')"
      />
      <OkButton
        :text="props.okText"
        @click="onOk"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
import { defineModel, ref, watch, toRefs, toRef } from 'vue';
import CancelButton from '../common/CancelButton.vue';
import OkButton from '../common/OkButton.vue';
import FormItem from '../form/formItem.vue';

const emit = defineEmits(['onOk', 'onCancel']);
const refForm = ref<HTMLElement | null>(null);

export interface IRefFormModel {
  name: string;
  phone: string;
  company: string;
  position: string;
}
const model = defineModel({
  type: Object as () => IRefFormModel,
  default: () => ({
    name: '',
    phone: '',
    company: '',
    position: '',
  }),
});

const props = defineProps({
  defaultValue: {
    type: Object as () => IRefFormModel | null,
    default: null,
    required: false,
  },
  okText: {
    type: String,
    default: 'Lưu thông tin',
  },
  cancelText: {
    type: String,
    default: 'Hủy',
  },
});

const defaultValue = toRef(props.defaultValue);

const errorModel = ref({
  name: '',
  phone: '',
  company: '',
  position: '',
});

const validate = () => {
  errorModel.value = {
    name: model.value.name ? '' : 'Vui lòng nhập họ và tên',
    phone: model.value.phone ? '' : 'Vui lòng nhập số điện thoại',
    company: model.value.company ? '' : 'Vui lòng nhập tên công ty',
    position: model.value.position ? '' : 'Vui lòng nhập chức vụ',
  };

  if (model.value.phone && !/^\d{10}$/.test(model.value.phone)) {
    errorModel.value.phone = 'Số điện thoại không hợp lệ. Số điện thoại phải có 10 số';
  }

  return Object.values(errorModel.value).every((error) => !error);
};
const onCompanyChange = (e: Event) => {
  model.value.company = (e.target as HTMLInputElement).value;
};
const onOk = () => {
  if (validate()) {
    emit('onOk', model.value);
  }
};

onMounted(() => {
  if (defaultValue.value) {
    model.value = defaultValue.value;
  }

  nextTick(() => {
    // Focus on the first input field
    if (refForm.value) {
      const firstInput = refForm.value.querySelector('input, textarea');
      if (firstInput) {
        (firstInput as HTMLInputElement).focus();
      }
    }
  })
});
</script>
