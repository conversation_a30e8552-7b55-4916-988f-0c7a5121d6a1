<template>
  <div class="fixed bottom-0 bg-white w-full border border-gray-200 md:max-w-[calc(100%-16rem)] md:left-64">
    <div class="flex justify-between px-6 py-3 items-center">
      <div>
        <!-- <div class="form-control">
          <label class="label cursor-pointer">
            <input
              type="checkbox"
              class="toggle toggle-primary"
              :checked="isSearchAllowed === TinyInt.Yes"
              @change="onSearchAllowed"
            />
            <span class="label-text ml-2 !text-xs font-light"
              >Cho phép nhà tuyển dụng tìm kiếm hồ sơ trực tuyến của bạn</span
            >
          </label>
        </div> -->
      </div>
      <div class="flex justify-end space-x-4">
        <button
          class="btn btn-soft btn-primary btn-lg !text-sm"
          @click="emits('onPreview')"
        >
          Xem trước
        </button>
        <button
          class="btn btn-primary btn-lg !text-sm"
          @click="onRequestApproval"
        >
          <PERSON><PERSON><PERSON> c<PERSON>
        </button>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { TinyInt } from '~/types';
export interface ResumeFormFooterSubmitPayload {
  // isSearchAllowed: TinyInt;
  requestApproval: TinyInt;
}

const emits = defineEmits({
  onPreview: null,
  onSubmit: (payload: ResumeFormFooterSubmitPayload) => true,
  onRequestApproval: (payload: ResumeFormFooterSubmitPayload) => true,
});

const props = defineProps({
  isSearchAllowed: {
    type: Number as PropType<TinyInt>,
    default: TinyInt.No,
  },
});

const allowedSearching = ref<TinyInt>(TinyInt.No);

// const onSearchAllowed = (e: Event) => {
//   allowedSearching.value = (e.target as HTMLInputElement).checked ? TinyInt.Yes : TinyInt.No;
// };

// const onSubmit = () => {
//   const payload: ResumeFormFooterSubmitPayload = {
//     isSearchAllowed: allowedSearching.value,
//   };
//   emits('onSubmit', payload);
// };

const onRequestApproval = () => {
  const payload: ResumeFormFooterSubmitPayload = {
    requestApproval: TinyInt.Yes,
  };
  emits('onRequestApproval', payload);
};

onMounted(() => {
  allowedSearching.value = props.isSearchAllowed;
});

watch(
  () => props.isSearchAllowed,
  (value) => {
    allowedSearching.value = value;
  },
);
</script>
