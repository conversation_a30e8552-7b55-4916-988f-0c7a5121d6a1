<template>
  <div>
    <FormItem
      label="Tên phần mềm"
      :required="false"
    >
      <input
        v-model="softwareInput"
        type="text"
        class="input input-bordered w-full"
        placeholder="Vd: Microsoft Office"
        @keydown.enter="addTag"
      />
      <p class="text-xs font-light my-2">Nhấn "Enter" để thêm</p>
      <div class="space-x-2 flex flex-wrap">
        <Tag
          v-for="(software, i) in model.softwares"
          :clear="true"
          @click="removeTag(i)"
          >{{ software }}</Tag
        >
      </div>
    </FormItem>
    <div class="flex justify-center md:justify-end space-x-2">
      <CancelButton
        title="Hủy"
        @click="emit('onCancel')"
      />
      <OkButton
        text="Lưu thông tin"
        @click="onSubmit"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
import { defineModel, defineEmits } from 'vue';
import Tag from '~/components/common/Tag.vue';
import FormItem from '~/components/form/formItem.vue';
import CancelButton from '../common/CancelButton.vue';
import OkButton from '../common/OkButton.vue';

const emit = defineEmits(['onSubmit', 'onCancel']);

export interface ITFormModel {
  softwares: string[];
}

const model = defineModel<ITFormModel>({
  type: Object as () => ITFormModel,
  default: () => ({ softwares: [] }),
});

const props = defineProps({
  skills: {
    type: Array as () => string[],
    default: () => [],
  },
});

const softwareInput = ref('');

const addTag = () => {
  if (softwareInput.value) {
    const newSoftware = softwareInput.value.trim();
    const isExist = model.value.softwares?.some((software) => software === newSoftware);
    if (!isExist) {
      if (!model.value.softwares) {
        model.value.softwares = [];
      }
      model.value.softwares.push(newSoftware);
    }
    softwareInput.value = '';
  }
};

const removeTag = (index: number) => {
  model.value.softwares.splice(index, 1);
  model.value = { ...model.value };
};

const onSubmit = () => {
  emit('onSubmit', model.value);
};

onMounted(() => {
  console.log('onMounted: ', props.skills);
  if (props.skills) {
    model.value.softwares = props.skills;
    model.value = { ...model.value };
  }
});
</script>
