<template>
  <div
    v-if="value"
    class="flex justify-between my-4"
  >
    <div>
      <div class="font-semibold text-sm">
        <span>{{ value.fullName }}</span>
        <Dot />
        <span>{{ value.phone }}</span>
      </div>
      <div class="text-sm font-light text-gray-500">
        <span>{{ value.companyName }}</span>
        <Dot />
        <span>{{ value.position }}</span>
      </div>
    </div>
    <ResumeButtonAccountGroup
      @on-delete="onDelete"
      @on-edit="onEdit"
    />
  </div>
</template>
<script setup lang="ts">
import type { ResumeRef } from "~/types/resume.interface";
import Dot from "../common/Dot.vue";
import ResumeButtonAccountGroup from "./ResumeButtonAccountGroup.vue";

const emit = defineEmits(["onDelete", "onEdit"]);

defineProps({
  value: {
    type: Object as () => ResumeRef,
    required: true,
  },
});

const onDelete = () => {
  emit("onDelete");
};

const onEdit = () => {
  emit("onEdit");
};
</script>
