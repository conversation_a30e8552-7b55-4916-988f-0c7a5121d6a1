<template>
  <div class="dropdown w-full">
    <label class="input input-info input-bordered w-full flex items-center gap-2">
      <input
        type="text"
        class="w-full"
        :value="model"
        placeholder="Tì<PERSON> kiếm trường / trung tâm đào tạo"
        @input="search"
      />
      <a
        v-if="modelValue"
        href="javascript:void(0)"
        @click="onClear"
      >
        <IconClearRound class="w-6 hover:cursor-pointer" />
      </a>
    </label>
    <ul
      v-if="list.length"
      class="menu dropdown-content bg-base-100 z-[1] w-full p-2 shadow mt-2 max-h-44 block overflow-hidden overflow-y-scroll hide-scrollbar"
    >
      <li
        v-for="item in list"
        :key="item.id"
      >
        <a
          href="javascript:void(0)"
          @click="onSelect(item)"
          >{{ item.name }}</a
        >
      </li>
    </ul>
  </div>
</template>
<script setup lang="ts">
import type { University } from '~/types/university.interface';
import IconClearRound from '../icons/IconClearRound.vue';

const baseUrl = useRuntimeConfig().public.apiUrl;
const timeout = 1000;
const timeoutTrigger = ref<NodeJS.Timeout | null>(null);

// const keyword = ref('');
const model = defineModel<string | undefined | null>({
  type: String,
});
const list = ref<University[]>([]);

const search = (e: Event) => {
  model.value = (e.target as HTMLInputElement).value;
  if (timeoutTrigger.value) {
    clearTimeout(timeoutTrigger.value);
  }
  timeoutTrigger.value = setTimeout(async () => {
    try {
      const response = await fetch(`${baseUrl}/v1/universities/keyword?keyword=${model.value}`);
      const universities = await response.json();
      list.value = universities;
    } catch (error) {
      console.error(error);
    }
  }, timeout);
};
const onSelect = (university: University) => {
  console.warn(university);
  if (!university) {
    return;
  }
  model.value = university.name;
  list.value = [];
};
const onClear = () => {
  model.value = '';
  list.value = [];
};
</script>
