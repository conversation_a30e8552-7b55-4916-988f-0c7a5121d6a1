<template>
  <div class="my-4">
    <div class="font-semibold text-sm flex justify-between">
      <div>{{ title }}</div>
      <div class="space-x-2">
        <button
          class="btn btn-xs !bg-white !btn-ghost font-light"
          @click="emit('onDelete')"
        >
          <IconTrash class="w-4 h-4" />
          Xóa
        </button>
        <EditButton @click="emit('onEdit')" />
      </div>
    </div>
    <p class="text-sm font-normal text-gray-500">{{ subTitle }}</p>
    <p
      v-if="extra"
      class="text-sm font-semibold text-gray-700 my-2"
    >
      {{ extra }}
    </p>
    <p class="text-sm font-normal text-gray-500">
      <span>{{ formatDate(from) }}</span>
      <span v-if="to">{{ ' - ' }}</span>
      <span>{{ formatDate(to) }}</span>
    </p>
  </div>
</template>
<script setup lang="ts">
import moment from 'moment';
import { defineEmits, defineProps } from 'vue';
import EditButton from '../../common/EditButton.vue';
import IconTrash from '~/components/icons/IconTrash.vue';

const emit = defineEmits(['onEdit', 'onDelete']);

defineProps({
  title: String,
  subTitle: String,
  from: String,
  to: {
    type: String,
    required: false,
    default: null,
  },
  extra: String,
});

const formatDate = (date: string | null | undefined) => {
  if (!date) return '';
  return moment(date).format('MM/YYYY');
};
</script>
