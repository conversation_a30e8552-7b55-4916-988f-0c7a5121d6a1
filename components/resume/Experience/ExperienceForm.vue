<template>
  <div>
    <button class="btn btn-primary btn-outline w-full" @click="setDefaultValue">set default value</button>
    <div class="grid grid-cols-1 lg:grid-cols-2 lg:gap-8">
      <FormItem
        label="Chức danh hoặc vị trí"
        :required="true"
        :error="errorModel.position"
      >
        <input
          v-model="position"
          type="text"
          class="input input-bordered w-full"
          placeholder="Chức danh hoặc vị trí"
        />
      </FormItem>
      <FormItem
        label="Tên công ty"
        :required="true"
        :error="errorModel.company"
      >
        <input
          v-model="company"
          type="text"
          class="input input-bordered w-full"
          placeholder="Tên công ty"
        />
      </FormItem>
    </div>
    <div class="grid lg:grid-cols-5">
      <CheckBox
        v-model="current"
        text="Tôi đang làm ở đây"
      />
    </div>
    <div class="grid lg:grid-cols-2 lg:gap-8">
      <FormItem
        label="Thời gian bắt đầu"
        :error="errorModel.from"
        :required="true"
      >
        <MonthYearPicker v-model="from" />
      </FormItem>
      <FormItem
        v-if="!current"
        label="Thời gian kết thúc"
        :error="errorModel.to"
      >
        <MonthYearPicker v-model="to" />
      </FormItem>
      <div
        v-else
        class="flex items-center"
      >
        <IconChevronRight16Filled class="w-6" />
        <div class="ml-4">Hiện tại</div>
      </div>
    </div>
    <FormItem
      label="Mô tả công việc"
      :error="errorModel.description"
      :required="true"
    >
      <textarea
        class="textarea w-full textarea-bordered"
        :value="description"
        placeholder="Mô tả công việc"
        @change="onDescChange"
      />
    </FormItem>

    <div class="flex justify-center md:justify-end space-x-2">
      <CancelButton
        title="Hủy"
        @click="emits('onCancel')"
      />
      <OkButton
        text="Lưu thông tin"
        @click="onOk"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
import { defineEmits, ref } from 'vue';
import type { ResumeExperience } from '~/types/resume.interface';
import CancelButton from '../../common/CancelButton.vue';
import CheckBox from '../../common/CheckBox.vue';
import MonthYearPicker from '../../common/MonthYearPicker.vue';
import OkButton from '../../common/OkButton.vue';
import FormItem from '../../form/formItem.vue';
import IconChevronRight16Filled from '~/components/icons/IconChevronRight16Filled.vue';

const emits = defineEmits(['onCancel', 'onOk']);

export interface IExperienceFormModel {
  position: string; // chức danh hoặc vị trí, english: title or position
  company: string; // tên công ty, english: company
  current: boolean; // đang làm ở đây, english: current
  from: string; // thời gian bắt đầu, english: from
  to: string; // thời gian kết thúc, english: to
  description: string; // mô tả công việc, english: description
}

const props = defineProps({
  defaultValue: {
    type: Object as () => ResumeExperience | null,
    default: () => null,
  },
});

/* const position = ref('Backend developer');
const company = ref('inspired Lab');
const current = ref(false);
const from = ref({ year: 2018, month: 1 });
const to = ref({ year: 2018, month: 12 });
const description = ref('Backend developer at inspired Lab'); */

const position = ref('');
const company = ref('');
const current = ref(false);
const from = ref({ year: -1, month: -1 });
const to = ref({ year: -1, month: -1 });
const description = ref('');

const errorModel = ref({
  position: '',
  company: '',
  from: '',
  to: '',
  description: '',
});

const setDefaultValue = () => {
  position.value = 'Backend developer';
  company.value = 'inspired Lab';
  current.value = false;
  from.value = { year: 2018, month: 1 };
  to.value = { year: 2018, month: 12 };
  description.value = 'Backend developer at inspired Lab';
}

const onDescChange = (e: Event) => {
  description.value = (e.target as HTMLTextAreaElement).value;
};

const validate = () => {
  errorModel.value = {
    position: position.value ? '' : 'Vui lòng nhập chức danh hoặc vị trí',
    company: company.value ? '' : 'Vui lòng nhập tên công ty',
    from: from.value.year && from.value.month ? '' : 'Vui lòng chọn thời gian bắt đầu',
    to: current.value || (to.value.year && to.value.month) ? '' : 'Vui lòng chọn thời gian kết thúc',
    description: description.value ? '' : 'Vui lòng nhập mô tả công việc',
  };

  if (!current.value && from.value.year && to.value.year) {
    const fromDate = new Date(`${from.value.year}-${from.value.month}-01`);
    const toDate = new Date(`${to.value.year}-${to.value.month}-01`);
    if (fromDate > toDate) {
      errorModel.value.to = 'Thời gian kết thúc phải sau thời gian bắt đầu';
    }
  }

  return Object.values(errorModel.value).every((error) => !error);
};

const formatDate = (date: { year: number; month: number }) => {
  if (!date || !date.year || !date.month || date.year < 0 || date.month < 0) return null;
  const month = date.month > 9 ? date.month : [0, date.month].join(''); // 1 -> 01 for month
  return `${date.year}-${month}-01`;
};

const onOk = () => {
  if (validate()) {
    console.log('ok');
    emits('onOk', {
      position: position.value,
      company: company.value,
      current: current.value,
      from: formatDate(from.value),
      to: formatDate(to.value),
      description: description.value,
    });
  }
};
onMounted(() => {
  const defaultValue = props.defaultValue;
  // console.log('default value ===> ', defaultValue);
  if (defaultValue) {
    if (defaultValue.companyName) company.value = defaultValue.companyName;
    if (defaultValue.position) position.value = defaultValue.title;
    if (defaultValue.isCurrent) current.value = defaultValue.isCurrent === 1;
    if (defaultValue.startDate)
      from.value = {
        year: +defaultValue.startDate.split('-')[0],
        month: +defaultValue.startDate.split('-')[1],
      };
    if (defaultValue.endDate)
      to.value = {
        year: +defaultValue.endDate.split('-')[0],
        month: +defaultValue.endDate.split('-')[1],
      };
    if (defaultValue.description) description.value = defaultValue.description;

    /* console.warn(
      'default value ===> ',
      company.value,
      position.value,
      current.value,
      from.value,
      to.value,
      description.value,
    ); */
  }
});
</script>
