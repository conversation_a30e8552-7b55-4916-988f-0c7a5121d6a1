<template>
  <Collapse
    title="Kinh nghiệm làm việc"
    adding-text="Thêm kinh nghiệm làm việc"
    :required="true"
    :valid="tinyintToBoolean(resume?.experienceCompleted)"
    @on-adding="formOpen = !formOpen"
  >
    <template v-if="resume?.experiences?.length">
      <ExperienceItem
        v-for="item in resume.experiences"
        :key="`experience-item-${item.id}`"
        :title="item.title"
        :sub-title="item.companyName"
        :from="item.startDate"
        :to="item.endDate || undefined"
        @on-edit="onUpdate(item)"
        @on-delete="onDelete(item)"
      />
    </template>
    <Empty v-else-if="!formOpen" description="Thông tin kinh nghiệm rỗng" />
    <ExperienceForm
      v-if="formOpen"
      :default-value="updateValue"
      @on-cancel="onCancel"
      @on-ok="onOk"
    />
  </Collapse>
</template>
<script setup lang="ts">
import { storeToRefs } from 'pinia';
import Collapse from '~/components/common/collapse.vue';
import { useResumeStore } from '~/store/resume';
import type { Resume, ResumeExperience } from '~/types/resume.interface';
import ExperienceForm, { type IExperienceFormModel } from './ExperienceForm.vue';
import ExperienceItem from './experienceItem.vue';
import Empty from '~/components/common/Empty.vue';

const { resume } = storeToRefs(useResumeStore());
const { createExperience, updateExperience, removeExperience } = useResumeStore();

const formOpen = ref(false);
const updateValue = ref<ResumeExperience | null>(null);

const onCancel = () => {
  formOpen.value = false;
  updateValue.value = null;
};

const onUpdate = (value: ResumeExperience) => {
  updateValue.value = value;
  formOpen.value = true;
};

const onOk = (value: IExperienceFormModel) => {
  if (!resume.value?.id) {
    return;
  }

  const { position, company, current, from, to, description } = value;
  if (!position || !company || !from || (!current && !to) || !description) {
    return;
  }

  if (updateValue.value) {
    updateExperience(updateValue?.value.resumeId, updateValue.value.id, {
      title: position,
      companyName: company,
      startDate: from,
      endDate: to,
      description,
      position,
      isCurrent: +value.current,
    });
  } else {
    createExperience(resume.value.id, {
      title: position,
      companyName: company,
      startDate: from,
      endDate: to,
      description,
      position,
      isCurrent: +value.current,
    });
  }
  // onCancel();
};

const onDelete = (value: ResumeExperience) => {
  removeExperience(value.resumeId, value.id);
};

watch(
  () => useResumeStore().resume,
  (value: Resume | null) => {
    if (value?.experiences?.length === 0) {
      // formOpen.value = true;
      onCancel();
    }
  },
);
watch(
  () => useResumeStore().updateExperienceSuccess,
  (value: boolean, oldValue: boolean) => {
    if (value && !oldValue) {
      // formOpen.value = false;
      onCancel();
    }
  },
);
watch(
  () => useResumeStore().createExperienceSuccess,
  (value: boolean, oldValue: boolean) => {
    if (value && !oldValue) {
      // formOpen.value = false;
      onCancel();
    }
  },
);
</script>
