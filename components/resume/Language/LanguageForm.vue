<template>
  <div>
    <button class="btn btn-primary btn-outline w-full" @click="setDefaultValue">set default value</button>
    <div class="grid lg:grid-cols-2 lg:gap-4">
      <FormItem
        label="Ngoại ng<PERSON>"
        :required="true"
        title="Không bắt buộc"
        :error="rules['language']"
      >
        <LanguageSelect v-model="languageId" />
      </FormItem>
      <FormItem
        label="Trình độ"
        :required="true"
        :error="rules['levelId']"
      >
        <LanguageLevelSelect v-model="levelId" />
      </FormItem>
    </div>
    <div class="flex justify-center md:justify-end space-x-2">
      <CancelButton
        title="Hủy"
        @click="emit('onCancel')"
      />
      <OkButton
        text="Lưu thông tin"
        @click="onOk"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
import { defineEmits, defineProps, ref } from 'vue';
import { useCommonStore } from '~/store/common.store';
import type { ResumeForeignLanguage } from '~/types/resume.interface';
import CancelButton from '../../common/CancelButton.vue';
import OkButton from '../../common/OkButton.vue';
import FormItem from '../../form/formItem.vue';
import LanguageLevelSelect from './LanguageLevelSelect.vue';
import LanguageSelect from './LanguageSelect.vue';
import Empty from '~/components/common/Empty.vue';

const { languages } = storeToRefs(useCommonStore());
export interface IModel {
  languageId: number | null;
  levelId: number | null;
}
const emit = defineEmits(['onCancel', 'onOk']);

const levelId = ref<number | null>(null);
const languageId = ref<number | null>(null);

const rules = ref({
  levelId: '',
  language: '',
});

const props = defineProps({
  defaultValue: {
    type: Object as () => ResumeForeignLanguage | null,
    default: () => null,
  },
});

const validate = () => {
  let valid = true;
  if (!languageId.value) {
    rules.value['language'] = 'Vui lòng chọn ngoại ngữ';
    valid = false;
  } else {
    rules.value['language'] = '';
  }

  if (!levelId.value) {
    rules.value['levelId'] = 'Vui lòng chọn trình độ';
    valid = false;
  } else {
    rules.value['levelId'] = '';
  }

  return valid;
};

const setDefaultValue = () => {
  languageId.value = languages.value[0].id;
  levelId.value = 1;
};

const onOk = () => {
  const valid = validate();
  if (!valid) return;
  emit('onOk', {
    levelId: levelId.value,
    languageId: languageId.value,
  } as IModel);
};

onMounted(() => {
  if (props.defaultValue) {
    languageId.value = languages.value.find((item) => item.id === props.defaultValue?.languageId)?.id || null;
    levelId.value = props.defaultValue.levelId;
  }
});
</script>
