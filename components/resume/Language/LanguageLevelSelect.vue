<template>
  <DropdownSearchSelect
    v-model:model-value="model"
    :list="languageLevels"
    id-key="id"
    value-key="name"
    :show-search="true"
    placeholder="<PERSON><PERSON><PERSON> mức độ thành thạo"
  />
</template>
<script setup lang="ts">
import DropdownSearchSelect from '~/components/common/DropdownSearchSelect.vue';
import { languageLevels } from '~/types/resume.interface';

// interface ILanguageLevel {
//   id: number;
//   name: string;
// }

const model = defineModel<number | null | undefined>({
  type: Number as () => number | null,
});

// const open = ref(false);

// const onSelect = (item: ILanguageLevel) => {
//   model.value = item?.id;
//   open.value = false;
//   console.log('model ==> ', model.value);
// };
</script>
