<template>
  <DropdownSearchSelect
    v-model:model-value="model"
    :list="languages"
    :show-search="true"
    value-key="name"
    id-key="id"
    placeholder="Chọn ngôn ngữ"
  />
</template>
<script setup lang="ts">
import { defineEmits } from 'vue';
import DropdownSearchSelect from '~/components/common/DropdownSearchSelect.vue';
import { useCommonStore } from '~/store/common.store';

const { languages } = storeToRefs(useCommonStore());
const model = defineModel<number | null | string>({
  type: Number as PropType<number | null>,
  default: null,
});
</script>
