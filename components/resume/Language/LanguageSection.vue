<template>
  <Collapse
    v-if="resume"
    title="Ngoại ngữ"
    adding-text="Thêm ngôn ngữ"
    :expanded="true"
    action-text="Thêm"
    :show-adding="!addingFormOption"
    :loading="useResume.updateLanguagePending || useResume.createLanguagePending"
    @on-adding="onFormOpen"
  >
    <template v-if="resume?.languages?.length">
      <LanguageItem
        v-for="item in resume.languages"
        :key="item.id"
        :language="item"
        @on-edit="onEdit(item)"
        @on-delete="onDelete(item.id)"
      />
    </template>
    <Empty
      v-else-if="!addingFormOption"
      description="Thông tin ngoại ngữ rỗng"
    />
    <LanguageForm
      v-if="addingFormOption"
      :default-value="editRef"
      @on-ok="onOk"
      @on-cancel="onCancel"
    />
  </Collapse>
</template>
<script setup lang="ts">
import { useResumeStore } from '~/store/resume';
import type { ResumeForeignLanguage } from '~/types/resume.interface';
import Collapse from '../../common/collapse.vue';
import type { IModel } from './LanguageForm.vue';
import LanguageForm from './LanguageForm.vue';
import LanguageItem from './LanguageItem.vue';
import Empty from '~/components/common/Empty.vue';
const { resume } = storeToRefs(useResumeStore());
// const { removeLanguage, createLanguage, updateLanguage } = useResumeStore();
const useResume = useResumeStore();

const editRef = ref<ResumeForeignLanguage | null>(null);
const addingFormOption = ref<boolean>(false);
const expanded = ref<boolean>(true);

const onEdit = (ref: ResumeForeignLanguage) => {
  editRef.value = ref;
  addingFormOption.value = true;
};
const onDelete = (id: number) => {
  if (!id) return;
  if (!resume.value) return;
  useResume.removeLanguage(resume.value.id, id);
};
const onCancel = () => {
  addingFormOption.value = false;
  editRef.value = null;
};
const onFormOpen = () => {
  addingFormOption.value = true;
  expanded.value = true;
};

const onOk = (value: IModel) => {
  const { languageId, levelId } = value;
  if (!languageId || !levelId || !resume.value) return;
  if (editRef.value) {
    useResume.updateLanguage(resume.value?.id, editRef.value.id, {
      languageId,
      levelId,
    });
  } else {
    useResume.createLanguage(resume.value.id, {
      languageId,
      levelId,
    });
  }
  onCancel();
};
</script>
