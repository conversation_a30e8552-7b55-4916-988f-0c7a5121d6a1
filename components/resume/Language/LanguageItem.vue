<template>
  <div
    v-if="language"
    class="flex justify-between items-center mb-8"
  >
    <div class="flex space-x-2">
      <div class="text-gray-500">
        {{ languages.find((lang) => lang.id == language?.languageId)?.name }}
      </div>
      <Dot />
      <div class="text-gray-500">
        {{ languageLevels.find((item) => item.id === language?.levelId)?.name }}
      </div>
    </div>
    <ResumeButtonAccountGroup
      @on-delete="emit('onDelete')"
      @on-edit="emit('onEdit')"
    />
  </div>
</template>
<script setup lang="ts">
import { useCommonStore } from "~/store/common.store";
import type { ResumeForeignLanguage } from "~/types/resume.interface";
import { languageLevels } from "~/types/resume.interface";
import Dot from "../../common/Dot.vue";
import ResumeButtonAccountGroup from "../ResumeButtonAccountGroup.vue";

const { languages } = storeToRefs(useCommonStore());

const emit = defineEmits(["onDelete", "onEdit"]);

defineProps({
  language: {
    type: Object as () => ResumeForeignLanguage | null | undefined,
    required: true,
  },
});
</script>
