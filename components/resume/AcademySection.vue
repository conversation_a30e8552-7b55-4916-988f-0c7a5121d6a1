<template>
  <Collapse
    title="Thông tin học vấn"
    :expanded="true"
    adding-text="Thêm học vấn"
    :loading="useResume.updateAcademyPending || useResume.createAcademyPending"
    :required="true"
    :valid="tinyintToBoolean(useResume.resume?.educationCompleted)"
    @on-adding="openAddingForm"
  >
    <template v-if="useResume.resume?.academies?.length">
      <ExperienceItem
        v-for="item in useResume.resume?.academies"
        :key="`experience-item-${item.id}`"
        :title="item.degree"
        :sub-title="item.schoolName"
        :from="item.startDate"
        :to="item.endDate"
        :extra="item.specialize"
        @on-edit="onEdit(item)"
        @on-delete="onDelete(item)"
      />
    </template>
    <Empty v-else-if="!addingFormOption" />
    <Divider />
    <DegreeForm
      v-if="addingFormOption"
      :default-value="editRef"
      @submit="onUpdateOrCreate"
      @cancel="onCancel"
    />
  </Collapse>
</template>
<script setup lang="ts">
import { useResumeStore } from '~/store/resume';
import type { Resume, ResumeAcademy } from '~/types/resume.interface';
import Collapse from '../common/collapse.vue';
import ExperienceItem from '../resume/Experience/experienceItem.vue';
import DegreeForm, { type DegreeFormModel } from './DegreeForm.vue';
import Empty from '../common/Empty.vue';

const useResume = useResumeStore();

const editRef = ref<ResumeAcademy | null>(null);
const addingFormOption = ref<boolean>(false);

const openAddingForm = () => {
  addingFormOption.value = true;
};

const onUpdateOrCreate = (value: DegreeFormModel) => {
  if (editRef.value) {
    onUpdate(value);
  } else {
    onCreate(value);
  }
};

const onCreate = (value: DegreeFormModel) => {
  if (!value || !useResume.resume) return;

  useResume.createAcademy(useResume.resume.id, {
    schoolName: value.schoolName,
    specialize: value.specialize,
    degree: value.degree,
    startDate: value.startDate,
    endDate: value.endDate,
  });
};

const onEdit = (item: ResumeAcademy) => {
  editRef.value = item;
  addingFormOption.value = true;
};

const onDelete = (item: ResumeAcademy) => {
  if (!useResume.resume) return;
  useResume.removeAcademy(useResume.resume?.id, item.id);
};

const onUpdate = (value: DegreeFormModel) => {
  if (!value) return;
  const resumeAcademyId = editRef.value?.id;
  if (!resumeAcademyId) return;

  if (!useResume.resume) return;

  useResume.updateAcademy(useResume.resume.id, resumeAcademyId, {
    schoolName: value.schoolName,
    degree: value.degree,
    startDate: value.startDate,
    endDate: value.endDate,
    specialize: value.specialize,
  });
};

const onCancel = () => {
  addingFormOption.value = false;
  editRef.value = null;
};

// watch(
//   () => useResumeStore().resume,
//   (value: Resume | null) => {
//     if (!value?.academies?.length) {
//       openAddingForm();
//     }
//   },
// );
watch(
  () => useResumeStore().createAcademySuccess,
  (value: boolean) => {
    if (value) {
      onCancel();
    }
  },
);
watch(
  () => useResumeStore().updateAcademySuccess,
  (value: boolean) => {
    if (value) {
      onCancel();
    }
  },
);
watch(
  () => useResumeStore().deleteAcademySuccess,
  (value: boolean) => {
    if (value) {
      onCancel();
    }
  },
);
</script>
