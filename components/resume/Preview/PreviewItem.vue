<template>
  <div class="my-2">
    <div class="flex justify-between items-center">
      <PreviewLabel>{{ label }}</PreviewLabel>
      <div
        v-if="extraLabel"
        class="text-gray-500 text-sm"
      >
        {{ extraLabel }}
      </div>
    </div>
    <div
      v-if="subLabel"
      class="text-primary font-light text-sm my-2"
    >
      {{ subLabel }}
    </div>
    <div class="font-light text-sm text-gray-500">{{ content }}</div>
  </div>
</template>
<script setup lang="ts">
import PreviewLabel from "./PreviewLabel.vue";
defineProps({
  label: {
    type: String,
    default: ''
  },
  extraLabel: {
    type: String,
    default: ''
  },
  subLabel: {
    type: String,
    default: ''
  },
  content: {
    type: String,
    default: ''
  },
});
</script>
