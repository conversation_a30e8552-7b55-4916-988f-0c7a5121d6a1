<template>
  <div
    v-if="resume"
    class="py-6"
  >
    <PreviewStepTitle>Ng<PERSON><PERSON><PERSON> ng<PERSON></PreviewStepTitle>
    <PreviewCard>
      <template v-if="resume.languages?.length">
        <PreviewItem
          v-for="language in resume.languages"
          :key="language.id"
          :label="findByLanguageId(language.languageId)?.name"
          :sub-label="findByLanguageLevelId(language.levelId)?.name"
        />
      </template>
      <Empty
        v-else
        description="Chưa có thông tin"
        size="sm"
      />
    </PreviewCard>
  </div>
</template>
<script setup lang="ts">
import moment from 'moment';
import PreviewCard from './PreviewCard.vue';
import PreviewItem from './PreviewItem.vue';
import PreviewStepTitle from './PreviewStepTitle.vue';
import Empty from '~/components/common/Empty.vue';

import { useCommonStore } from '~/store/common.store';
import { useResumeStore } from '~/store/resume';

const { findByLanguageId, findByLanguageLevelId } = useCommonStore();

const { resume } = storeToRefs(useResumeStore());
</script>
