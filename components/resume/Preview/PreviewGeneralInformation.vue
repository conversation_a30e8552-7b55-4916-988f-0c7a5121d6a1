<template>
  <div
    v-if="resume"
    class="py-6"
  >
    <PreviewStepTitle>
      {{ resume.type === ResumeType.offline ? '<PERSON><PERSON> sơ đính kèm' : '<PERSON><PERSON> sơ ứng tuyển' }}
    </PreviewStepTitle>
    <PreviewCard>
      <div
        v-if="resume"
        class="grid grid-cols-3 gap-4"
      >
        <PreviewItem
          label="Vị trí mong muốn"
          :content="resume.expectPosition"
        />
        <PreviewItem
          label="Cấp bậc hiện tại"
          :content="findJobLevelById(resume.currentLevelId)?.name"
        />
        <PreviewItem
          label="Cấp bậc mong muốn"
          :content="findJobLevelById(resume.expectLevelId)?.name"
        />
        <PreviewItem
          label="Mức lương mong muốn"
          :content="findSalaryById(resume.expectSalary)?.name"
        />
        <PreviewItem
          label="Trình độ học vấn"
          :content="findDegreeById(resume?.educationId)?.name"
        />
        <PreviewItem
          label="Số năm kinh nghiệm"
          :content="findExperienceById(resume.experienceId)?.name"
        />
        <PreviewItem
          label="Nghề nghiệp"
          :content="
            findOccupationByIds(resume.occupationIds)
              .map((item) => item.name)
              .join(', ')
          "
        />
        <PreviewItem
          label="Địa điểm làm việc"
          :content="
            findProvinceByIds(resume.provinceIds)
              ?.map((item) => item.name)
              ?.join(', ')
          "
        />
        <PreviewItem
          label="Hình thức làm việc"
          :content="findJobMethodById(resume.typeOfEmploymentId)?.name"
        />
        <PreviewItem
          v-if="resume.careerObjective"
          label="Mục tiêu nghề nghiệp"
          :content="resume.careerObjective"
        />
        <PreviewItem
          label="Kỹ năng mềm / kỹ năng cứng"
          :content="resume.softSkills?.join(', ')"
        />
      </div>
      <Empty
        v-else
        description="Chưa có thông tin"
        size="sm"
      />
    </PreviewCard>
  </div>
</template>
<script setup lang="ts">
import { ResumeType, type Resume } from '~/types';
import PreviewCard from './PreviewCard.vue';
import PreviewItem from './PreviewItem.vue';
import PreviewStepTitle from './PreviewStepTitle.vue';
import Empty from '~/components/common/Empty.vue';

import { useCommonStore } from '~/store/common.store';
const {
  findJobLevelById,
  findSalaryById,
  findDegreeById,
  findExperienceById,
  findOccupationByIds,
  findJobMethodById,
  findProvinceByIds,
} = useCommonStore();

defineProps({
  resume: Object as PropType<Resume>,
});

// const { resume } = storeToRefs(useResumeStore());
</script>
