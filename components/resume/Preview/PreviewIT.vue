<template>
  <div
    v-if="resume"
    class="py-6"
  >
    <PreviewStepTitle><PERSON> học</PreviewStepTitle>
    <PreviewCard>
      <template v-if="resume.techs?.length">
        <PreviewItem
          v-for="it in resume.techs"
          :key="it.id"
          :label="it.skills.join(', ')"
        />
      </template>
      <Empty
        v-else
        description="Chưa có thông tin"
        size="sm"
      />
    </PreviewCard>
  </div>
</template>
<script setup lang="ts">
import moment from 'moment';
import PreviewCard from './PreviewCard.vue';
import PreviewItem from './PreviewItem.vue';
import PreviewStepTitle from './PreviewStepTitle.vue';
import Empty from '~/components/common/Empty.vue';

import { useCommonStore } from '~/store/common.store';
import { useResumeStore } from '~/store/resume';

const { findByLanguageId, findByLanguageLevelId } = useCommonStore();

const { resume } = storeToRefs(useResumeStore());
</script>
