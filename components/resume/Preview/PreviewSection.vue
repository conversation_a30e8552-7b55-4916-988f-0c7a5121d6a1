<template>
  <div v-if="resume">
    <PreviewProfile
      v-if="profile"
      :profile="profile"
    />
    <PreviewAttachedCVFile
      v-if="resume.cvFileUrl"
      :file="resume.cvFileUrl"
    />
    <PreviewGeneralInformation :resume="resume" />
    <PreviewExperience v-if="resume.type === ResumeType.online" />
    <PreviewEducation v-if="resume.type === ResumeType.online" />
    <PreviewForeignLanguage v-if="resume.type === ResumeType.online" />
    <PreviewIT v-if="resume.type === ResumeType.online" />
    <PreviewReference v-if="resume.type === ResumeType.online" />
  </div>
</template>
<script setup lang="ts">
import PreviewAttachedCVFile from './PreviewAttachedCVFile.vue';
import PreviewEducation from './PreviewEducation.vue';
import PreviewExperience from './PreviewExperience.vue';
import PreviewForeignLanguage from './PreviewForeignLanguage.vue';
import PreviewGeneralInformation from './PreviewGeneralInformation.vue';
import PreviewIT from './PreviewIT.vue';
import PreviewProfile from './PreviewProfile.vue';
import PreviewReference from './PreviewReference.vue';

import { ResumeType, type Customer, type Resume } from '~/types';

defineProps({
  resume: {
    type: Object as () => Resume,
    required: true,
  },
  profile: {
    type: Object as () => Customer,
    required: true,
  },
});

// const {
//   resume
// } = storeToRefs(useResumeStore());
// const {
//   profile
// } = storeToRefs(useProfileStore());
</script>
