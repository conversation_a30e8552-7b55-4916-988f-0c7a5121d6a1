<template>
  <div
    v-if="profile"
    class="py-6"
  >
    <PreviewStepTitle>Thông tin ứng viên</PreviewStepTitle>
    <PreviewCard class="grid grid-cols-4 gap-4">
      <PreviewItem
        label="Số điện thoại"
        :content="profile.phoneNumber || 'NaN'"
      />
      <PreviewItem
        label="Email"
        :content="profile.phoneNumber || 'NaN'"
      />
      <PreviewItem
        label="Ngày sinh"
        :content="profile.birthday || 'NaN'"
      />
      <PreviewItem
        label="Giới tính"
        :content="profile.gender || 'NaN'"
      />
      <PreviewItem
        label="Tỉnh / Thành phố"
        :content="findProvinceById(profile.provinceId)?.name || 'NaN'"
      />
      <PreviewItem
        label="Địa chỉ"
        :content="profile.address || 'NaN'"
      />
      <PreviewItem
        label="Tình trạng hôn nhân"
        :content="parseMaritalStatus(profile.maritalStatus)"
      />
    </PreviewCard>
  </div>
</template>
<script setup lang="ts">
import type { Customer, TinyInt } from "~/types";
import PreviewCard from "./PreviewCard.vue";
import PreviewItem from "./PreviewItem.vue";
import PreviewStepTitle from "./PreviewStepTitle.vue";

import { useAuthStore } from "~/store/auth.store";
import { useCommonStore } from "~/store/common.store";
import { useResumeStore } from "~/store/resume";

// const { profile } = storeToRefs(useAuthStore());
const { findProvinceById } = useCommonStore();

defineProps({
  profile: Object as PropType<Customer>
})

const parseMaritalStatus = (maritalStatus: TinyInt) => {
  switch (maritalStatus) {
    case 0:
      return "Độc thân";
    case 1:
      return "Đã kết hôn";
    default:
      return "NaN";
  }
};
</script>
