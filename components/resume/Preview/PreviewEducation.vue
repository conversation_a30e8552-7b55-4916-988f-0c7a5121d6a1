<template>
  <div
    v-if="resume"
    class="py-6"
  >
    <PreviewStepTitle>Tr<PERSON><PERSON> độ học vấn</PreviewStepTitle>
    <PreviewCard>
      <template v-if="resume.academies?.length">
        <PreviewItem
          v-for="education in resume.academies"
          :key="education.id"
          :label="education.schoolName"
          :extra-label="
            experienceDateRangeFormat(education.startDate, education.endDate)
          "
          :sub-label="education.degree"
          :content="education.specialize"
        />
      </template>
      <Empty
        v-else
        description="Chưa có thông tin"
        size="sm"
      />
    </PreviewCard>
  </div>
</template>
<script setup lang="ts">
import moment from "moment";
import PreviewCard from "./PreviewCard.vue";
import PreviewItem from "./PreviewItem.vue";
import PreviewStepTitle from "./PreviewStepTitle.vue";
import Empty from "~/components/common/Empty.vue";

import { useResumeStore } from "~/store/resume";

const { resume } = storeToRefs(useResumeStore());

const experienceDateRangeFormat = (
  startDate: string,
  toDate?: string | null,
) => {
  const startDateMoment = moment(startDate, "YYYY-MM-DD");
  const toDateMoment = toDate ? moment(toDate, "YYYY-MM-DD") : null;
  let formatString = startDateMoment.format("MM/YYYY");
  if (toDateMoment) {
    formatString += " - " + toDateMoment.format("MM/YYYY");
  } else {
    formatString += " - nay";
  }
  return formatString;
};
</script>
