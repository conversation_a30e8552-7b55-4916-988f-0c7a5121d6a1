<template>
  <div class="py-6">
    <PreviewStepTitle><PERSON><PERSON> sơ đ<PERSON>h kèm</PreviewStepTitle>
    <PreviewCard>
      <div class="flex justify-start items-center border p-2 rounded-sm">
        <IconPdf />
        <div>
          {{ file?.split('/').pop() }}
        </div>
      </div>
    </PreviewCard>
  </div>
</template>
<script setup lang="ts">
import PreviewStepTitle from "./PreviewStepTitle.vue";
import PreviewCard from './PreviewCard.vue';
import IconPdf from '~/components/icons/IconPdf.vue';

defineProps({
  file: Object as PropType<string>
})
</script>