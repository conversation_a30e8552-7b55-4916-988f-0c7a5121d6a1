<template>
  <div
    v-if="resume"
    class="py-6"
  >
    <PreviewStepTitle>Thông tin người tham khảo</PreviewStepTitle>
    <PreviewCard>
      <template v-if="resume.refs?.length">
        <PreviewItem
          v-for="ref in resume.refs"
          :key="ref.id"
          :label="ref.fullName + ' - ' + ref.phone"
          :sub-label="ref.position"
          :content="ref.companyName"
        />
      </template>
      <Empty
        v-else
        description="Chưa có thông tin"
        size="sm"
      />
    </PreviewCard>
  </div>
</template>
<script setup lang="ts">
import PreviewCard from './PreviewCard.vue';
import PreviewItem from './PreviewItem.vue';
import PreviewStepTitle from './PreviewStepTitle.vue';
import Empty from '~/components/common/Empty.vue';

import { useCommonStore } from '~/store/common.store';
import { useResumeStore } from '~/store/resume';

const { resume } = storeToRefs(useResumeStore());
</script>
