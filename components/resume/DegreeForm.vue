<template>
  <div>
    <button class="btn btn-primary btn-outline w-full" @click="setDefaultValue">set default value</button>
    <FormItem
      label="Trường / trung tâm đào tạo"
      :error="errMessage['schoolName']"
    >
      <SchoolSelect v-model:model-value="form.schoolName" />
    </FormItem>
    <div class="grid lg:grid-cols-2 lg:gap-8">
      <FormItem
        label="Chuyên ngành đào tạo"
        :error="errMessage['specialize']"
      >
        <input
          v-model="form.specialize"
          type="text"
          class="input input-bordered input-secondary w-full"
          placeholder="Chuyên ngành đào tạo"
        />
      </FormItem>
      <FormItem
        label="Tên bằng cấp / chứng chỉ"
        :error="errMessage['degree']"
      >
        <input
          v-model="form.degree"
          type="text"
          class="input input-bordered input-secondary w-full"
          placeholder="Tên bằng cấp / chứng chỉ"
        />
      </FormItem>
    </div>
    <div class="grid lg:grid-cols-2 lg:gap-4">
      <FormItem
        label="Thời gian bắt đầu"
        :error="errMessage['startDate']"
      >
        <MonthYearPicker v-model="form.startDate" />
      </FormItem>
      <FormItem
        label="Thời gian kết thúc"
        :error="errMessage['endDate']"
      >
        <MonthYearPicker :model-value="form.endDate" />
      </FormItem>
    </div>
    <div class="flex justify-center md:justify-end space-x-2">
      <CancelButton @click="onCancel" />
      <OkButton
        :disable="true"
        text="Lưu thông tin"
        @click="validate"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
import moment from 'moment';
import { defineEmits, defineProps, onMounted, ref } from 'vue';

import CancelButton from '~/components/common/CancelButton.vue';
import OkButton from '~/components/common/OkButton.vue';
import type { ResumeAcademy } from '~/types/resume.interface';
import MonthYearPicker from '../common/MonthYearPicker.vue';
import FormItem from '../form/formItem.vue';
import SchoolSelect from './SchoolSelect.vue';

const emit = defineEmits(['submit', 'cancel']);

interface IForm {
  schoolName: string | null;
  specialize: string | null; // Chuyên ngành đào tạo
  startDate: {
    month: number | null;
    year: number | null;
  };
  endDate: {
    month: number | null;
    year: number | null;
  };
  degree: string | null;
}

export interface DegreeFormModel {
  schoolName: string;
  specialize: string;
  startDate: string;
  endDate: string;
  degree: string;
}

const form = ref<IForm>({
  schoolName: '',
  specialize: null,
  startDate: {
    month: null,
    year: null,
  },
  endDate: {
    month: null,
    year: null,
  },
  degree: null,
});

const props = defineProps({
  defaultValue: {
    type: Object as () => ResumeAcademy | null | undefined,
    required: false,
    default: null,
  },
});

const defaultErrMsg = {
  schoolName: null,
  startDate: null,
  endDate: null,
  degree: null,
};

const errMessage = ref<Record<string, string | null>>(defaultErrMsg);

const onCancel = () => {
  emit('cancel');
};

const setDefaultValue = () => {
  form.value = {
    schoolName: 'Trường đại học Bách Khoa Hà Nội',
    specialize: 'Chuyên ngành công nghệ thông tin',
    startDate: {
      month: 1,
      year: 2014,
    },
    endDate: {
      month: 12,
      year: 2018,
    },
    degree: 'Tên bằng cấp / chứng chỉ',
  };
};

const validate = () => {
  let isValid = true;
  errMessage.value = {};
  if (!form.value.schoolName) {
    errMessage.value.schoolName = 'Trường / trung tâm đào tạo không được để trống';
    isValid = false;
  }

  if (!form.value.startDate.month || !form.value.startDate.year) {
    errMessage.value.startDate = 'Thời gian bắt đầu không được để trống';
    isValid = false;
  }

  if (!form.value.endDate.month || !form.value.endDate.year) {
    errMessage.value.endDate = 'Thời gian kết thúc không được để trống';
    isValid = false;
  } else if (
    form.value.startDate.year &&
    form.value.startDate.month &&
    form.value.endDate.year &&
    form.value.endDate.month &&
    (form.value.startDate.year > form.value.endDate.year ||
      (form.value.startDate.year === form.value.endDate.year && form.value.startDate.month > form.value.endDate.month))
  ) {
    errMessage.value.endDate = 'Thời gian kết thúc không được nhỏ hơn thời gian bắt đầu';
    isValid = false;
  }

  if (form.value.degree === null) {
    errMessage.value.degree = 'Tên bằng cấp / chứng chỉ không được để trống';
    isValid = false;
  } else if (form.value.degree.length < 5) {
    errMessage.value.degree = 'Tên bằng cấp / chứng chỉ phải có ít nhất 5 ký tự';
    isValid = false;
  }

  if (form.value.specialize === null) {
    errMessage.value.specialize = 'Chuyên ngành đào tạo không được để trống';
    isValid = false;
  } else if (form.value.specialize.length < 5) {
    errMessage.value.specialize = 'Chuyên ngành đào tạo phải có ít nhất 5 ký tự';
    isValid = false;
  }

  console.log(`Form is valid: ${isValid}`);
  console.log(`Form data: ${JSON.stringify(form.value)}`);
  console.log(`Error message: ${JSON.stringify(errMessage.value)}`);
  if (isValid) {
    const startDate = moment()
      .set('year', form.value.startDate.year!)
      .set('month', form.value.startDate.month! - 1)
      .startOf('month')
      .format('YYYY-MM-DD');
    const endDate = moment()
      .set('year', form.value.endDate.year!)
      .set('month', form.value.endDate.month! - 1)
      .startOf('month')
      .format('YYYY-MM-DD');
    const value: DegreeFormModel = {
      schoolName: form.value.schoolName ?? '',
      specialize: form.value.specialize ?? '',
      startDate,
      endDate,
      degree: form.value.degree!,
    };
    emit('submit', value);
  }
};

onMounted(() => {
  if (props.defaultValue) {
    const startDateMoment = moment(props.defaultValue.startDate, 'YYYY-MM-DD');
    const endDateMoment = moment(props.defaultValue.endDate, 'YYYY-MM-DD');
    form.value = {
      schoolName: props.defaultValue.schoolName,
      specialize: props.defaultValue.specialize,
      startDate: {
        month: startDateMoment.month() + 1,
        year: startDateMoment.year(),
      },
      endDate: {
        month: endDateMoment.month() + 1,
        year: endDateMoment.year(),
      },
      degree: props.defaultValue.degree,
    };
  }
});
</script>
