<template>
  <div class="grid grid-cols-3 gap-2 lg:grid-cols-9">
    <NuxtLink
      v-for="stats in mapOccupationStats"
      :key="stats.occupationId"
      :to="{
        name: 'tim-kiem-viec-lam',
        query: { occupationIds: [stats.occupationId] },
      }"
      target="_blank"
      class="text-center bg-transparent border border-transparent hover:border-secondary hover:border hover:shadow-lg rounded-md py-2 px-3"
    >
      <div class="flex justify-center mb-2">
        <img
          :src="
            '/images/occupation-icons/' +
            stats.occupationId +
            '.png'
          "
          class="w-8 h-8"
        />
      </div>
      <div>
        <span class="text-info text-base font-semibold my-2">{{
          formatNumber(stats.count)
        }}</span>
        {{ " " }}
        <span class="text-xs text-gray-500">Việc</span>
      </div>
      <div class="text-xs">
        {{ stats.name }}
      </div>
    </NuxtLink>
    <a
      href="javascript:void(0)"
      class="text-center bg-white border border-transparent hover:border-secondary hover:border hover:shadow-lg rounded-md py-2 px-6"
      onclick="occupation_modal.showModal()"
    >
      <div
        class="flex justify-center"
        @click="onOpenOccupationModal"
      >
        <img
          :src="'/images/all_occ.png'"
          class="w-8 h-8"
        />
      </div>
      <div
        class="text-info text-xs font-semibold my-2 text-center block w-full"
      >
        <div class="max-w-20 mx-auto">Tất cả các ngành</div>
      </div>
    </a>
  </div>
</template>
<script setup lang="ts">
import { defineProps } from "vue";
import type { Occupation, OccupationStats } from "~/types";
import { formatNumber } from "~/utils/common";

const props = defineProps({
  occupations: Array<Occupation>,
  occupationsStats: Array<OccupationStats>,
});

const mapOccupationStats = props.occupationsStats?.map((item) => {
  return {
    ...item,
    name: props.occupations?.find(
      (occupation) => occupation.id === item.occupationId,
    )?.name,
  };
});

const onOpenOccupationModal = () => {
  console.log("Open occupation modal");
};
const onCloseOccupationModal = () => {
  console.log("Close occupation modal");
};
</script>
