<template>
  <div class="bg-white">
    <div class="container">
      <div
        class="card px-4 py-6 w-auto backdrop-blur-xl shadow-lg top-[-50px] bg-gradient-to-t from-white to-transparent"
      >
        <div v-if="isMobile">
          <div class="join w-full">
            <div class="w-full">
              <input
                class="input shadow-lg join-item w-full border-white !rounded-r-none focus:outline-0 focus-within:outline-0"
                placeholder="Tìm kiếm công việc"
              />
            </div>
            <div class="indicator">
              <button class="btn join-item !bg-secondary text-white animate-none">
                <IconSearch />
                <span>Tìm</span>
              </button>
            </div>
          </div>
        </div>
        <SearchGroupInput
          v-else
          :occupations="props.occupations || []"
          :provinces="provinces"
        />
        <!-- <br />
        <JobStats
          :occupations="occupations"
          :occupations-stats="occupationsStats"
        /> -->

        <div class="flex flex-wrap gap-2 mt-2">
          <NuxtLink
            class="badge badge-soft badge-warning font-light"
            :to="{
              path: 'tim-kiem-viec-lam',
            }"
            target="_blank"
          >
            <IconTrendingUp class="w-4 h-4" />
            Tất cả
          </NuxtLink>
          <NuxtLink
            v-for="stats in occupationsStats"
            :key="stats.occupationId"
            class="badge badge-soft badge-info font-light"
            :to="{
              name: 'tim-kiem-viec-lam',
              query: { occupationIds: [stats.occupationId] },
            }"
          >
            <IconTrendingUp class="w-4 h-4" />
            {{ findByOccupationId(stats.occupationId)?.name }}
          </NuxtLink>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import SearchGroupInput from '~/components/home/<USER>';
import type { Occupation, OccupationStats, Province } from '~/types';
import IconSearch from '../icons/IconSearch.vue';
import IconTrendingUp from '../icons/IconTrendingUp.vue';
import { useCommonStore } from '~/store/common.store';

const { findByOccupationId } = useCommonStore();

interface Props {
  occupations?: Array<Occupation>;
  occupationsStats?: Array<OccupationStats>;
  provinces?: Array<Province>;
}

const props = withDefaults(defineProps<Props>(), {
  occupations: () => [],
  occupationsStats: () => [],
  provinces: () => [],
});

const { isMobile } = useDevice();
</script>
