<template>
  <div class="bg-white py-16 px-10">
    <Container>
      <div class="w-full grid grid-cols-12 gap-8">
        <div class="col-span-12 lg:col-span-4">
          <h1 class="text-lg mb-5">Vi<PERSON><PERSON> làm theo ngành nghề</h1>
          <div>
            <NuxtLink
              v-for="occupation in topFiveOccupations"
              :key="occupation.id"
              :to="{
                name: 'tim-kiem-viec-lam',
                query: { occupationIds: [occupation.id] },
              }"
              target="_blank"
              class="block text-sm my-1 font-light"
            >
              {{ occupation.name }}
            </NuxtLink>
            <NuxtLink
              :to="{
                path: 'viec-lam/viec-lam-theo-nganh-nghe',
              }"
              target="_blank"
              class="block text-sm font-semibold text-info mt-4"
            >
              Xem tất cả
              <span class="font-light">></span>
            </NuxtLink>
          </div>
        </div>
        <div class="col-span-12 lg:col-span-4">
          <h1 class="text-lg mb-5">Vi<PERSON><PERSON> làm theo khu vực</h1>
          <div>
            <NuxtLink
              v-for="province in topFiveProvinces"
              :key="province.id"
              :to="{
                name: 'tim-kiem-viec-lam',
                query: { provinceIds: [province.id] },
              }"
              target="_blank"
              class="block text-sm my-1 font-light"
            >
              {{ province.name }}
            </NuxtLink>
            <NuxtLink
              :to="{
                path: 'viec-lam/viec-lam-theo-tinh-thanh',
              }"
              target="_blank"
              class="block text-sm font-semibold text-info mt-4"
            >
              Xem tất cả
              <span class="font-light">></span>
            </NuxtLink>
          </div>
        </div>
        <div class="col-span-12 lg:col-span-4">
          <h1 class="text-lg mb-5">Việc làm mới</h1>
          <div>
            <RecruitmentLink
              v-for="job in topFiveJobs"
              :key="job.id"
              :id="job.id"
              :slug="job.slug"
            >
              <div class="block text-sm my-1 font-light">
                {{ job.title }}
              </div>
            </RecruitmentLink>
          </div>
        </div>
      </div>
    </Container>
  </div>
</template>
<script lang="ts" setup>
import Container from '~/components/layouts/container.vue';
import { useCommonStore } from '~/store/common.store';
import { useRecruitmentStore } from '~/store/recruitment.store';
import RecruitmentLink from '~/components/link/recruitmentLink.vue';

const { occupations, provinces } = storeToRefs(useCommonStore());
const { newestList } = storeToRefs(useRecruitmentStore());

const topFiveOccupations = computed(() => {
  return occupations.value?.map((occupation) => occupation).splice(0, 5) || [];
});
const topFiveProvinces = computed(() => {
  return provinces.value?.map((province) => province).splice(0, 5) || [];
});
const topFiveJobs = computed(() => {
  return newestList.value?.map((job) => job).splice(0, 5) || [];
});
</script>
