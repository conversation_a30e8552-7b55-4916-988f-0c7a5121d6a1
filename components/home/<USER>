<template>
  <div
    class="container pt-2 pb-8"
    v-if="props?.jobs?.length"
  >
    <Title :title="title">
      <template #icon>
        <IconsIconHot class="w-10 h-10" />
      </template>
    </Title>
    <div class="grid grid-cols-1 gap-2 lg:grid-cols-3">
      <JobItem
        v-for="job in props.jobs"
        :key="job.id"
        :job="job"
        :provinces="provinces"
        :districts="districts"
        :is-favorite="isFavorite(job.id)"
        @favorite="onFavorite"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
import JobItem from '~/components/common/jobItem.vue';
import Title from '~/components/common/title.vue';
import { useAuthStore } from '~/store/auth.store';
import { recruitmentFavoriteStore } from '~/store/recruitmentFavorite';
import type { Province, Recruitment } from '~/types';

const { authenticated } = storeToRefs(useAuthStore());
const { openAuthPopup } = useAuthStore();
const { isFavorite, addFavoriteRecruitment, removeFavoriteRecruitment } = recruitmentFavoriteStore();

const props = defineProps({
  jobs: Array<Recruitment>,
  provinces: Array<Province>,
  districts: Array<Province>,
  title: {
    type: String,
    default: '',
  },
});

const onFavorite = (jobId: number, favorite: boolean) => {
  if (!authenticated.value) {
    openAuthPopup(true);
    return;
  }
  if (favorite) {
    addFavoriteRecruitment(jobId);
  } else {
    removeFavoriteRecruitment(jobId);
  }
};
</script>
