<template>
  <div class="bg-white pt-4 pb-10 mt-10">
    <Container>
      <Title
        title="Ngành nghề nổi bật"
        text-class="text-gray-700 text-2xl"
      >
        <template #icon>
          <img
            :src="'/images/star.png'"
            class="w-10 h-10"
          />
        </template>
      </Title>
      <br />
      <div>
        <!-- <div class="carousel carousel-center rounded-box">
          <div
            class="carousel-item w-1/6"
            v-for="stats in mapOccupationStats"
            :key="stats.occupationId"
          >
            <NuxtLink
              target="_blank"
              class="block w-full m-2 p-4 rounded-md border border-base-200 bg-base-200"
              :to="{
                name: 'tim-kiem-viec-lam',
                query: { occupationIds: [stats.occupationId] },
              }"
            >
              <div class="text-center w-full">
                <div class="h-20 w-20 mb-4 mx-auto">
                  <img
                    :src="'/images/occupation-icons/' + stats.occupationId + '.png'"
                    class="w-full h-auto"
                    :alt="stats.occupationId.toString()"
                  />
                </div>
                <div class="text-sm my-1 line-clamp-1">
                  {{ stats.name }}
                </div>
                <div class="text-sm text-primary">
                  {{ formatNumber(stats.count || 0) }}
                  việc làm
                </div>
              </div>
            </NuxtLink>
          </div>
        </div> -->

        <div class="grid grid-cols-2 gap-6 md:grid-cols-4">
          <NuxtLink
            v-for="stats in mapOccupationStats"
            :key="stats.occupationId"
            target="_blank"
            class="block w-full py-4 px-2 rounded-lg border border-base-200 bg-base-200"
            :to="{
              name: 'tim-kiem-viec-lam',
              query: { occupationIds: [stats.occupationId] },
            }"
          >
            <div class="text-center w-full">
              <div class="h-16 w-16 mx-auto my-6">
                <img
                  :src="'/images/occupation-icons/' + stats.occupationId + '.png'"
                  class="w-full h-auto"
                  :alt="stats.occupationId.toString()"
                />
              </div>
              <div class="text-md font-semibold my-1 line-clamp-1">
                {{ stats.name }}
              </div>
              <div class="text-xs text-primary">
                {{ formatNumber(stats.count || 0) }}
                việc làm
              </div>
            </div>
          </NuxtLink>
        </div>
      </div>
    </Container>
  </div>
</template>
<script setup lang="ts">
import { defineProps } from 'vue';
import type { Occupation, OccupationStats } from '~/types';
import { formatNumber } from '~/utils/common';
import Title from '../common/title.vue';
import Container from '../layouts/container.vue';

const props = defineProps({
  occupations: Array<Occupation>,
  occupationsStats: Array<OccupationStats>,
});

const mapOccupationStats = props.occupationsStats?.map((item) => {
  return {
    ...item,
    name: props.occupations?.find((occupation) => occupation.id === item.occupationId)?.name,
  };
});
</script>
