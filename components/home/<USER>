<template>
  <div class="join w-full border-none rounded-md shadow-lg py-3 px-3 bg-white">
    <div class="w-[37%]">
      <div>
        <label
          class="input input-lg border-white input-ghost join-item flex items-center gap-2 !focus:ring-white focus:!border-white focus-within:!border-white"
        >
          <IconSearch class="w-6" />
          <input
            type="text"
            class="grow"
            placeholder="Nhập vị trí muốn ứng tuyển"
            @change="onSearchKeywordChange"
          />
        </label>
      </div>
    </div>
    <div class="w-[2px] bg-base-300 mx-1" />
    <OccupationMultipleSelect
      v-model:model-value="occupationIds"
      class="!max-w-[30%] w-full"
      :border="false"
      size="lg"
    />
    <div class="w-[2px] bg-base-300 mx-1" />
    <DropdownSearchSelect
      v-model="provinceId"
      class="!w-[30%]"
      :bordered="false"
      :list="provinces"
      :show-search="true"
      size="lg"
      placeholder="Lọc theo tỉnh thành"
    />
    <div class="indicator mx-2">
      <button
        class="btn btn-primary text-white w-52 rounded-md"
        @click="onButtonClick"
      >
        <IconSearch class="w-6" />
        <span>Tìm việc</span>
      </button>
    </div>
  </div>
</template>
<script setup lang="ts">
import { defineProps } from 'vue';
import { useRouter } from 'vue-router';

import IconSearch from '../icons/IconSearch.vue';
import { JOB_BROWSER_PAGE_PATH } from '~/constants';
import type { Occupation, Province } from '~/types';
import DropdownSearchSelect from '../common/DropdownSearchSelect.vue';
import OccupationMultipleSelect from '../resume/GeneralInformation/OccupationMultipleSelect.vue';
const props = defineProps({
  occupations: {
    type: Array as PropType<Occupation[]>,
    default: () => []
  },
  provinces: {
    type: Array as PropType<Province[]>,
    default: () => []
  },
});

const provinceId = ref<number | null>(null);
const occupationIds = ref<number[]>([]);
const searchKeyword = ref<string>('');

const router = useRouter();

const onButtonClick = () => {
  router.push({
    path: JOB_BROWSER_PAGE_PATH,
    query: {
      occupationIds: occupationIds.value || undefined,
      provinceId: provinceId.value || undefined,
      keyword: searchKeyword.value || undefined,
    },
  });
};

const onSearchKeywordChange = (event: Event) => {
  searchKeyword.value = (event.target as HTMLInputElement).value;
};

const handleSearch = () => {
  router.push({
    path: JOB_BROWSER_PAGE_PATH,
  });
};
</script>
