<template>
  <dialog
    id="occupation_modal"
    class="modal"
  >
    <div class="modal-box w-11/12 max-w-5xl h-[700px] bg-white">
      <div class="modal-action justify-start m-0">
        <form method="dialog">
          <button class="btn-circle btn-ghost">
            <IconTimerSand class="w-6 text-gray-500" />
          </button>
        </form>
      </div>
      <div class="modal-header">
        <h3 class="text-xl font-semibold my-4">Tất cả các nghề nghiệp</h3>
      </div>
      <div>
        <label class="input input-md input-bordered flex items-center gap-2 mb-3">
          <input
            type="text"
            class="grow"
            placeholder="Tìm kiếm"
            @input="onSearch"
          />
          <IconClearRound class="w-6" />
        </label>
        <div
          v-for="occ in occupationMatchedList.length ? occupationMatchedList : occupations"
          :key="occ.id"
          class="font-light text-xs py-2"
        >
          {{ occ.name }}
        </div>
      </div>
    </div>
  </dialog>
</template>
<script setup lang="ts">
import { useCommonStore } from '~/store/common.store';
import type { Occupation } from '~/types';
import IconClearRound from '../icons/IconClearRound.vue';
import IconTimerSand from '../icons/IconTimerSand.vue';
import { removeUnicode } from '~/utils/common';

const { occupations } = storeToRefs(useCommonStore());

const occupationMatchedList = ref<Occupation[]>([]);

const onSearch = (e: Event) => {
  const el = e.target as HTMLInputElement;

  if (!el.value) {
    occupationMatchedList.value = [];
    return;
  }

  occupationMatchedList.value = occupations.value.filter((occ) => {
    const removedUnicodeKeyword = removeUnicode(el.value.toLowerCase());
    return removeUnicode(occ.name.toLowerCase()).includes(removedUnicodeKeyword);
  });
};
</script>
