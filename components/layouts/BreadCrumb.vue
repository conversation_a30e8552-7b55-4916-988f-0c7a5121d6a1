<template>
  <div class="text-sm my-2 font-normal breadcrumbs">
    <template v-if="props.routes.length">
      <ul>
        <li
          v-for="(route, idx) in props.routes"
          :key="route.path"
        >
          <NuxtLink
            :class="[
              {
                'text-gray-400': idx === props.routes.length - 1,
              },
            ]"
            :to="route.path"
            >{{ route.breadcrumbName }}</NuxtLink
          >
        </li>
      </ul>
    </template>
  </div>
</template>
<script lang="ts" setup>
export interface Route {
  path: string;
  breadcrumbName: string;
}
const props = defineProps({
  routes: {
    type: Array as PropType<Array<Route>>,
    default: () => [],
  },
});
</script>
