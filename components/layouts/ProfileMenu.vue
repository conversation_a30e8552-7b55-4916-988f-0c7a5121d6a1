<template>
  <ul class="menu bg-white w-full">
    <template
      v-for="menu in menuList"
      :key="`profile-menu-key-${menu.key}`"
    >
      <li class="menu-title">
        {{ menu.title }}
      </li>
      <li
        v-for="item in menu.items || []"
        :key="`profile-menu-key-${item.key}`"
      >
        <NuxtLink
          :class="[
            'text-nowrap',
            {
              'menu-active': menuActivate(item.key),
            },
          ]"
          :to="item.path"
          @click="onMenuItemClick(item)"
        >
          <component
            :is="item.iconComponent"
            v-if="item.iconComponent"
            :class="[
              'w-6',
              {
                'text-info': !menuActivate(item.key),
              },
            ]"
          />
          <span>{{ item.title }}</span>
        </NuxtLink>
      </li>
    </template>
  </ul>
</template>
<script setup lang="ts">
import IconClipboardDocument from '~/components/icons/IconClipboardDocument.vue';
import IconDocumentTextClock24Regular from '~/components/icons/IconDocumentTextClock24Regular.vue';
import IconUserProfile from '~/components/icons/IconUserProfile.vue';
import IconHeartOutline from '../icons/IconHeartOutline.vue';
const emits = defineEmits(['onMenuItemClick']);
const menuList = [
  {
    key: 'account',
    title: 'Quản lý tài khoản',
    items: [
      {
        key: 'profile',
        path: '/tai-khoan/profile',
        title: 'Thông tin cá nhân',
        iconComponent: IconUserProfile,
      },
    ],
  },
  {
    key: 'resume-manager',
    title: 'Quản lý hồ sơ',
    items: [
      {
        key: 'my-resume',
        path: '/ho-so-cua-ban',
        title: 'Hồ sơ của bạn',
        iconComponent: IconClipboardDocument,
      },
    ],
  },
  {
    key: 'job-manager',
    title: 'Quản lý việc làm',
    items: [
      {
        key: 'applied-jobs',
        path: '/viec-lam-da-ung-tuyen',
        title: 'Việc làm đã ứng tuyển',
        iconComponent: IconDocumentTextClock24Regular,
      },
      {
        key: 'saved-jobs',
        path: '/viec-lam-da-luu',
        title: 'Việc làm đã lưu',
        iconComponent: IconHeartOutline,
      },
    ],
  },
];

const activeMenus = ref<string[]>([]);

const onMenuItemClick = (item: { key: string }) => {
  activeMenus.value = [item.key];
  emits('onMenuItemClick', item);
};

const onInit = () => {
  const path = window.location.pathname;
  if (path === '/') {
    return;
  }
  path.split('/').forEach((path) => {
    menuList.forEach((menu) => {
      menu.items.forEach((item) => {
        if (item.path.includes(path)) {
          activeMenus.value = [item.key];
        }
      });
    });
  });
  // force re-render
  activeMenus.value = [...activeMenus.value];
};

const menuActivate = (key: string) => {
  return activeMenus.value.includes(key) ? 'bg-primary/15' : '';
};

onMounted(() => {
  onInit();
});
onBeforeMount(() => {
  onInit();
});
</script>
