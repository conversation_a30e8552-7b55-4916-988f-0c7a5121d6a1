<template>
  <div class="navbar bg-primary text-white py-0">
    <div class="navbar-start py-0 h-16">
      <NuxtLink
        to="/"
        class="btn btn-primary text-white text-xl border-r !h-16 py-1 !rounded-none w-full max-w-[272px] border-r-base-100/20"
      >
        <img
          alt="vietlamlamdong.site"
          :src="'/images/logo.png'"
          class="h-full"
        />
      </NuxtLink>
      <!-- <div class="h-full border-r border-base-100/20 ml-[52px]" /> -->
      <NuxtLink
        to="tim-kiem-viec-lam"
        class="h-16 items-center text-center flex text-sm font-semibold px-4 hover:bg-base-100/20"
      >
        Cơ hội việc làm
      </NuxtLink>
    </div>
    <div class="navbar-end mr-14 py-0 h-16">
      <template v-if="authenticated && profile">
        <button
          class="btn btn-ghost h-16 rounded-none border-transparent hover:bg-base-100/20 hover:text-white"
          popovertarget="popover-auth-header-dropdown"
          style="anchor-name: --popover-auth-header-dropdown"
        >
          <div class="avatar">
            <div class="w-10 rounded-full">
              <img
                alt="vietlamlamdong.site"
                :src="profile.avatar || 'https://ui-avatars.com/api/?name=' + profile.fullName"
              />
            </div>
          </div>
          <span class="text-sm font-semibold px-2">{{ profile.fullName }}</span>
          <IconArrowDown class="w-6 h-6" />
        </button>

        <ul
          class="dropdown menu w-52 rounded-box bg-base-100 shadow-sm !text-base-content border !border-secondary"
          popover
          id="popover-auth-header-dropdown"
          style="position-anchor: --popover-auth-header-dropdown"
        >
          <li>
            <ProfileLink class="py-2"> Tài khoản </ProfileLink>
          </li>
          <li>
            <div
              class="py-2 cursor-pointer"
              @click="logout"
            >
              Đăng xuất
            </div>
          </li>
        </ul>
      </template>
      <div v-else>
        <button
          class="btn btn-ghost h-16 rounded-none border-transparent hover:bg-base-100/20 hover:text-white"
          @click="openAuthPopup(true)"
        >
          Đăng ký/Đăng nhập
        </button>
      </div>

      <div class="h-full border-r border-base-100/20" />
      <NuxtLink
        to="/employer"
        class="h-16 items-center text-center flex justify-start text-sm font-semibold px-4 hover:bg-base-100/20"
      >
        <IconSuitcase class="text-info w-8 h-8" />
        <div class="text-left mx-2 uppercase">
          <div class="text-xs font-light">dành cho</div>
          <div class="text-sm">Nhà tuyển dụng</div>
        </div>
      </NuxtLink>
    </div>
    <!-- <LoginModal :show="authPopupOpen" /> -->
  </div>
</template>
<script setup lang="ts">
import { useAuthStore } from '~/store/auth.store';
import ProfileLink from '../link/profileLink.vue';
import IconArrowDown from '../icons/IconArrowDown.vue';
import IconSuitcase from '../icons/IconSuitcase.vue';

const { openAuthPopup } = useAuthStore();
const { authenticated, authPopupOpen, profile } = storeToRefs(useAuthStore());
// const { profile } = storeToRefs(useProfileStore());

const showLoginModal = ref(false);

function logout() {
  useAuthStore().logUserOut();
}
</script>
