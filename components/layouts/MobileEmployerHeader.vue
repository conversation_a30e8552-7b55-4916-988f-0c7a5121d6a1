<template>
  <div class="drawer z-10">
    <input
      id="my-drawer"
      type="checkbox"
      class="drawer-toggle"
    />
    <div class="drawer-content">
      <div class="bg-primary h-16 flex justify-between items-center flex-1">
        <label
          href="javascript:void(0)"
          for="my-drawer"
          class="h-16 w-16 text-white flex justify-center items-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            class="inline-block h-8 w-8 stroke-current"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 6h16M4 12h16M4 18h16"
            />
          </svg>
        </label>
        <div>
          <NuxtLink to="/">
            <img
              :src="'/images/logo.png'"
              class="h-16"
            />
          </NuxtLink>
        </div>
        <div class="px-4">
          <div
            class="avatar"
            @click="onOpenProfileMenu"
          >
            <div class="w-10 rounded-full">
              <img
                v-if="profile?.avatar"
                :src="profile?.avatar"
              />
              <IconsIconAvatar
                v-else
                class="text-white"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="drawer-side">
      <label
        for="my-drawer"
        aria-label="close sidebar"
        class="drawer-overlay"
      />
      <div class="bg-white min-h-full max-w-72 w-full">
        <ul
          v-if="!useAuth.authenticated"
          class="menu bg-white w-full rounded-none max-w-72 px-0 py-6"
        >
          <li>
            <NuxtLink
              to="/tim-kiem-viec-lam"
              @click="closeDrawer"
            >
              <span class="w-5">
                <IconStar24Regular class="text-primary w-6" />
              </span>
              <span class="text-gray-700">Cơ hội việc làm</span>
            </NuxtLink>
          </li>
          <Divider />
          <li>
            <NuxtLink
              to="/dieu-khoan-su-dung"
              @click="closeDrawer"
            >
              <span class="w-5">
                <IconsIconTerm class="text-primary text-base" />
              </span>
              <span class="text-gray-700">Điều khoản sử dụng</span>
            </NuxtLink>
          </li>
          <li class="mt-2">
            <NuxtLink
              to="/dieu-khoan-bao-mat"
              @click="closeDrawer"
            >
              <span class="w-5">
                <IconsIconSecurity class="text-primary text-base" />
              </span>
              <span class="text-gray-700">Điều khoản bảo mật</span>
            </NuxtLink>
          </li>
          <li class="mt-2">
            <NuxtLink
              to="/chinh-sach-du-lieu-ca-nhan"
              @click="closeDrawer"
            >
              <span class="w-5">
                <IconsIconPolicy class="text-primary text-base" />
              </span>
              <span class="text-gray-700">Chính sách dữ liệu cá nhân</span>
            </NuxtLink>
          </li>
        </ul>
        <div v-else>
          <div class="px-2 my-4">
            <div class="p-2 rounded-md flex justify-start items-center bg-primary/10">
              <div class="avatar">
                <div class="w-10 rounded-full">
                  <img
                    v-if="profile?.avatar"
                    :src="profile?.avatar"
                  />
                  <IconsIconAvatar
                    v-else
                    class="text-white"
                  />
                </div>
              </div>
              <div class="font-medium text-base ml-2">
                {{ profile?.fullName }}
              </div>
            </div>
          </div>
          <EmployerMenuBar @change="onMenuBarChange" />
          <Divider />
          <div class="px-4 my-4">
            <button
              class="btn btn-ghost"
              @click="onLogOut"
            >
              <IconLogOutOutline class="w-8 text-primary" />
              <span class="font-semibold text-info ml-2">Đăng xuất</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import EmployerMenuBar from '~/components/employer/layout/EmployerMenuBar.vue';
import { useAuthStore } from '~/store/auth.store';
import { useProfileStore } from '~/store/profile';
import Divider from '../common/Divider.vue';
import IconLogOutOutline from '../icons/IconLogOutOutline.vue';
import IconStar24Regular from '../icons/IconStar24Regular.vue';
const { profile } = storeToRefs(useProfileStore());
const useAuth = useAuthStore();
const closeDrawer = () => {
  document.getElementById('my-drawer')?.click();
};
const openDrawer = () => {
  document.getElementById('my-drawer')?.click();
};

const onLogOut = () => {
  useAuth.logUserOut();
  closeDrawer();
};

const onMenuBarChange = (key: string) => {
  closeDrawer();
};

const onOpenProfileMenu = () => {
  console.log('onOpenProfileMenu ==> ', useAuth.authenticated);
  if (useAuth.authenticated) {
    openDrawer();
  } else {
    useAuth.openAuthPopup(true);
  }
};
</script>
