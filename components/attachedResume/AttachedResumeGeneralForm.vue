<template>
  <div>
    <div class="grid grid-cols-1 gap-4">
      <FormItem label="Vị trí mong muốn">
        <input
          v-model="expectPosition"
          type="text"
          class="input input-bordered w-full"
        />
      </FormItem>
    </div>
    <div class="grid grid-cols-1 gap-4">
      <FormItem label="Nghề nghiệp">
        <OccupationMultipleSelect v-model="occupationIds" />
      </FormItem>
    </div>
    <div class="grid grid-cols-1 gap-2 lg:grid-cols-2">
      <FormItem label="Cấp bật hiện tại">
        <JobLevelSelect v-model="currentLevelId" />
      </FormItem>
      <FormItem label="Cấp bậc mong muốn">
        <JobLevelSelect v-model="expectLevelId" />
      </FormItem>
      <FormItem label="Mức lương mong muốn">
        <JobSalarySelect v-model="expectSalaryId" />
      </FormItem>
      <FormItem label="Trình độ học vấn">
        <JobDegreeSelect v-model="jobEducationId" />
      </FormItem>
      <FormItem label="Số năm kinh nghiệm">
        <JobExperienceSelect v-model="jobExperienceId" />
      </FormItem>
      <FormItem label="Địa điểm làm việc">
        <ProvinceMultipleSelect v-model="jobProvinceIds" />
      </FormItem>
      <FormItem label="Hình thức làm việc">
        <JobMethodSelect v-model="jobMethodId" />
      </FormItem>
      <FormItem label="Mục tiêu nghề nghiệp">
        <textarea
          v-model="careerObjective"
          class="textarea textarea-info textarea-bordered w-full"
        />
      </FormItem>
    </div>
    <div class="grid grid-cols-1 gap-4">
      <FormItem label="Kỹ năng mềm & cứng">
        <SkillMultipleTagsSelect v-model="jobSkills" />
      </FormItem>
    </div>
  </div>
</template>
<script setup lang="ts">
import { defineEmits } from 'vue';

import FormItem from '~/components/form/formItem.vue';
import type { Resume } from '~/types/resume.interface';
import JobDegreeSelect from '../resume/GeneralInformation/JobDegreeSelect.vue';
import JobExperienceSelect from '../resume/GeneralInformation/JobExperienceSelect.vue';
import JobLevelSelect from '../resume/GeneralInformation/JobLevelSelect.vue';
import JobMethodSelect from '../resume/GeneralInformation/JobMethodSelect.vue';
import JobSalarySelect from '../resume/GeneralInformation/JobSalarySelect.vue';
import OccupationMultipleSelect from '../resume/GeneralInformation/OccupationMultipleSelect.vue';
import ProvinceMultipleSelect from '../resume/GeneralInformation/ProvinceMultipleSelect.vue';
import SkillMultipleTagsSelect from '../resume/GeneralInformation/SkillMultipleTagsSelect.vue';

export interface IGeneralInformationFormModel {
  expectPosition: string;
  currentLevelId: number | null;
  expectLevelId: number | null;
  expectSalaryId: number | null;
  occupationIds: number[];
  jobEducationId: number | null;
  jobExperienceId: number | null;
  jobMethodId: number | null;
  jobSkills: string[];
  jobProvinceIds: number[];
  careerObjective: string | null;
}

const emit = defineEmits(['submit', 'cancel']);

const expectPosition = ref<string>('');
const currentLevelId = ref<number | null>();
const expectLevelId = ref<number | null>();
const expectSalaryId = ref<number | null>();
const occupationIds = ref<number[]>([]);
const jobEducationId = ref<number | null>();
const jobExperienceId = ref<number | null>();
const jobMethodId = ref<number | null>(null);
const jobSkills = ref<string[]>([]);
const jobProvinceIds = ref<number[]>([]);
const careerObjective = ref<string | null>(null);

const props = defineProps({
  defaultValue: {
    type: Object as () => Resume | null | undefined,
    default: () => null,
  },
});

onMounted(() => {
  const defaultValue = props.defaultValue;
  if (defaultValue) {
    expectPosition.value = defaultValue.expectPosition;
    currentLevelId.value = defaultValue.currentLevelId;
    expectLevelId.value = defaultValue.expectLevelId;
    expectSalaryId.value = defaultValue.expectSalary;
    occupationIds.value = defaultValue.occupationIds;
    jobEducationId.value = defaultValue.educationId;
    jobExperienceId.value = defaultValue.experienceId;
    jobMethodId.value = defaultValue.typeOfEmploymentId;
    jobSkills.value = defaultValue.softSkills;
    jobProvinceIds.value = defaultValue.provinceIds;
    careerObjective.value = defaultValue.careerObjective;
  }
});
</script>
