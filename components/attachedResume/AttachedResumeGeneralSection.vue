<template>
  <Collapse
    title="Thông tin chung"
    :show-adding="false"
    :show-edit="true"
    @on-edit="formOpen = !formOpen"
  >
    <GeneralInformationDetail
      v-if="!formOpen"
      :value="resume"
    />
    <AttachedResumeGeneralForm
      v-if="formOpen"
      :default-value="resume"
      @submit="onSubmit"
      @cancel="onCancel"
    />
  </Collapse>
</template>
<script setup lang="ts">
import Collapse from '~/components/common/collapse.vue';
import { useResumeStore } from '~/store/resume';
import { ResumeType, type Resume } from '~/types';
import GeneralInformationDetail from '../resume/GeneralInformation/GeneralInformationDetail.vue';

import AttachedResumeGeneralForm, { type IGeneralInformationFormModel } from './AttachedResumeGeneralForm.vue';

const props = defineProps({
  resume: {
    type: Object as () => Resume | null,
    required: false,
    default: null,
  },
});

const { updateGeneralInfo, createResume } = useResumeStore();
const formOpen = ref(false);

const onSubmit = (value: IGeneralInformationFormModel) => {
  if (props.resume && props.resume.id) {
    updateGeneralInfo(props.resume.id, {
      type: props.resume.type,
      expectPosition: value.expectPosition,
      expectSalary: value.expectSalaryId,
      expectLevelId: value.expectLevelId,
      currentLevelId: value.currentLevelId,
      educationId: value.jobEducationId,
      experienceId: value.jobExperienceId,
      softSkills: value.jobSkills,
      provinceIds: value.jobProvinceIds,
      occupationIds: value.occupationIds,
      careerObjective: value.careerObjective,
      typeOfEmploymentId: value.jobMethodId,
    });
  } else {
    createResume({
      type: ResumeType.online,
      expectPosition: value.expectPosition,
      expectSalary: value.expectSalaryId,
      expectLevelId: value.expectLevelId,
      currentLevelId: value.currentLevelId,
      educationId: value.jobEducationId,
      experienceId: value.jobExperienceId,
      softSkills: value.jobSkills,
      provinceIds: value.jobProvinceIds,
      occupationIds: value.occupationIds,
      careerObjective: value.careerObjective,
      typeOfEmploymentId: value.jobMethodId,
    });
  }
};

const onCancel = () => {
  formOpen.value = false;
};

// watch(() => useResumeStore().createSuccess, (value: boolean, oldValue: boolean) => {
//   if (value && !oldValue) {
//     formOpen.value = false;
//     getResumes();
//   }
// });

// watch(() => useResumeStore().updateSuccess, (value: boolean, oldValue: boolean) => {
//   if (value && !oldValue) {
//     formOpen.value = false;
//     getResumes();
//   }
// });
</script>
