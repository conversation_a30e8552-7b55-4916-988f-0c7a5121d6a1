<template>
  <div>
    <div class="mb-4">
      <NuxtLink
        to="http://localhost:3001/uploads/cv/1723642263286-882220149-MGP_Merchant_Interface_V1.4.4(VN).pdf"
        target="_blank"
        class="flex justify-between border border-base-200 rounded-sm p-2"
      >
        <div class="flex justify-start items-center">
          <div>
            <img
              :src="'/icons/pdf_file.svg'"
              class="w-8 h-8"
              alt="pdf_file"
            />
          </div>
          <div class="text-sm font-light ml-2">{{ 'dd' }}</div>
        </div>
        <div>
          <a
            href="javascript:void(0)"
            class="btn btn-circle btn-primary"
          >
            <IconTrash class="w-4 h-4" />
          </a>
        </div>
      </NuxtLink>
    </div>
    <button
      class="btn"
      @click="onOpenFileModal"
    >
      <IconUpload class="w-6 h-6" />
      <span>Tải file</span>
      <input
        id="cv_upload_input"
        type="file"
        accept=".pdf"
        class="hidden"
      />
    </button>
    <p class="text-gray-500 text-xs my-2 font-light">Định dạng file .doc, .docx, .pdf dung lượng {{ '<=' }} 2 MB</p>
  </div>
</template>
<script setup lang="ts">
import IconUpload from '../icons/IconUpload.vue';
import IconTrash from '../icons/IconTrash.vue';
import { getAccessToken } from '@/utils/authLocalstorage';
import type { HttpResponse } from '~/store/httpRequest.store';
const onOpenFileModal = () => {
  const input = document.getElementById('cv_upload_input') as HTMLInputElement;
  input.click();
  input.addEventListener('change', uploadFile);
};

const model = defineModel({
  type: String,
});

const uploadFile = (e: Event) => {
  const target = e.target as HTMLInputElement;
  if (!target.files) return;
  const file = target.files[0];
  if (file) {
    const baseUrl = useRuntimeConfig().public.apiUrl;
    const formData = new FormData();
    formData.append('file', file);
    $fetch<HttpResponse<any>>(`${baseUrl}/v1/cv/me/upload`, {
      method: 'POST',
      body: formData,
      headers: {
        Authorization: `Bearer ${getAccessToken()}`,
      },
    })
      .then((res) => {
        const cvFile = res.data?.file?.url;
        if (cvFile) {
          model.value = cvFile;
          console.log('Upload file success', res);
        } else {
          console.log('Upload file failed');
        }
      })
      .catch((err) => {
        console.log('Upload file failed', err);
      });
  }
};
</script>
