<template>
  <RecruitmentLink
    v-if="appliedJob.recruitment"
    :id="appliedJob.recruitment.id"
    :slug="appliedJob.recruitment.slug"
  >
    <div
      class="border border-secondary/20 p-3 flex justify-between my-1.5 rounded-sm cursor-pointer bg-green-50 lg:bg-white hover:shadow-md"
    >
      <div class="flex justify-between w-full">
        <div class="flex justify-start items-center">
          <div>
            <div class="avatar border border-base-200 rounded-md mr-6 p-1">
              <div class="w-16 lg:w-24">
                <img alt="vietlamlamdong.site" :src="appliedJob.recruitment.company?.logo || ''" />
              </div>
            </div>
          </div>
          <div class="my-auto">
            <div class="font-semibold w-full text-sm mb-1 line-clamp-2 lg:text-base">
              {{ appliedJob.recruitment.title }}
            </div>
            <div class="text-xs text-gray-500 font-light lg:text-xs lg:mb-2">
              {{ appliedJob.recruitment.company?.name }}
            </div>
            <div class="flex justify-start flex-col font-light lg:flex-row">
              <div class="flex justify-start items-center mt-1">
                <span class="text-gray-300 w-5 text-left">
                  <IconMoney class="w-5 h-5" />
                </span>
                <span class="text-xs px-2 text-info lg:text-base">
                  {{ formatSalaryRange(appliedJob.recruitment.minSalary, appliedJob.recruitment.maxSalary, 1000000) }}
                </span>
              </div>
              <div class="flex justify-start items-center mt-1">
                <span class="text-gray-300 w-5 text-left">
                  <IconLocation class="w-5 h-5" />
                </span>
                <span class="text-xs px-2 lg:text-base">{{ briefJobWorkspaces(appliedJob.recruitment) }}</span>
              </div>
              <div class="flex justify-start items-center mt-1 lg:text-base">
                <span class="text-gray-300 w-5 text-left">
                  <IconCalendar class="w-5 h-5" />
                </span>
                <span class="text-xs px-2 lg:text-base">{{ '19/02/2024' }}</span>
              </div>
            </div>
          </div>
        </div>
        <div>
          <div class="text-sm">
            <span class="text-gray-500">Ứng tuyển lúc: </span><span>{{ formatDate(appliedJob.appliedAt) }}</span>
          </div>
        </div>
      </div>
    </div>
  </RecruitmentLink>
</template>

<script setup lang="ts">
import { formatDate } from '@/utils/dateFormatter.utils.js';
import RecruitmentLink from '~/components/link/recruitmentLink.vue';
import { useCommonStore } from '~/store/common.store';
import type { Recruitment, RecruitmentApply } from '~/types';
import IconCalendar from '../icons/IconCalendar.vue';
import IconLocation from '../icons/IconLocation.vue';
import IconMoney from '../icons/IconMoney.vue';
const { provinces } = storeToRefs(useCommonStore());
const emits = defineEmits(['favorite']);

const props = defineProps({
  appliedJob: {
    type: Object as PropType<RecruitmentApply>,
    required: true,
  },
});

function briefJobWorkspaces(job: Recruitment) {
  return job.workspaces
    ?.map((workspace) => workspace.provinceId)
    .map((provinceId) => {
      const province = provinces.value.find((province) => province.id === provinceId);
      return province?.name;
    })
    .splice(0, 1)
    .join(', ');
}

function formatSalary(salary: number, unit: number) {
  const salaryInUnit = salary / unit;
  return salaryInUnit.toFixed(0);
}

function formatSalaryRange(minSalary: number, maxSalary: number, unit: number) {
  return `${formatSalary(minSalary, unit)} - ${formatSalary(maxSalary, unit)} triệu`;
}
</script>
