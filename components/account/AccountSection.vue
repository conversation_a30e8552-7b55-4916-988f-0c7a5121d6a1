<template>
  <div v-if="profile">
    <!-- <div class="bg-white px-2 py-4 lg:px-8">
      <div class="text-lg font-semibold">
        <span class="text-gray-400">Xin chào, </span>
        <span>{{ profile.fullName }}</span>
      </div>
    </div> -->
    <ProfileHeader :name="profile.fullName" />
    <br />
    <div class="mx-2 lg:mx-6">
      <RegisterProfile />
      <Profile />
    </div>
  </div>
  <div v-else>
    <div>Bạn chưa đăng nhập</div>
  </div>
</template>

<script setup lang="ts">
import { useCommonStore } from '~/store/common.store';
import { useProfileStore, type UpdateProfileDto } from '~/store/profile';
import Profile from './Profile.vue';
import ProfileHeader from './ProfileHeader.vue';
import RegisterProfile from './RegisterProfile.vue';
const { getProfile, updateProfile } = useProfileStore();
const { getCommonData } = useCommonStore();

const { profile } = storeToRefs(useProfileStore());
const { provinces } = storeToRefs(useCommonStore());

const emailForm = ref({
  email: '',
});

const passwordForm = ref({
  oldPassword: '',
  newPassword: '',
  confirmPassword: '',
});

const profileForm = ref<Partial<UpdateProfileDto>>({
  phoneNumber: null,
  fullName: null,
  birthday: null,
  provinceId: null,
  address: null,
  avatar: null,
  gender: null,
  maritalStatus: null,
});

const showUpdateProfileForm = ref(false);

// const showDialog = (id: string) => {
//   const dialog = document.getElementById(id);
//   if (!dialog) return;
//   if (dialog.hasAttribute('open')) {
//     dialog.removeAttribute('open');
//   } else {
//     dialog.setAttribute('open', '');
//   }
// };

const onUpdateEmail = () => {
  if (!emailForm.value.email) {
    return;
  }
  updateProfile({
    email: emailForm.value.email,
  });
};

const onUpdateProfile = () => {
  updateProfile({
    address: profileForm.value.address,
    avatar: profileForm.value.avatar,
    birthday: profileForm.value.birthday,
    fullName: profileForm.value.fullName,
    phoneNumber: profileForm.value.phoneNumber,
    provinceId: profileForm.value.provinceId,
    gender: profileForm.value.gender,
    maritalStatus: profileForm.value.maritalStatus,
  });
};

const onUpdatePassword = () => {
  console.log('Update password');
};

const addProfileForm = () => {
  if (profile && profile.value) {
    const { phoneNumber, fullName, birthday, provinceId, address, avatar, gender, maritalStatus } = profile.value;
    profileForm.value.address = address;
    profileForm.value.avatar = avatar;
    profileForm.value.birthday = birthday;
    profileForm.value.fullName = fullName;
    profileForm.value.phoneNumber = phoneNumber;
    profileForm.value.provinceId = provinceId;
    profileForm.value.gender = gender;
    profileForm.value.maritalStatus = maritalStatus;
  }
};

const onProvinceChange = (id: number) => {
  profileForm.value.provinceId = id;
};

const onGenderChange = (gender: string) => {
  profileForm.value.gender = gender;
};

const onMarriedStatus = (status: number) => {
  profileForm.value.maritalStatus = status;
};

const onFormVisible = () => {
  showUpdateProfileForm.value = !showUpdateProfileForm.value;
  addProfileForm();
};

onMounted(() => {
  getProfile();
  getCommonData();
});
</script>
