<template>
  <div v-if="profile">
    <collapse
      title="Thông tin đăng ký"
      :show-adding="false"
    >
      <div class="flex flex-col lg:items-center lg:mb-4 lg:justify-between lg:flex-row">
        <HorizontalFormItem
          label="Email"
          class="w-full"
        >
          <div class="flex items-center justify-start w-full flex-col md:flex-row">
            <label
              class="input input-bordered w-full flex items-center justify-between gap-2 !bg-gray-200 !border-none lg:max-w-96"
            >
              <div>{{ profile.emailAddress }}</div>
              <span>
                <IconCheck
                  v-if="profile.verifyEmail"
                  class="w-4 text-success"
                />
                <IconWarningCircleFil
                  v-else
                  class="w-4 text-warning"
                />
              </span>
            </label>
            <div
              v-if="!profile.verifyEmail"
              class="text-right lg:ml-2"
            >
              <button
                class="btn btn-link text-info no-underline"
                @click="onOpenEmailOtpModal"
              >
                <PERSON><PERSON><PERSON> thực ngay
              </button>
            </div>
          </div>
        </HorizontalFormItem>
        <button
          class="btn btn-soft btn-primary w-full !max-w-full lg:!max-w-48"
          @click="onOpenEmailModal(true)"
        >
          Sửa email
        </button>
      </div>
      <div class="flex flex-col lg:items-center lg:mb-4 lg:justify-between lg:flex-row">
        <HorizontalFormItem
          label="Mật khẩu"
          class="w-full"
        >
          <input
            type="password"
            class="input !w-full !bg-gray-200 border-none lg:max-w-96"
            disabled
            value="********"
          />
        </HorizontalFormItem>
        <button
          class="btn btn-soft btn-primary w-full !max-w-full lg:!max-w-48"
          @click="openPasswordModal = !openPasswordModal"
        >
          Đổi mật khẩu
        </button>
      </div>
    </collapse>
    <dialog
      id="profile_email_modal"
      :class="['modal', openEmailModal ? 'modal-open' : null]"
    >
      <div className="modal-box rounded-md bg-white">
        <h3 className="font-bold text-lg mb-6 text-center">Thay đổi email</h3>
        <FormItem
          label="Email hiện tại"
          :required="true"
        >
          <input
            id="email"
            type="email"
            :value="profile.emailAddress"
            :disabled="true"
            className="input input-bordered w-full btn-disabled"
          />
        </FormItem>
        <FormItem
          label="Email mới"
          :required="true"
          :error="newEmailError"
        >
          <input
            id="email"
            ref="newEmailRef"
            v-model="newEmail"
            type="email"
            className="input input-bordered w-full"
          />
        </FormItem>
        <div className="modal-action">
          <form
            method="dialog"
            class="grid grid-cols-2 gap-x-2 w-full"
          >
            <button
              className="btn btn-soft btn-primary w-full"
              @click="onOpenEmailModal(false)"
            >
              Hủy bỏ
            </button>
            <button
              :class="['btn btn-primary w-full ', loading ?? 'btn-disabled']"
              @click="onUpdateEmail"
            >
              <Spin v-if="loading" />
              <span>Lưu email mới</span>
            </button>
          </form>
        </div>
      </div>
    </dialog>
    <dialog
      id="profile_email_modal"
      :class="['modal', openPasswordModal ? 'modal-open' : null]"
    >
      <div className="modal-box bg-white">
        <h3 className="font-bold text-lg mb-6">Mật khẩu cũ</h3>
        <FormItem
          label="Mật khẩu cũ"
          :error="newPasswordError['old']"
        >
          <input
            id="email"
            v-model="newPassword.old"
            type="password"
            className="input input-bordered w-full"
            placeholder="Nhập mật khẩu cũ"
          />
        </FormItem>
        <FormItem
          label="Mật khẩu mới"
          :required="true"
          :error="newPasswordError['new']"
        >
          <input
            id="email"
            v-model="newPassword.new"
            type="password"
            className="input input-bordered w-full"
            placeholder="Nhập mật khẩu mới"
          />
        </FormItem>
        <FormItem
          label="Nhập lại mật khẩu mới"
          :required="true"
          :error="newPasswordError['confirm']"
        >
          <input
            id="email"
            v-model="newPassword.confirm"
            type="password"
            className="input input-bordered w-full"
            placeholder="Nhập lại mật khẩu mới"
          />
        </FormItem>
        <div className="modal-action">
          <form
            method="dialog"
            class="flex w-full space-x-4 items-center"
          >
            <button
              class="btn btn-soft btn-primary w-full max-w-1/2"
              @click="openPasswordModal = false"
            >
              Hủy bỏ
            </button>
            <button
              class="btn btn-primary w-full max-w-1/2"
              @click="onUpdatePassword"
            >
              <Spin v-if="loading" />
              <span>Cập nhật</span>
            </button>
          </form>
        </div>
      </div>
    </dialog>
    <dialog
      id="update_email_success_modal"
      class="modal"
    >
      <div class="modal-box rounded-md max-w-[400px] text-center">
        <div>
          <img
            :src="'/images/bg-dialog-success.png'"
            class="w-full object-cover"
            alt="bg-dialog-success"
          />
          <IconCheck class="text-info absolute top-0 left-[50%] w-24 h-24 translate-x-[-50%] translate-y-[50%]" />
        </div>
        <div class="text-primary text-base font-semibold">Đổi email thành công!</div>
        <div class="text-xs my-4">Bạn có thể đăng nhập với email mới</div>
      </div>
    </dialog>

    <EmailVerifyModal
      v-model:modal-visible="openEmailOtpModal"
      :email="profile.emailAddress"
      @ok="onVerifyEmailSuccess()"
    />
  </div>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
import { useProfileStore } from '~/store/profile';
import EmailVerifyModal from '../auth/EmailVerifyModal.vue';
import Spin from '../common/Spin.vue';
import collapse from '../common/collapse.vue';
import HorizontalFormItem from '../form/HorizontalFormItem.vue';
import FormItem from '../form/formItem.vue';
import IconCheck from '../icons/IconCheck.vue';
import IconWarningCircleFil from '../icons/IconWarningCircleFil.vue';
const { profile, loading, verifyEmailError, verifyEmailSuccess } = storeToRefs(useProfileStore());
const { updateProfile, updatePassword, sendOtpToVerifyEmail, verifyEmail, verifyEmailPending } = useProfileStore();

const { isMobile } = useDevice();
const newEmailRef = ref<HTMLInputElement | null>(null);
const openEmailModal = ref<boolean>(false);
const newEmail = ref('');
const newEmailError = ref('');
const openEmailOtpModal = ref(false);
const emailOtp = ref<number[]>([]);
const validateEmail = () => {
  if (!newEmail.value) {
    newEmailError.value = 'Email không được để trống';
    return false;
  }
  if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(newEmail.value)) {
    newEmailError.value = 'Email không hợp lệ';
    return false;
  }
  if (newEmail.value === profile.value?.emailAddress) {
    newEmailError.value = 'Email mới không được trùng với email hiện tại';
    return false;
  }
  newEmailError.value = '';
  return true;
};

const openPasswordModal = ref<boolean>(false);
const newPassword = ref({
  old: '' as string,
  new: '' as string,
  confirm: '' as string,
});
const newPasswordError = ref({
  old: '' as string,
  new: '' as string,
  confirm: '' as string,
});

const validatePassword = () => {
  if (!newPassword.value.new) {
    newPasswordError.value.new = 'Mật khẩu mới không được để trống';
    return false;
  }
  if (newPassword.value.new === newPassword.value.old) {
    newPasswordError.value.new = 'Mật khẩu mới không được trùng với mật khẩu cũ';
    return false;
  }
  if (newPassword.value.new !== newPassword.value.confirm) {
    newPasswordError.value.confirm = 'Mật khẩu mới không trùng khớp';
    return false;
  }
  newPasswordError.value = {
    old: '',
    new: '',
    confirm: '',
  };
  return true;
};

const onOpenEmailModal = (open: boolean) => {
  openEmailModal.value = open;
  if (openEmailModal.value) {
    newEmail.value = '';
    setTimeout(() => {
      newEmailRef.value?.focus();
    }, 50);
  }
};

const onUpdateEmail = async () => {
  const isValid = validateEmail();
  if (isValid) {
    await updateProfile({ email: newEmail.value });
  }
};

const onUpdatePassword = async () => {
  const isValid = validatePassword();
  if (isValid) {
    await updatePassword({
      newPassword: newPassword.value.new,
      oldPassword: newPassword.value.old,
    });
  }
};
const onVerifyEmailSuccess = () => {
  useProfileStore().getProfile();
};

const onOpenEmailOtpModal = () => {
  openEmailOtpModal.value = true;
};

watch(
  () => useProfileStore().updateSuccess,
  (value, oldValue) => {
    if (value !== oldValue && value) {
      openEmailModal.value = false;
    }
  },
);

watch(
  () => useProfileStore().verifyEmailSuccess,
  (value, oldValue) => {
    if (value !== oldValue && value) {
      openEmailOtpModal.value = false;
    }
  },
);
</script>
