<template>
  <Spin :loading="loading">
    <div class="flex justify-start items-center">
      <div class="mr-8">
        <div class="avatar">
          <div class="w-24 rounded-full">
            <img alt="vietlamlamdong.site" :src="avatar ? avatar : '/images/default_avatar.png'" />
          </div>
        </div>
      </div>
      <div>
        <button
          class="btn"
          @click="uploadFile"
        >
          <IconUpload class="w-6 text-primary" />
          <span>Tải ảnh lên</span>
        </button>
        <input
          id="input_upload_avatar"
          type="file"
          class="hidden"
          accept="image/jpg,image/jpeg,image/png"
          @change="onFileChange"
        />
        <p class="font-light text-xs text-gray-400 my-3">
          Định dạng .JPG, .JPEG, .PNG dung lượng thấp hơn 300 KB với kích thước tối thiểu 300x300 px
        </p>
      </div>
    </div>
    <Divider />
    <div class="grid grid-cols-1 lg:grid-cols-2 lg:gap-4">
      <FormItem
        label="Họ và tên"
        :error="error['fullName']"
      >
        <input
          id="full_name"
          v-model="fullName"
          type="text"
          className="input input-bordered w-full"
        />
      </FormItem>
      <FormItem
        label="Ngày sinh"
        :error="error['birthday']"
      >
        <input
          id="birthday"
          v-model="birthday"
          type="date"
          className="input input-bordered w-full"
        />
      </FormItem>
      <FormItem
        label="Tỉnh / Thành phố"
        :error="error['provinceId']"
      >
        <!-- <ProvinceSelect v-model="provinceId" /> -->
        <DropdownSearchSelect
          v-model="provinceId"
          :list="provinces"
          :show-search="true"
          value-key="name"
          id-key="id"
        />
      </FormItem>
      <FormItem
        label="Địa chỉ"
        :error="error['address']"
      >
        <input
          id="birthday"
          v-model="address"
          type="text"
          className="input input-bordered w-full"
        />
      </FormItem>
      <FormItem
        label="Giới tính"
        :error="error['gender']"
      >
        <div class="space-x-4 flex justify-start items-center">
          <div class="form-control">
            <label class="label cursor-pointer">
              <span class="text-gray-800 text-sm mr-4">Nam</span>
              <input
                type="radio"
                name="radio-10"
                class="radio radio-primary"
                :checked="gender === Gender.male"
                @click="gender = Gender.male"
              />
            </label>
          </div>
          <div class="form-control">
            <label class="label cursor-pointer">
              <span class="text-gray-800 text-sm mr-4">Nữ</span>
              <input
                type="radio"
                name="radio-10"
                class="radio radio-primary"
                :checked="gender === Gender.female"
                @click="gender = Gender.female"
              />
            </label>
          </div>
        </div>
      </FormItem>
      <FormItem
        label="Tình trạng hôn nhân"
        :error="error['maritalStatus']"
      >
        <div class="space-x-4 flex justify-start items-center">
          <div class="form-control">
            <label class="label cursor-pointer">
              <span class="text-gray-800 text-sm mr-4">Độc thân</span>
              <input
                type="radio"
                name="radio-married-status-01"
                class="radio radio-primary"
                :checked="maritalStatus === TinyInt.No"
                @click="maritalStatus = TinyInt.No"
              />
            </label>
          </div>
          <div class="form-control">
            <label class="label cursor-pointer">
              <span class="text-gray-800 text-sm mr-4">Đã lập gia đình</span>
              <input
                type="radio"
                name="radio-married-status-02"
                class="radio radio-primary"
                :checked="maritalStatus === TinyInt.Yes"
                @click="maritalStatus = TinyInt.Yes"
              />
            </label>
          </div>
        </div>
      </FormItem>
      <FormItem
        label="Số điện thoại"
        :error="error['phoneNumber']"
      >
        <input
          id="phone_number"
          v-model="phoneNumber"
          type="text"
          className="input input-bordered w-full"
        />
      </FormItem>
    </div>
    <div class="flex justify-center md:justify-end space-x-2">
      <CancelButton
        title="Hủy"
        @click="emits('onCancel')"
      />
      <OkButton
        text="Lưu thông tin"
        @click="onOk"
      />
    </div>
  </Spin>
</template>
<script setup lang="ts">
import { defineEmits, ref } from 'vue';
import { Gender, TinyInt, type Customer, type File } from '~/types';
import DropdownSearchSelect from '../common/DropdownSearchSelect.vue';
import Spin from '../common/Spin.vue';
import CancelButton from '../common/CancelButton.vue';
import Divider from '../common/Divider.vue';
import OkButton from '../common/OkButton.vue';
import FormItem from '../form/formItem.vue';
import ProvinceSelect from '../resume/ProvinceSelect.vue';
import { getAccessToken } from '~/utils/authLocalstorage';
import { useCommonStore } from '~/store/common.store';
import IconUpload from '../icons/IconUpload.vue';
import { useToast } from '~/store/toast';
import type { HttpResponse } from '~/store/httpRequest.store';

const { provinces } = storeToRefs(useCommonStore());

const emits = defineEmits(['onCancel', 'onOk']);

export interface IProfileFormModel {
  fullName: string;
  birthday: string;
  provinceId: number;
  address: string;
  gender: Gender;
  maritalStatus: TinyInt;
  phoneNumber: string;
  avatar: string | null;
}

const props = defineProps({
  defaultValue: {
    type: Object as () => Customer | null,
    default: () => null,
  },
  loading: {
    type: Boolean,
    default: false,
  },
});

const avatar = ref<string | null>(null);
const fullName = ref<string | null>(null);
const birthday = ref<string | null>(null);
const provinceId = ref<number | null>(null);
const address = ref<string | null>(null);
const gender = ref<Gender>(Gender.male);
const maritalStatus = ref<TinyInt>(TinyInt.No);
const phoneNumber = ref();

const error = ref({
  fullName: '',
  birthday: '',
  provinceId: '',
  address: '',
  phoneNumber: '',
  gender: '',
  maritalStatus: '',
});

onMounted(() => {
  const defaultValue = props.defaultValue;
  if (defaultValue) {
    avatar.value = defaultValue.avatar;
    fullName.value = defaultValue.fullName;
    birthday.value = defaultValue.birthday;
    provinceId.value = defaultValue.provinceId;
    address.value = defaultValue.address;
    gender.value = defaultValue.gender;
    phoneNumber.value = defaultValue.phoneNumber;
    maritalStatus.value = defaultValue.maritalStatus;
  }
});

const validate = () => {
  error.value = {
    fullName: '',
    birthday: '',
    provinceId: '',
    address: '',
    phoneNumber: '',
    gender: '',
    maritalStatus: '',
  };
  let isValid = true;
  if (!fullName.value) {
    error.value.fullName = 'Họ và tên không được để trống';
    isValid = false;
  }
  if (!birthday.value) {
    error.value.birthday = 'Ngày sinh không được để trống';
    isValid = false;
  }
  if (!provinceId.value) {
    error.value.provinceId = 'Tỉnh / Thành phố không được để trống';
    isValid = false;
  }
  if (!address.value) {
    error.value.address = 'Địa chỉ không được để trống';
    isValid = false;
  }
  if (!phoneNumber.value) {
    error.value.phoneNumber = 'Số điện thoại không được để trống';
    isValid = false;
  }
  if (!/^\d{10,11}$/.test(phoneNumber.value)) {
    error.value.phoneNumber = 'Số điện thoại không hợp lệ';
    isValid = false;
  }
  return isValid;
};

const uploadFile = () => {
  const input = document.getElementById('input_upload_avatar');
  if (input) {
    input.click();
  }
};

const onFileChange = async (e: Event) => {
  const target = e.target as HTMLInputElement;
  const file = target.files?.[0];
  if (file) {
    const reader = new FileReader();
    reader.onload = (e) => {
      avatar.value = e.target?.result as string;
    };
    reader.readAsDataURL(file);

    const config = useRuntimeConfig();
    const baseUrl = config.public.apiUrl;
    const formData = new FormData();
    formData.append('file', file);
    try {
      const response = await fetch(`${baseUrl}/v1/files/upload`, {
        method: 'POST',
        body: formData,
        headers: {
          Authorization: `Bearer ${getAccessToken()}`,
        },
      });
      const httpResponse = (await response.json()) as HttpResponse<File>;

      if (httpResponse?.success && httpResponse?.data) {
        avatar.value = httpResponse.data?.url;
      }
    } catch (error) {
      console.error(error);
      useToast().error('Có lỗi xảy ra trong quá trình tải ảnh lên');
    }
  }
};

const onOk = () => {
  const isValid = validate();
  if (!isValid) {
    return;
  }

  if (fullName.value && birthday.value && provinceId.value && address.value && phoneNumber.value) {
    const modalValue: IProfileFormModel = {
      fullName: fullName.value,
      birthday: birthday.value,
      provinceId: provinceId.value,
      address: address.value,
      gender: gender.value,
      maritalStatus: maritalStatus.value,
      phoneNumber: phoneNumber.value,
      avatar: avatar.value,
    };

    emits('onOk', modalValue);
  }
};

watch(
  () => provinceId.value,
  (value: number | null) => {
    console.log('Province ID changed', value);
  },
);
</script>
