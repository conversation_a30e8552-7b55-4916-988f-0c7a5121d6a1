<template>
  <Collapse
    v-if="profile"
    title="Thông tin cá nhân"
    :show-adding="false"
    :show-edit="true"
    edit-title="Cập nhật"
    @on-edit="formOpen = !formOpen"
  >
    <div
      v-if="!formOpen"
      class="flex justify-start"
    >
      <div class="mr-6">
        <div class="avatar">
          <div class="w-20 rounded-full">
            <img alt="vietlamlamdong.site" :src="profile.avatar ? profile.avatar : '/images/default_avatar.png'" />
          </div>
        </div>
      </div>
      <div>
        <h3 class="font-semibold">{{ profile.fullName }}</h3>
        <div>
          <div class="font-light text-sm my-2">Số điện thoại: <Dot /> {{ profile.phoneNumber }}</div>
          <div class="font-light text-sm">
            {{ profile.birthday }}
            <Dot />
            {{ profile.gender ? 'Nam' : 'N<PERSON>' }}
            <Dot />
            {{ profile.maritalStatus ? 'Đ<PERSON> kết hôn' : '<PERSON><PERSON><PERSON> thân' }}
          </div>
          <div class="font-light text-sm">{{ profile.address }}</div>
        </div>
      </div>
    </div>
    <ProfileForm
      v-if="formOpen"
      :loading="updatePending"
      :default-value="profile"
      @on-cancel="formOpen = false"
      @on-ok="onSubmit"
    />
  </Collapse>
</template>
<script setup lang="ts">
import { useProfileStore } from '~/store/profile';
import Dot from '../common/Dot.vue';
import Collapse from '../common/collapse.vue';
import ProfileForm, { type IProfileFormModel } from './ProfileForm.vue';
const { profile, updatePending, updateSuccess } = storeToRefs(useProfileStore());
const { updateProfile } = useProfileStore();

const formOpen = ref(false);
const onSubmit = (value: IProfileFormModel) => {
  if (!value) return;
  updateProfile({
    phoneNumber: value.phoneNumber,
    fullName: value.fullName,
    birthday: value.birthday,
    provinceId: value.provinceId,
    address: value.address,
    gender: value.gender,
    maritalStatus: value.maritalStatus,
    avatar: value.avatar,
  });
};

watch(
  () => useProfileStore().updateSuccess,
  (value: boolean, oldValue: boolean) => {
    if (value && oldValue !== value) {
      formOpen.value = false;
    }
  },
);
</script>
