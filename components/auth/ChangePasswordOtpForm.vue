<template>
  <Spin :loading="useAuth.changePasswordWithOtpPending">
    <FormItem
      label="Email"
      type="email"
      :required="true"
      :error="errors.email"
    >
      <input
        v-model="email"
        type="text"
        class="input input-md w-full"
        placeholder="Email"
      />
    </FormItem>
    <FormItem
      label="Mật khẩu mới"
      type="password"
      :required="true"
      :error="errors.password"
    >
      <input
        v-model="password"
        type="password"
        class="input input-md w-full"
        placeholder="Mật khẩu mới"
      />
    </FormItem>
    <FormItem
      label="Nhập lại mật khẩu"
      type="password"
      :required="true"
      :error="errors.confirmPassword"
    >
      <input
        v-model="confirmPassword"
        type="password"
        class="input input-md w-full"
        placeholder="Mật khẩu mới"
      />
    </FormItem>

    <button
      class="btn btn-md bg-primary text-white w-full"
      :disabled="useAuth.changePasswordWithOtpPending"
      @click="onSubmit"
    >
      <PERSON><PERSON><PERSON> mật khẩu
    </button>
  </Spin>
</template>
<script setup lang="ts">
import { object, string, ref } from 'yup';
import { toTypedSchema } from '@vee-validate/yup';
import { useAuthStore } from '~/store/auth.store';
import { useToast } from '~/store/toast';
import FormItem from '../form/formItem.vue';
import Spin from '../common/Spin.vue';
const emits = defineEmits(['ok']);

export interface IChangePasswordOtpEvent {
  email: string;
  success: boolean;
}

const props = defineProps({
  otp: {
    type: String,
    required: true,
  },
  defaultEmail: {
    type: String,
    required: true,
  },
});

const useAuth = useAuthStore();

interface FormState {
  email: string;
  password: string;
  confirmPassword: string;
}
const { values, handleSubmit, setErrors, errors, defineField } = useForm<FormState>({
  validationSchema: toTypedSchema(
    object({
      email: string().email('Email không đúng định dạng').required('Email không được để trống'),
      password: string().required('Mật khẩu không được để trống'),
      confirmPassword: string()
        .required('Mật khẩu không được để trống')
        .test('passwords-match', 'Mật khẩu không khớp', function (value) {
          return this.parent.password === value;
        }),
    }),
  ),
  initialValues: {
    email: props.defaultEmail,
    password: 'dangkhoi123',
    confirmPassword: 'dangkhoi123',
  },
});
const [email] = defineField('email');
const [password] = defineField('password');
const [confirmPassword] = defineField('confirmPassword');

const onSubmit = handleSubmit(async (values) => {
  await useAuth.changePasswordWithOtp(values.email, props.otp, values.password);
});

watch(
  () => useAuth.changePasswordWithOtpFailed,
  (value, oldValue) => {
    if (value && value !== oldValue) {
      useToast().error(useAuth.changePasswordWithOtpMessage || 'Đổi mật khẩu thất bại');
    }
  },
);

watch(
  () => useAuth.changePasswordWithOtpSuccess,
  (value, oldValue) => {
    if (value && value !== oldValue) {
      useToast().success('Đổi mật khẩu thành công');

      const event: IChangePasswordOtpEvent = {
        email: values.email,
        success: true,
      };
      emits('ok', event);
    }
  },
);
</script>
