<template>
  <div class="form-control">
    <div class="text-center mb-8">
      <div class="text-lg">Ng<PERSON><PERSON><PERSON> tìm việc</div>
      <div class="text-2xl"><PERSON><PERSON><PERSON> ký hoặc Đăng nhập</div>
    </div>

    <FormItem
      label="Địa chỉ email"
      type="email"
      placeholder="Nhập địa chỉ email"
      :error="useLoginForm.errors.value.email"
      :required="true"
    >
      <input
        v-model="email"
        type="email"
        placeholder="Email"
        class="input input-md input-bordered w-full"
      />
    </FormItem>
    <FormItem
      label="Mật khẩu"
      type="password"
      placeholder="Nhập mật khẩu"
      :error="useLoginForm.errors.value.password"
      :required="true"
    >
      <input
        v-model="password"
        type="password"
        placeholder="password"
        class="input input-md input-bordered w-full"
      />
    </FormItem>
    <div class="flex justify-end my-3">
      <label
        class="cursor-pointer text-info text-sm font-semibold"
        @click="emits('forgotPassword', email)"
      >
        Quên mật khẩu?
      </label>
    </div>
    <button
      class="btn btn-primary btn-lg text-white text-lg w-full"
      :disabled="useAuth.signInWithEmailPending"
      @click="onLoginSubmit"
    >
      <span
        v-if="useAuth.signInWithEmailPending"
        class="loading loading-spinner"
      ></span>
      Tiếp tục
    </button>
  </div>
</template>
<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/yup';
import { useForm } from 'vee-validate';
import { object, string } from 'yup';
import { useAuthStore } from '~/store/auth.store';

const emits = defineEmits(['forgotPassword']);
const props = defineProps<{
  defaultEmail: string;
}>();

const useAuth = useAuthStore();

interface FormState {
  email: string;
  password: string;
}
const useLoginForm = useForm<FormState>({
  validationSchema: toTypedSchema(
    object({
      email: string().email('Email không đúng định dạng').required('Email không được để trống'),
      password: string().required('Mật khẩu không được để trống'),
    }),
  ),
  initialValues: {
    email: props.defaultEmail,
    password: '',
  },
});

const [email] = useLoginForm.defineField('email');
const [password] = useLoginForm.defineField('password');

const onLoginSubmit = useLoginForm.handleSubmit(async (values) => {
  useAuth.signIn(values.email, values.password);
});
</script>
