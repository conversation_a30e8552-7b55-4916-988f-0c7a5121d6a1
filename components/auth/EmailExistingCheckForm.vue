<template>
  <div class="form-control">
    <FormItem
      label="Địa chỉ email"
      type="email"
      placeholder="Nhập địa chỉ email"
      :error="errors.email"
    >
      <input
        v-model="email"
        type="email"
        placeholder="Email"
        class="input input-bordered w-full input-md"
      />
    </FormItem>
    <AuthPolicySignUpCheckBox />
    <br />
    <button
      class="btn btn-primary btn-lg text-white text-lg w-full"
      :disabled="useAuth.checkEmailExistPending"
      @click="onSubmit"
    >
      <span
        v-if="useAuth.checkEmailExistPending"
        class="loading loading-spinner"
      ></span>
      Tiếp tục
    </button>
  </div>
</template>
<script lang="ts" setup>
import { toTypedSchema } from '@vee-validate/yup';
import { object, string } from 'yup';
import { useAuthStore } from '~/store/auth.store';
import AuthPolicySignUpCheckBox from './AuthPolicySignUpCheckBox.vue';

const useAuth = useAuthStore();

const props = defineProps({
  email: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['ok']);

export interface IEmailExistCheckEvent {
  email: string;
  exist: boolean;
}

interface FormState {
  email: string;
}
const { values, handleSubmit, setErrors, errors, defineField } = useForm<FormState>({
  validationSchema: toTypedSchema(
    object({
      email: string().email('Email không đúng định dạng').required('Email không được để trống'),
    }),
  ),
  initialValues: {
    email: '',
  },
});
const [email] = defineField('email');

const onSubmit = handleSubmit(async (values) => {
  const emailExist = await useAuth.checkEmailExist(values.email);
  if (typeof emailExist === 'boolean') {
    const eventData: IEmailExistCheckEvent = {
      email: values.email,
      exist: emailExist,
    };
    emits('ok', eventData);
  }
});

onMounted(() => {
  if (props.email) {
    email.value = props.email;
  }
});
</script>
