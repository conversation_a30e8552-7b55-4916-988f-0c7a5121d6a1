<template>
  <dialog
    id="update_otp_modal"
    :class="[
      'modal',
      {
        'modal-open': openEmailOtpModal,
      },
    ]"
  >
    <div
      :class="[
        'modal-box w-full rounded-md h-full max-h-full text-center p-0 bg-white lg:max-w-[880px] lg:max-h-[356px]',
      ]"
    >
      <div class="flex h-full">
        <div class="flex flex-col px-4 justify-start pt-32 lg:justify-center lg:w-1/2 lg:pt-0">
          <a
            class="rounded-full w-9 h-9 bg-base-100 absolute top-2 left-2 p-2"
            href="javascript:void(0)"
            @click="onClose"
          >
            <IconDismissRegular />
          </a>
          <h1 class="text-xl font-semibold mb-1 select-none">Nhập mã OTP</h1>
          <p class="text-sm">
            Vui lòng nhập mã OTP mà chúng tôi đã gửi đến
            <b>{{ props.email }}</b> mà bạn đã đăng ký. Kiểm tra hộp thư Spam nếu không thấy mail.
          </p>

          <OtpInput
            v-if="openEmailOtpModal"
            v-model:value="otp"
            @ok="onOtpOk"
          />

          <p
            v-if="verifyEmailError"
            class="text-error text-xs font-semibold"
          >
            {{ 'Mã OTP chưa đúng. Vui lòng thử lại' }}
          </p>
          <div class="text-sm font-semibold">
            <span>Không nhận được mã OTP?</span>
            <span
              v-if="useAuth.requestForgotPasswordEmailOtpPending"
              class="loading loading-spinner loading-xs text-primary"
            ></span>
            <label
              v-if="countDown == 0"
              class="text-info ml-1 hover:cursor-pointer"
              @click="sendOtpToVerifyEmail"
            >
              Gửi lại
            </label>
            <CountDown
              v-else
              v-model:value="countDown"
            />
          </div>
        </div>
        <div
          class="hidden w-1/2 !bg-cover !bg-no-repeat lg:block"
          :style="{
            background: 'url(/images/bg-forget-password.png)',
          }"
        />
      </div>
    </div>
  </dialog>
</template>
<script setup lang="ts">
import { useAuthStore } from '~/store/auth.store';
import IconDismissRegular from '~/components/icons/IconDismissRegular.vue';
import { ToastType, useToast } from '~/store/toast';
import CountDown from '../common/CountDown.vue';
import OtpInput from './OtpInput.vue';

const COUNTDOWN = 60;

export interface EmailOtpConfirmationModalSuccessEvent {
  email: string;
  otp: string;
  success: boolean;
}

const props = defineProps({
  email: {
    type: String,
    required: true,
  },
});

const useAuth = useAuthStore();

const openEmailOtpModal = defineModel('modalVisible', {
  type: Boolean,
  default: false,
});

const verifyEmailError = ref(false);
const countDown = ref(0);
const otp = ref('');
const emits = defineEmits(['ok', 'cancel']);

const sendOtpToVerifyEmail = () => {
  if (useAuth.requestForgotPasswordEmailOtpPending) {
    return;
  }
  useAuth.requestForgotPasswordEmailOtp(props.email);
};

const onOtpOk = (otpValue: string) => {
  useAuth.verifyForgotPasswordEmailOtp(props.email, otpValue);
};

const onClose = () => {
  openEmailOtpModal.value = false;
  emits('cancel', openEmailOtpModal.value);
};

onMounted(() => {
  sendOtpToVerifyEmail();
});

watch(
  () => useAuth.requestForgotPasswordEmailOtpSuccess,
  (value, oldValue) => {
    if (value && value !== oldValue) {
      countDown.value = COUNTDOWN;
      useToast().showToast('Gửi mã OTP thành công', ToastType.Success);
    }
  },
);

watch(
  () => useAuth.requestForgotPasswordEmailOtpFailed,
  (value, oldValue) => {
    if (value && value !== oldValue) {
      useToast().showToast(useAuth.requestForgotPasswordEmailOtpMessage || 'Gửi mã OTP thất bại', ToastType.Error);
    }
  },
);

watch(
  () => useAuth.verifyForgotPasswordEmailOtpFailed,
  (value, oldValue) => {
    if (value && value !== oldValue) {
      const msg = useAuth.verifyForgotPasswordEmailOtpMessage || 'Xác thực mã OTP thất bại';
      useToast().showToast(msg, ToastType.Error);
    }
  },
);

watch(
  () => useAuth.verifyForgotPasswordEmailOtpSuccess,
  (value, oldValue) => {
    if (value && value !== oldValue) {
      useToast().showToast('Xác thực mã OTP thành công', ToastType.Success);
      openEmailOtpModal.value = false;
      const otpConfirmationEventData: EmailOtpConfirmationModalSuccessEvent = {
        email: props.email,
        otp: otp.value,
        success: true,
      };
      emits('ok', otpConfirmationEventData);
    }
  },
);
</script>
