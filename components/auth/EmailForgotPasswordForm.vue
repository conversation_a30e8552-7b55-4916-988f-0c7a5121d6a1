<template>
  <div class="form-control">
    
  </div>
</template>

<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/yup';
import { useForm } from 'vee-validate';

import { object, string } from 'yup';
interface FormForgotPassword {
  email: string;
  code: string;
}

const { errors, handleSubmit, defineField } = useForm<FormForgotPassword>({
  validationSchema: toTypedSchema(
    object({
      email: string().email('Email không đúng định dạng').required('Email không được để trống'),
      code: string().required('Mã xác nhận không được để trống'),
    }),
  ),
  initialValues: {
    email: '',
    code: '',
  },
});
const [email] = defineField('email');

const onLoginSubmit = handleSubmit(async (values) => {
  console.log(values);
});
</script>
