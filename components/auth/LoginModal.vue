<template>
  <dialog
    id="my_modal_4"
    :class="['modal', show ? 'modal-open' : null]"
  >
    <div class="modal-box rounded-md relative">
      <div class="flex justify-between items-center">
        <div v-if="formStep.state !== 'start'">
          <button
            class="btn btn-circle btn-ghost btn-sm"
            @click="onBack"
          >
            <ArrowBackIosNewRound />
          </button>
        </div>
        <button
          class="btn btn-circle btn-ghost btn-sm"
          @click="openAuthPopup(false)"
        >
          <IconClearRound />
        </button>
      </div>
      <LoginOptionForm
        v-if="formStep.state === 'start'"
        :email="formStep.email"
        @change="onLoginOptionChange"
      />
      <SignUpForm
        v-if="formStep.state === 'signup'"
        :email="formStep.email"
        @ok="onSignUpSuccess"
      />
      <EmailLoginForm
        v-if="formStep.state == 'signin'"
        :default-email="formStep.email"
        @forgot-password="onForgotPassword"
      />
      <ChangePasswordOtpForm
        v-if="formStep.state === 'changePassword'"
        :otp="formStep.otp"
        :default-email="formStep.email"
        @ok="onChangePasswordSuccess"
      />
    </div>

    <EmailOtpConfirmationModal
      v-if="formStep.state == 'otp'"
      :modal-visible="formStep.state == 'otp'"
      :email="formStep.email"
      @ok="onConfirmOtpSuccess"
      @cancel="onEmailOtpConfirmationModalCancel"
    />
  </dialog>
</template>
<script setup lang="ts">
import type { UnwrapRef } from 'vue';
import { useAuthStore } from '~/store/auth.store';
import { ToastType, useToast } from '~/store/toast';
import IconClearRound from '../icons/IconClearRound.vue';
import ChangePasswordOtpForm, { type IChangePasswordOtpEvent } from './ChangePasswordOtpForm.vue';
import { type IEmailExistCheckEvent } from './EmailExistingCheckForm.vue';
import EmailLoginForm from './EmailLoginForm.vue';
import EmailOtpConfirmationModal, { type EmailOtpConfirmationModalSuccessEvent } from './EmailOtpConfirmationModal.vue';
import LoginOptionForm from './LoginOptionForm.vue';
import SignUpForm, { type ISignUpEvent } from './SignUpForm.vue';
import ArrowBackIosNewRound from '../icons/ArrowBackIosNewRound.vue';

const { openAuthPopup } = useAuthStore();

defineProps({
  show: Boolean,
});

type FormStep = 'start' | 'signup' | 'signin' | 'forgotPassword' | 'otp' | 'changePassword';

interface FormStepState {
  state: FormStep;
  email: string;
  otp: string;
  stateHistories: FormStep[];
}

const formStep: UnwrapRef<FormStepState> = reactive({
  state: 'start',
  email: '',
  otp: '',
  stateHistories: [],
});

const onForgotPassword = (email: string) => {
  formStep.stateHistories.push(formStep.state);
  formStep.email = email;
  formStep.state = 'otp';
  formStep.otp = '';
};

const onLoginOptionChange = (data: IEmailExistCheckEvent) => {
  formStep.email = data.email;
  formStep.stateHistories.push(formStep.state);
  if (data.exist) {
    formStep.state = 'signin';
  } else {
    formStep.state = 'signup';
  }
};

const onConfirmOtpSuccess = (data: EmailOtpConfirmationModalSuccessEvent) => {
  if (!data) {
    return;
  }
  formStep.stateHistories.push(formStep.state);
  if (data.success && data.email && data.otp) {
    formStep.otp = data.otp;
    formStep.state = 'changePassword';
    formStep.email = data.email;
  }
};

const onSignUpSuccess = (data: ISignUpEvent) => {
  formStep.stateHistories.push(formStep.state);
  if (data) {
    formStep.email = data.email;
    formStep.state = 'signin';
  }
};

const onChangePasswordSuccess = (data: IChangePasswordOtpEvent) => {
  formStep.stateHistories.push(formStep.state);
  if (data.success) {
    formStep.state = 'signin';
    formStep.email = data.email;
  }
};

const onEmailOtpConfirmationModalCancel = () => {
  onBack();
};

const onBack = () => {
  if (formStep.stateHistories.length > 0) {
    formStep.state = formStep.stateHistories.pop() || 'start';
  } else {
    formStep.state = 'start';
  }
};

watch(
  () => useAuthStore().signInWithEmailFailed,
  (value) => {
    if (value) {
      const msg = useAuthStore().signInWithEmailMessage || 'Đăng nhập thất bại';
      useToast().showToast(msg, ToastType.Error);
    }
  },
);
</script>
