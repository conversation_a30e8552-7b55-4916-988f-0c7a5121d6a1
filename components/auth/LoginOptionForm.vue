<template>
  <div>
    <div class="text-center">
      <p class="py-4 text-xl">Người tìm việc</p>
      <h3 class="font-semibold text-2xl"><PERSON><PERSON><PERSON> ký hoặc Đăng nhập</h3>
    </div>

    <br />
    <div>
      <ClientOnly>
        <LoginWithGoogleForm />
      </ClientOnly>
      <div class="divider">Hoặc tiếp tục vời email</div>
      <EmailExistingCheckForm
        :email="props.email"
        @ok="onEmailExist"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
import EmailExistingCheckForm, { type IEmailExistCheckEvent } from './EmailExistingCheckForm.vue';
import LoginWithGoogleForm from './LoginWithGoogleForm.vue';

const emits = defineEmits(['change']);
const props = defineProps({
  email: {
    type: String,
    default: '',
  },
});

const onEmailExist = (data: IEmailExistCheckEvent) => {
  emits('change', data);
};
</script>
