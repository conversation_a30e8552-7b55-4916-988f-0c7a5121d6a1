<template>
  <Spin :loading="useAuth.SignUpPending">
    <div class="text-center mb-6">
      <div class="text-gray-700 text-sm font-semibold"><PERSON><PERSON>n tất việc đăng ký với email</div>
      <div class="text-primary font-semibold text-xl my-2">
        {{ props.email }}
      </div>
      <div class="text-gray-700 text-sm font-semibold">để trải nghiệm các tính năng của Vieclamlamdong.site</div>
    </div>
    <FormItem
      label="Họ và tên"
      :required="true"
      :error="errors.fullName"
    >
      <input
        v-model="fullName"
        type="text"
        class="input input-md w-full"
        placeholder="Họ và tên"
      />
    </FormItem>
    <FormItem
      label="Số điện thoại"
      :required="true"
      :error="errors.phoneNumber"
    >
      <input
        v-model="phoneNumber"
        type="text"
        class="input input-md w-full"
        alt="phoneNumber"
        name="phoneNumber"
        placeholder="Số điện thoại"
      />
    </FormItem>
    <FormItem
      label="Mật khẩu"
      :required="true"
      :error="errors.password"
    >
      <input
        v-model="password"
        type="password"
        class="input input-md w-full"
        placeholder="Mật khẩu"
        name="password"
      />
    </FormItem>
    <AuthPolicySignUpCheckBox />
    <br />
    <button
      class="btn btn-primary btn-lg w-full"
      :disabled="useAuth.SignUpPending"
      @click="onSubmit"
    >
      Tiếp tục
    </button>
  </Spin>
</template>
<script setup lang="ts">
import { object, string } from 'yup';
import { toTypedSchema } from '@vee-validate/yup';
import FormItem from '../form/formItem.vue';
import { useAuthStore } from '~/store/auth.store';
import { useToast } from '~/store/toast';
import Spin from '../common/Spin.vue';
import AuthPolicySignUpCheckBox from './AuthPolicySignUpCheckBox.vue';
const props = defineProps({
  email: {
    type: String,
    required: true,
  },
});
const emits = defineEmits(['ok']);
export interface ISignUpEvent {
  email: string;
  password: string;
  phoneNumber: string;
  fullName: string;
  type: string;
}
const useAuth = useAuthStore();
interface FormState {
  password: string;
  phoneNumber: string;
  fullName: string;
}
const { defineField, errors, handleSubmit } = useForm<FormState>({
  validationSchema: toTypedSchema(
    object({
      fullName: string().required('Họ và tên không được để trống'),
      phoneNumber: string()
        .test('is-phone-number', 'Số điện thoại không đúng định dạng', (value) => {
          if (!value) return false;
          return /^\d{10,11}$/.test(value);
        })
        .required('Số điện thoại không được để trống'),
      password: string().required('Mật khẩu không được để trống'),
    }),
  ),
  initialValues: {
    password: '',
    fullName: '',
    phoneNumber: '',
  },
});

const [password] = defineField('password');
const [phoneNumber] = defineField('phoneNumber');
const [fullName] = defineField('fullName');

const onSubmit = handleSubmit(async (values) => {
  useAuth.signUp({
    email: props.email,
    password: values.password,
    phoneNumber: values.phoneNumber,
    fullName: values.fullName,
    type: 'email',
  });
});

watch(
  () => useAuth.SignUpSuccess,
  (value, oldValue) => {
    if (value && value !== oldValue) {
      useToast().success('Đăng ký thành công');
      const event: ISignUpEvent = {
        email: props.email,
        password: password.value,
        phoneNumber: phoneNumber.value,
        fullName: fullName.value,
        type: 'email',
      };
      emits('ok', event);
    }
  },
);

watch(
  () => useAuth.SignUpFailed,
  (value, oldValue) => {
    if (value && value !== oldValue) {
      useToast().error(useAuth.SignUpMessage || 'Đăng ký thất bại');
    }
  },
);
</script>
