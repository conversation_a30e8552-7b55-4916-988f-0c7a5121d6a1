<template>
  <button
    class="btn bg-white btn-lg font-light border-[#e5e5e5] w-full"
    :disabled="isReady === false"
    @click="() => login()"
  >
    <img
      :src="'/icons/google.svg'"
      alt="Google"
    />
    <span class="mx-2"><PERSON><PERSON>ng nhập với Google</span>
  </button>
</template>
<script setup lang="ts">
import {
  useTokenClient,
  useOneTap,
  type AuthCodeFlowSuccessResponse,
  type CredentialResponse,
  type NativeCallbackResponse
} from 'vue3-google-signin';

import { useAuthStore } from '~/store/auth.store';
import { useToast } from '~/store/toast';

const useAuth = useAuthStore();

const handleLoginSuccess = async (response: AuthCodeFlowSuccessResponse) => {
  const accessToken = response.access_token;
  if (accessToken) {
    await useAuth.authenticateUserWithToken(accessToken);
  }
};

const handleLoginError = () => {
  useToast().error('Đăng nhập thất bại');
};

const { isReady, login } = useTokenClient({
  onSuccess: handleLoginSuccess,
  onError: handleLoginError,
  // other options
});

useOneTap({
  onSuccess: (response: CredentialResponse) => {
    console.log('response', response);
  },
  onError: () => {
    console.log('Error with One Tap Login');
  },
});

watch(
  () => useAuth.signInWithEmailFailed,
  (value) => {
    if (value) {
      const msg = useAuth.signInWithEmailMessage || 'Đăng nhập thất bại';
      useToast().error(msg);
    }
  },
);
</script>
