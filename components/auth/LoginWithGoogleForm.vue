<template>
  <button
    class="btn bg-white btn-lg font-light border-[#e5e5e5] w-full"
    :disabled="isReady === false"
    @click="() => login()"
  >
    <img
      :src="'/icons/google.svg'"
      alt="Google"
    />
    <span class="mx-2">Đ<PERSON>ng nhập với Google</span>
  </button>
</template>
<script setup lang="ts">
import {
  useTokenClient,
  type AuthCodeFlowSuccessResponse,
} from 'vue3-google-signin';

import { useAuthStore } from '~/store/auth.store';
import { useToast } from '~/store/toast';

const useAuth = useAuthStore();

const handleLoginSuccess = async (response: AuthCodeFlowSuccessResponse) => {
  const accessToken = response.access_token;
  if (accessToken) {
    await useAuth.authenticateUserWithToken(accessToken);
  }
};

const handleLoginError = () => {
  useToast().error('<PERSON>ăng nhập thất bại');
};

const { isReady, login } = useTokenClient({
  onSuccess: handleLoginSuccess,
  onError: handleLoginError,
  // other options
});

// Note: Google One Tap is initialized globally via the googleOneTap.client.ts plugin
// This component only handles the manual Google Sign-In button

watch(
  () => useAuth.signInWithEmailFailed,
  (value) => {
    if (value) {
      const msg = useAuth.signInWithEmailMessage || 'Đăng nhập thất bại';
      useToast().error(msg);
    }
  },
);
</script>
