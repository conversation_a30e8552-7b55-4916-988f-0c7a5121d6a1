<template>
  <div class="flex justify-center items-center space-x-6 my-4">
    <input
      v-for="(otpInput, idx) of Array.from({
        length: 4,
      })"
      :id="'email_otp_input_' + idx"
      :key="'email_otp_input_' + idx"
      type="text"
      :value="emailOtp[idx]"
      :class="[
        'input input-bordered text-3xl font-bold text-center focus-within:input-info input-md !w-12 !h-12 lg:input-xl lg:w-14',
      ]"
      @input="(e) => onOtpChange(e, idx)"
      @keypress="isOtpNumber($event)"
      @keyup="(e) => onkeydown(e, idx)"
    />
  </div>
</template>
<script setup lang="ts">
import { nextTick } from 'vue';
const emailOtp = ref<(number | undefined)[]>([]);
const model = defineModel('value', { type: String });
const emits = defineEmits(['ok']);

const MAX_OTP_LENGTH = 4;

const onOtpChange = (e: Event, idx: number) => {
  const value = (e.target as HTMLInputElement).value;
  const parts = value.replaceAll(' ', '').split('');
  console.log(`otp input raw value: `, value);
  if (parts.length === 0) {
    emailOtp.value[idx] = undefined;
    return;
  }
  console.log(`otp input parts: `, parts);
  parts.forEach((part, index) => {
    if (index + 1 > MAX_OTP_LENGTH) {
      return;
    }
    const otpNumber = parseInt(part);
    if (isNaN(otpNumber)) {
      emailOtp.value[idx] = undefined;
      return;
    }
    if (idx > MAX_OTP_LENGTH) {
      return;
    }
    emailOtp.value[idx++] = otpNumber;
    focusOtpInput(idx);
  });
  focusOtpInput(emailOtp.value.length);
  model.value = emailOtp.value.join('');
  if (emailOtp.value.join('').length === MAX_OTP_LENGTH) {
    emits('ok', emailOtp.value.join(''));
  }
};

const focusOtpInput = (index: number) => {
  const input = document.querySelector(`#email_otp_input_${index}`) as HTMLInputElement;
  if (input) {
    input.focus();
  }
};

const isOtpNumber = (e: KeyboardEvent) => {
  const isNumber = /^[0-9]*$/.test(e.key);
  if (!isNumber || (e.target as HTMLInputElement).value.length >= 1) {
    e.preventDefault();
  }
};

const onkeydown = (e: KeyboardEvent, idx: number) => {
  const isBackspace = e.key === 'Backspace';
  if (!isBackspace) {
    return;
  }
  if (emailOtp.value[idx] === undefined || emailOtp.value[idx] === null) {
    focusOtpInput(idx - 1);
    return;
  }
};

const focusAtFirstTime = () => {
  // setTimeout(() => {
  nextTick(() => {
    const input = document.querySelector('#email_otp_input_0') as HTMLInputElement;
    if (input) {
      input.focus();
    }
  });
  // }, 1000);
};

onMounted(() => {
  // console.log('mounted');
  focusAtFirstTime();
});

watch(
  () => emailOtp.value,
  (value) => {
    model.value = value.join('');
  },
);
</script>
