<template>
  <div>
    <ProfileHeader :name="profile?.fullName" />
    <div class="md:px-6">
      <Collapse
        title="Vi<PERSON><PERSON> làm đã lưu"
        :show-adding="false"
        :loading="getPagedListPending"
      >
        <template v-if="pagedList.length">
          <div
            v-for="item of pagedList"
            :key="item.recruitmentId"
          >
            <JobBrowserItem
              v-if="item.recruitment"
              :job="item.recruitment"
              :favorite="true"
              @favorite="onFavorite"
            />
          </div>
          <Pagination
            :total="total"
            :page-size="limit"
            :current-page="page"
            @change="onPageChange"
          />
        </template>
        <Empty v-else />
      </Collapse>
    </div>
  </div>
</template>
<script setup lang="ts">
import ProfileHeader from '~/components/account/ProfileHeader.vue';
import JobBrowserItem from '~/components/browsers/JobBrowserItem.vue';
import Collapse from '~/components/common/collapse.vue';
import Pagination from '~/components/common/pagination.vue';
import Empty from '~/components/common/Empty.vue';

import { useProfileStore } from '~/store/profile';
import { recruitmentFavoriteStore } from '~/store/recruitmentFavorite';

const { getPagedList, removeFavoriteRecruitment } = recruitmentFavoriteStore();

const page = ref(1);
const limit = ref(10);

const { profile } = storeToRefs(useProfileStore());
const { pagedList, total, getPagedListPending } = storeToRefs(recruitmentFavoriteStore());
definePageMeta({
  layout: 'profile',
  middleware: 'auth',
});

const onPageChange = (pageSize: number) => {
  console.log(pageSize);
  if (pageSize < 1) {
    pageSize = 1;
  }
  page.value = pageSize;
  getPagedList(limit.value, page.value);
};

const onFavorite = ({ favorite, resumeId }: { favorite: boolean; resumeId: number }) => {
  if (!favorite) {
    removeFavoriteRecruitment(resumeId);
  }
};

onMounted(() => {
  getPagedList(limit.value, page.value);
});
</script>
