<template>
  <div>
    <Carousel />
    <SearchStats
      :occupations="useCommon?.occupations"
      :occupations-stats="useRecruitment?.occupationStatics || []"
      :provinces="useCommon?.provinces"
    />
    <Spin :loading="useRecruitment.getNewestListAtHomePending">
      <UrgentJobs
        title="Việc làm mới nhất"
        :jobs="useRecruitment?.newestList || []"
        :provinces="useCommon?.provinces"
        :districts="useCommon?.districts"
      />
      <div class="flex justify-center">
        <Pagination
          :current-page="newsJobsPage"
          :total="useRecruitment?.newestTotal || 0"
          :page-size="12"
          v-on:change="onNewsJobsPageChange"
        />
      </div>
    </Spin>

    <TopOccupationList
      :occupations="useCommon?.occupations"
      :occupations-stats="useRecruitment?.occupationStatics || []"
    />

    <UrgentJobs
      title="Việc làm gấp"
      :jobs="useRecruitment?.urgentList || []"
      :provinces="useCommon?.provinces"
      :districts="useCommon?.districts"
    />
    <JobCategorySession />
  </div>
</template>
<script setup lang="ts">
import Carousel from '~/components/common/carousel.vue';
import JobCategorySession from '~/components/home/<USER>';
import SearchStats from '~/components/home/<USER>';
import TopOccupationList from '~/components/home/<USER>';
import UrgentJobs from '~/components/home/<USER>';
import Pagination from '~/components/common/pagination.vue';
import Spin from '~/components/common/Spin.vue';

import { useRecruitmentStore } from '@/store/recruitment.store';
import { defaultSeo } from '~/constants/seo.constants';
import { useAuthStore } from '~/store/auth.store';
import { useCommonStore } from '~/store/common.store';
import { recruitmentFavoriteStore } from '~/store/recruitmentFavorite';
const { getFavoriteRecruitmentIds } = recruitmentFavoriteStore();
const useRecruitment = useRecruitmentStore();
const useCommon = useCommonStore();
const newsJobsPage = ref(1);

console.log('home page: useSeoMeta');
useSeoMeta(defaultSeo);

await useAsyncData('homePage', () => {
  return Promise.all([
    useCommon.getCommonData(),
    useRecruitment.getUrgentListAtHome(),
    useRecruitment.getNewestListAtHome(newsJobsPage.value),
    useRecruitment.getOccupationStatics(),
  ]);
});

const onNewsJobsPageChange = (page: number) => {
  newsJobsPage.value = page;
  useRecruitment.getNewestListAtHome(page);
};

const onInit = () => {
  if (useAuthStore().authenticated) {
    const ids = useRecruitment?.items?.map((item) => item.id) || [];
    getFavoriteRecruitmentIds(ids);
  }
};

onMounted(() => {
  onInit();
});
</script>
