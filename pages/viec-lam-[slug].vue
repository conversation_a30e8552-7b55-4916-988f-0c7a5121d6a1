<template>
  <RecruitmentBrowserPage
    :page-type="pageType"
    :occupation-id="occupationId"
    :province-id="provinceId"
    :district-id="districtId"
  />
</template>
<script setup lang="ts">
import RecruitmentBrowserPage, { type PageType } from '~/components/browsers/RecruitmentBrowserPage.vue';
definePageMeta({
  layout: 'default',
});

const route = useRoute();
const slugRaw = route.params.slug;
const slug = Array.isArray(slugRaw) ? slugRaw[0] : slugRaw;
const occupationId = ref<number | null | undefined>(undefined);
const provinceId = ref<number | null | undefined>(undefined);
const districtId = ref<number | null | undefined>(undefined);
const pageType = ref<PageType | undefined>(undefined);
const rawId = slug.split('-').pop();
// slug-o123: occupation
const subfix = rawId?.split('-').pop();
const isOccupationPage = subfix?.includes('o');
if (isOccupationPage) {
  const occupationRaw = subfix?.split('o').pop();
  if (occupationRaw) {
    occupationId.value = parseInt(occupationRaw);
  }
  pageType.value = 'occupation';
} else {
  const provinceDistrictRaw = rawId?.split('-').pop();
  const haveDistrict = provinceDistrictRaw?.includes('d');
  pageType.value = 'province';
  if (haveDistrict) {
    // slug-p123d22: province-district
    const districtRaw = provinceDistrictRaw?.split('d').pop();
    const provinceRaw = provinceDistrictRaw?.split('p').pop()?.split('d').shift();
    if (provinceRaw) {
      provinceId.value = parseInt(provinceRaw);
    }
    if (districtRaw) {
      districtId.value = parseInt(districtRaw);
    }
  } else {
    // slug-p123: province
    const provinceRaw = provinceDistrictRaw?.split('p').pop();
    if (provinceRaw) {
      provinceId.value = parseInt(provinceRaw);
    }
  }
}
</script>
