<template>
  <Container class="mb-16">
    <template v-if="useRecruitment.details">
      <BreadCrumb
        :routes="[
          {
            path: '/',
            breadcrumbName: 'Trang chủ',
          },
          {
            path: '/tim-kiem-viec-lam',
            breadcrumbName: 'Tì<PERSON> kiếm việc làm',
          },
          {
            path: '/cong-viec/' + useRecruitment.details?.slug + 'id' + useRecruitment.details?.id,
            breadcrumbName: useRecruitment.details?.title || '',
          },
        ]"
      />
      <DesktopJobHeader
        v-if="useRecruitment.details && isDesktop"
        :job="useRecruitment.details"
      />
      <MobileJobHeader
        v-if="useRecruitment.details && !isDesktop"
        :job="useRecruitment.details"
      />
      <br />
      <DesktopJobBody
        v-if="useRecruitment.details"
        :job="useRecruitment.details"
      />

      <CompanyCard
        v-if="useRecruitment.details?.company"
        :company="useRecruitment.details?.company"
      />
      <ApplyJobButtonCardFixed
        v-if="useRecruitment.details"
        :recruitment="useRecruitment.details"
        :is-display="!isDesktop"
      />
    </template>
    <JobEmpty v-else />
  </Container>
</template>
<script setup lang="ts">
import { useRoute } from 'vue-router';
import ApplyJobButtonCardFixed from '~/components/job/ApplyJobButtonCardFixed.vue';
import CompanyCard from '~/components/job/CompanyCard.vue';
import DesktopJobBody from '~/components/job/DesktopJobBody.vue';
import DesktopJobHeader from '~/components/job/DesktopJobHeader.vue';
import JobEmpty from '~/components/job/EmptyJob.vue';
import MobileJobHeader from '~/components/job/MobileJobHeader.vue';
import BreadCrumb from '~/components/layouts/BreadCrumb.vue';
import Container from '~/components/layouts/container.vue';
import { defaultSeo } from '~/constants/seo.constants';

import { useAuthStore } from '~/store/auth.store';
import { useCommonStore } from '~/store/common.store';
import type { HttpResponse } from '~/store/httpRequest.store';
import { useRecruitmentStore } from '~/store/recruitment.store';
import { recruitmentFavoriteStore } from '~/store/recruitmentFavorite';
import type { Recruitment } from '~/types';

definePageMeta({
  layout: 'default',
});
const { getFavoriteRecruitmentIds, isFavorite, addFavoriteRecruitment, removeFavoriteRecruitment } =
  recruitmentFavoriteStore();
const { findEmployeeSizeById } = useCommonStore();
const useAuth = useAuthStore();
const useRecruitment = useRecruitmentStore();
const id = ref<number | null>(null);
const parts = useRoute().path.split('id');
if (parts.length > 1) {
  id.value = parseInt(parts[parts.length - 1]);
}
const url = useRuntimeConfig().public.apiUrl + `/v1/recruitment/get-detail/${id.value}`;
const { data: useFetchData } = await useFetch<HttpResponse<Recruitment>>(url, {});

const httpResponse = useFetchData.value;

// const httpResponse = await $fetch<HttpResponse<Recruitment>>(url);

if (httpResponse && httpResponse.success && httpResponse.data) {
  useRecruitment.details = httpResponse.data;
}

const recruitmentDetails = httpResponse?.data;
const seoDescription = [
  recruitmentDetails?.title,
  'đăng tin tuyên dụng ' + formatDate(recruitmentDetails?.publishedAt),
  'tại ' + recruitmentDetails?.company?.name,
  'lương ' +
    (recruitmentDetails?.salaryNegotiable
      ? 'thỏa thuận'
      : recruitmentDetails?.minSalary + ' - ' + recruitmentDetails?.maxSalary + ' triệu'),
  'ở ' +
    recruitmentDetails?.workspaces
      ?.map((wp) => {
        return [wp.district?.name, wp.province?.name].join(', ');
      })
      .join(' - '),
  'trong ngành ' + recruitmentDetails?.occupations?.map((occ) => occ.name).join(', '),
  '- ' + recruitmentDetails?.id,
].join(' ');
useServerSeoMeta(
  Object.assign(
    { ...defaultSeo },
    {
      title: [recruitmentDetails?.title || undefined, 'vieclamlamdong'].join('|'),
      description: seoDescription,
      ogDescription: seoDescription,
      keywords: recruitmentDetails?.title || undefined,
      ogImageAlt: recruitmentDetails?.title || undefined,
      ogImageUrl: recruitmentDetails?.logo || recruitmentDetails?.company?.logo || undefined,
    },
  ),
);

const { isDesktop } = useDevice();

const occupationForBreadcrumb = computed(() => {
  // get occupation breadcrumb, get first occupation in list
  return useRecruitment.details?.occupations?.[0];
});

const fetchFavoriteStatusForUser = async () => {
  if (id.value && useAuth.authenticated) {
    getFavoriteRecruitmentIds([id.value]);
  }
};

const onGetDetails = async () => {
  if (useRecruitment.details?.id !== id.value && id.value) {
    await useRecruitment.getDetails(id.value);
  }
};

function openLoginModalIfNotAuthenticated() {
  if (!useAuth.authenticated) {
    useAuth.openAuthPopup(true);
    return true;
  }
  return false;
}

function onFavorite(id: number) {
  if (openLoginModalIfNotAuthenticated()) {
    return;
  }
  if (!isFavorite(id)) {
    addFavoriteRecruitment(id);
  } else {
    removeFavoriteRecruitment(id);
  }
}

const onInit = async () => {
  fetchFavoriteStatusForUser();
  // onGetDetails();
};

onMounted(() => {
  onInit();
});
</script>
