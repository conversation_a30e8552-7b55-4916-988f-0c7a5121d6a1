<template>
  <Container>
    <BreadCrumb
      :routes="[
        {
          path: '/',
          breadcrumbName: 'Trang chủ',
        },
        {
          path: '',
          breadcrumbName: 'Vi<PERSON><PERSON> làm',
        },
      ]"
    />
    <h1 class="text-3xl text-primary">Vi<PERSON><PERSON> làm theo ngành nghề</h1>
    <Divider />
    <br />
    <div class="grid grid-cols-4 gap-4">
      <NuxtLink
        v-for="occupation in occupations"
        :key="occupation.id"
        :to="{
          path: `/viec-lam-${occupation.code}-o${occupation.id}`,
        }"
        target="_blank"
        class="block text-sm my-1 font-normal"
      >
        {{ occupation.name }}
      </NuxtLink>
    </div>
    <br />
  </Container>
</template>
<script setup lang="ts">
import BreadCrumb from '~/components/layouts/BreadCrumb.vue';
import Container from '~/components/layouts/container.vue';
import Divider from '~/components/common/Divider.vue';
import { useCommonStore } from '~/store/common.store';
definePageMeta({
  layout: 'default',
});

const { occupations } = storeToRefs(useCommonStore());
</script>
