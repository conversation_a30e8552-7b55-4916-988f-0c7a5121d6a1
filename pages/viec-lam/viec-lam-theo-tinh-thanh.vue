<template>
  <Container>
    <BreadCrumb
      :routes="[
        {
          path: '/',
          breadcrumbName: 'Trang chủ',
        },
        {
          path: '',
          breadcrumbName: 'Việc làm',
        },
      ]"
    />
    <h1 class="text-3xl text-primary">Vi<PERSON><PERSON> làm theo ngành nghề</h1>
    <Divider />
    <br />
    <div class="grid grid-cols-4 gap-4">
      <NuxtLink
        v-for="province in provinces"
        :key="'province_' + province.id"
        :to="{
          path: `/viec-lam-${generateSlug(province.name)}-p${province.id}`,
        }"
        target="_blank"
        class="block text-sm my-1 font-normal"
      >
        {{ province.name }}
      </NuxtLink>
      <NuxtLink
        v-for="district in districts"
        :key="'district_' + district.id"
        :to="{
          path: `/viec-lam-${generateSlug(district.name)}-p${district.parentId}d${district.id}`,
        }"
        target="_blank"
        class="block text-sm my-1 font-normal"
      >
        {{ district.name }}
      </NuxtLink>
    </div>
    <br />
  </Container>
</template>
<script setup lang="ts">
import BreadCrumb from '~/components/layouts/BreadCrumb.vue';
import Container from '~/components/layouts/container.vue';
import { useCommonStore } from '~/store/common.store';
import { removeUnicode } from '~/utils/common';
import { generateSlug } from '~/utils/slug-generator.util';
const { provinces, districts } = storeToRefs(useCommonStore());
definePageMeta({
  layout: 'default',
});
</script>
