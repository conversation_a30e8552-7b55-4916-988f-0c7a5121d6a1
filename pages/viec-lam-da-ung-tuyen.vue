<template>
  <div>
    <ProfileHeader :name="profile?.fullName" />
    <div class="md:px-6">
      <Collapse
        title="Vi<PERSON><PERSON> làm đã ứng tuyển"
        :show-adding="false"
        :loading="useAppliedRecruitment.getPagedListPending"
      >
        <template v-if="pagedList.length">
          <AppliedRecruitmentItem
            v-for="item of pagedList"
            :key="item.recruitmentId"
            :applied-job="item"
          />
          <Pagination
            :total="total"
            :page-size="limit"
            :current-page="page"
            @change="onPageChange"
          />
        </template>
        <Empty v-else />
      </Collapse>
    </div>
  </div>
</template>
<script setup lang="ts">
import ProfileHeader from '~/components/account/ProfileHeader.vue';
import Collapse from '~/components/common/collapse.vue';
import Pagination from '~/components/common/pagination.vue';
import Empty from '~/components/common/Empty.vue';
import { useProfileStore } from '~/store/profile';
import AppliedRecruitmentItem from '~/components/appliedRecruitments/AppliedRecruitmentItem.vue';
import { appliedRecruitmentStore } from '~/store/recruitmentApply.js';

const useAppliedRecruitment = appliedRecruitmentStore();

const { profile } = storeToRefs(useProfileStore());
definePageMeta({
  layout: 'profile',
});

const page = ref(1);
const limit = ref(10);
const { pagedList, total } = storeToRefs(appliedRecruitmentStore());

const onPageChange = (pageSize: number) => {
  if (pageSize < 1) {
    pageSize = 1;
  }
  page.value = pageSize;
  useAppliedRecruitment.getList(limit.value, page.value);
};

onMounted(() => {
  useAppliedRecruitment.getList(limit.value, page.value);
});
</script>
