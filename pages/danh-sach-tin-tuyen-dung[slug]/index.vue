<template>
  <div class="mb-14">
    <div
      v-if="useCompany.company"
      class="bg-white"
    >
      <Container>
        <div class="rounded-b-xl overflow-hidden">
          <img
            class="w-full object-cover h-60"
            :src="useCompany.company.bgImage || 'https://cdn1.Vieclamlamdong.site/images/assets/img/generic_19.jpg'"
            :alt="useCompany.company?.slug || ''"
          />
        </div>
        <div class="lg:px-28 flex flex-col lg:justify-start lg:flex-row">
          <div class="text-center lg:mr-6 lg:text-left">
            <div class="avatar bottom-4 bg-white">
              <div class="w-32 rounded-md ring ring-white ring-offset-base-100 ring-offset-2 shadow-2xl">
                <img
                  alt="vietlamlamdong.site"
                  :src="useCompany.company.logo || undefined"
                />
              </div>
            </div>
          </div>
          <div class="py-6 w-full">
            <div class="block lg:flex lg:justify-between lg:items-center">
              <div>
                <h1 class="font-semibold text-2xl mb-4 text-center lg:text-left">
                  {{ useCompany.company.name }}
                </h1>
                <div class="flex flex-col justify-center lg:justify-start lg:flex-row">
                  <div class="flex my-w items-center justify-center lg:min-w-52 lg:justify-start">
                    <IconLocation class="w-4 h-4 text-info mr-1" />
                    <span class="font-light text-sm text-gray-600">{{
                      findProvinceById(useCompany.company.provinceId)?.name
                    }}</span>
                  </div>
                  <div class="flex my-2 justify-center items-center lg:min-w-52 lg:justify-start">
                    <IconWeb class="w-4 h-4 text-info mr-1" />
                    <NuxtLink
                      class="font-light text-sm text-gray-600"
                      target="_blank"
                      :href="useCompany.company.website || undefined"
                      >Trang web công ty</NuxtLink
                    >
                  </div>
                </div>
              </div>
              <!-- <div class="text-right flex justify-start px-8 gap-2 lg:flex-col lg:px-0">
                <button class="btn btn-primary text-nowrap text-white text-sm px-6 w-1/2 lg:w-auto">
                  <IconEye class="w-5 h-5" />
                  Theo dõi
                  <span class="font-semibold">•</span>
                  {{ useCompany.company.totalFollowers }}
                </button>
                <button
                  href="javascript:void(0)"
                  class="btn !bg-primary/10 text-primary font-light text-sm px-6 w-1/2 lg:w-auto"
                >
                  <IconShare class="w-4 h-4" />
                  Chia sẻ
                </button>
              </div> -->
            </div>
          </div>
        </div>
      </Container>
    </div>
    <br />
    <Container class="bg-base-100">
      <div class="p-2 lg:px-28">
        <h1 class="font-bold text-2xl my-6">Vị trí tuyển dụng</h1>
        <div class="border-secondary/20 border rounded-sm">
          <template v-if="useCompany.company?.recruitmentItems?.length">
            <RecruitmentLink
              v-for="job in useCompany.company?.recruitmentItems"
              :id="job.id"
              :key="job.id"
              :slug="job.slug"
              class="p-4 border-b border-b-secondary/20 flex justify-between hover:bg-base-100 hover:cursor-pointer"
            >
              <div class="w-full">
                <div class="text-gray-600 mb-2">{{ job.title }}</div>
                <div class="flex justify-between">
                  <div class="flex justify-start items-center text-nowrap text-xs lg:text-sm">
                    <IconMoney class="w-4 h-4 text-gray-400 mr-1" />
                    <span class="text-gray-400 font-light mx-1 hidden lg:block">Lương:</span>
                    <span class="font-light">{{ findSalaryById(job.jobSalaryId)?.name }}</span>
                  </div>
                  <div
                    v-if="job.workspaces?.length"
                    class="flex justify-start items-center text-nowrap text-xs lg:text-sm"
                  >
                    <IconLocation class="w-4 h-4 text-gray-400 mr-1" />
                    <span class="text-gray-400 font-light mx-1 hidden lg:block">Địa điểm:</span>
                    <span class="font-light">{{ findProvinceById(job.workspaces[0].provinceId)?.name }}</span>
                  </div>
                  <div class="flex justify-start items-center text-nowrap text-xs lg:text-sm">
                    <IconTimerSand class="w-4 h-4 text-gray-400 mr-1" />
                    <span class="text-gray-400 font-light mr-1 hidden lg:block">Hạn nộp:</span>
                    <span class="font-light">{{ formatDateOnly(job.applyExpiredAt) }}</span>
                  </div>
                </div>
              </div>
              <div class="max-w-24 w-full text-right">
                <a
                  v-if="isFavorite(job.id)"
                  class="inline-flex"
                  href="javascript:void(0)"
                  @click.prevent="onFavorite(job.id, false)"
                >
                  <IconHeartFil
                    v-if="isFavorite(job.id)"
                    class="w-6 h-6 text-info"
                  />
                </a>
                <a
                  v-else
                  class="inline-flex"
                  href="javascript:void(0)"
                  @click.prevent="onFavorite(job.id, true)"
                >
                  <IconHeartOutline class="w-6 h-6 text-info" />
                </a>
              </div>
            </RecruitmentLink>
          </template>
          <Empty
            v-else
            description="Chưa có vị trí tuyển dụng nào"
          />
        </div>
      </div>
      <br />
      <div class="p-2 lg:px-28">
        <h1 class="font-bold text-2xl my-6">Giới thiệu doanh nghiệp</h1>
        <p
          class="font-light"
          v-html="useCompany.company?.overview"
        />
      </div>
      <br />
      <div class="p-2 lg:px-28">
        <h1 class="font-bold text-2xl my-6">Liên hệ</h1>
        <div class="flex flex-col lg:justify-start lg:flex-row">
          <div class="px-6 w-full lg:w-2/5 lg:pl-0">
            <iframe
              width="100%"
              height="240"
              style="border: 0"
              loading="lazy"
              allowfullscreen
              referrerpolicy="no-referrer-when-downgrade"
              :src="`https://www.google.com/maps/embed/v1/place?key=AIzaSyAzzdmVQBufeKFfrSfZKTCjDdZ3rw3RTLY&q=${useCompany.company?.address}`"
            />
          </div>
          <div class="w-full lg:w-3/5">
            <div class="flex justify-start items-center font-light my-4">
              <IconLocation class="w-6 h-6 text-gray-500" />
              <span class="text-gray-500 text-nowrap mr-1">Địa chỉ:</span>
              <span>{{ useCompany.company?.address }}</span>
            </div>
            <div
              v-if="useCompany.company?.employeeSizeId"
              class="flex justify-start items-center font-light my-4"
            >
              <IconPeopleGroup class="w-6 h-6 text-gray-500" />
              <span class="text-gray-500 text-nowrap mr-1">Quy mô:</span>
              <span>{{ findEmployeeSizeById(useCompany.company?.employeeSizeId)?.name }} Người</span>
            </div>
            <div
              v-if="useCompany.company?.occupations?.length"
              class="flex justify-start items-center font-light my-4"
            >
              <IconPeopleGroup class="w-6 h-6 text-gray-500" />
              <span class="text-gray-500 text-nowrap mr-1">Lĩnh vực:</span>
              <!-- <span>{{ findByOccupationId(useCompany.company?)?.name }}</span> -->
            </div>
          </div>
        </div>
      </div>
    </Container>
  </div>
</template>
<script setup lang="ts">
import { useRoute } from 'vue-router';
import IconHeartFil from '~/components/icons/IconHeartFil.vue';
import IconHeartOutline from '~/components/icons/IconHeartOutline.vue';
import IconLocation from '~/components/icons/IconLocation.vue';
import IconMoney from '~/components/icons/IconMoney.vue';
import IconPeopleGroup from '~/components/icons/IconTabler.vue';
import IconTimerSand from '~/components/icons/IconTimerSand.vue';
import IconWeb from '~/components/icons/IconWeb.vue';
import Container from '~/components/layouts/container.vue';
import RecruitmentLink from '~/components/link/recruitmentLink.vue';
import { formatDateOnly } from '~/utils/dateFormatter.utils';
import Empty from '~/components/common/Empty.vue';

import { defaultSeo } from '~/constants/seo.constants';
import { useAuthStore } from '~/store/auth.store';
import { useCommonStore } from '~/store/common.store';
import { useCompanyStore } from '~/store/company.store';
import type { HttpResponse } from '~/store/httpRequest.store';
import { recruitmentFavoriteStore } from '~/store/recruitmentFavorite';
import type { Company } from '~/types';

const { openAuthPopup } = useAuthStore();
const { isFavorite, addFavoriteRecruitment, removeFavoriteRecruitment, getFavoriteRecruitmentIds } =
  recruitmentFavoriteStore();

const { authenticated } = storeToRefs(useAuthStore());
const { findSalaryById, findEmployeeSizeById, findByOccupationId, findProvinceById } = useCommonStore();
// const { company } = storeToRefs(useCompanyStore());

const useCompany = useCompanyStore();

const companyId = useRoute().path.split('-').pop();
const url = useRuntimeConfig().public.apiUrl + `/v1/companies/detail/${companyId}`;

// const response = await $fetch<HttpResponse<Company>>(url);
const { data: useFetchData } = await useFetch<HttpResponse<Company>>(url);
const response = useFetchData.value;
if (response && response.success && response.data) {
  useCompany.company = response.data;
}
useServerSeoMeta(
  Object.assign(
    { ...defaultSeo },
    {
      title: response?.data?.name,
      description: response?.data?.overview,
      ogImage: response?.data?.logo,
      ogTitle: response?.data?.name,
      ogDescription: response?.data?.overview,
      ogSiteName: 'vieclamLamDong.site',
      ogLocale: 'vi_VN',
      ogImageWidth: 1200,
      ogImageHeight: 630,
      ogImageAlt: response?.data?.name,
      ogImageSecureUrl: response?.data?.logo,
    },
  ),
);

// const companyRaw = useRoute().path.split('-').pop();

const onFavorite = (jobId: number, favorite: boolean) => {
  if (!authenticated.value) {
    openAuthPopup(true);
    return;
  }
  if (favorite) {
    addFavoriteRecruitment(jobId);
  } else {
    removeFavoriteRecruitment(jobId);
  }
};

const parseIdFromPathAndFetchCompany = () => {
  const parts = useRoute().path.split('-');
  const companyId = +parts[parts.length - 1];
  if (!useCompany.company?.id || useCompany.company?.id !== companyId) {
    useCompany.getCompany(companyId);
  }
};

const onInit = () => {
  // check if company is fetched
  parseIdFromPathAndFetchCompany();
};
onMounted(() => {
  // getFavoriteRecruitmentIds();
  // parseIdFromPathAndFetchCompany();
  onInit();
});

watch(
  () => useCompanyStore().getDetailsSuccess,
  (newValue: boolean, oldValue: boolean | undefined) => {
    if (newValue === true && oldValue === false) {
      if (useCompany.company?.recruitmentItems?.length && authenticated.value) {
        getFavoriteRecruitmentIds(useCompany.company.recruitmentItems.map((item) => item.id));
      }
    }
  },
  {
    immediate: true,
    deep: true,
  },
);
</script>
