<template>
  <div class="container mx-auto p-8">
    <h1 class="text-3xl font-bold mb-6">Google One Tap Test Page</h1>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
      <!-- Authentication Status -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <h2 class="card-title">Authentication Status</h2>
          <div class="space-y-2">
            <p><strong>Authenticated:</strong> {{ authStore.authenticated ? 'Yes' : 'No' }}</p>
            <p><strong>User:</strong> {{ authStore.profile?.fullName || 'Not logged in' }}</p>
            <p><strong>Email:</strong> {{ authStore.profile?.emailAddress || 'N/A' }}</p>
          </div>
          
          <div class="card-actions justify-end mt-4">
            <button 
              v-if="authStore.authenticated" 
              class="btn btn-error"
              @click="logout"
            >
              Logout
            </button>
            <button 
              v-else 
              class="btn btn-primary"
              @click="manualInitOneTap"
            >
              Manually Trigger One Tap
            </button>
          </div>
        </div>
      </div>

      <!-- One Tap Status -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <h2 class="card-title">One Tap Status</h2>
          <div class="space-y-2">
            <p><strong>One Tap Available:</strong> {{ isOneTapAvailable() ? 'Yes' : 'No' }}</p>
            <p><strong>Google API Loaded:</strong> {{ isGoogleApiLoaded ? 'Yes' : 'No' }}</p>
            <p><strong>Window Object:</strong> {{ typeof window !== 'undefined' ? 'Available' : 'Not Available' }}</p>
          </div>
          
          <div class="alert alert-info mt-4">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span>One Tap will automatically appear when you're not authenticated and have a Google account.</span>
          </div>
        </div>
      </div>

      <!-- Manual Login -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <h2 class="card-title">Manual Google Login</h2>
          <p>Use the traditional Google Sign-In button:</p>
          <div class="card-actions justify-end mt-4">
            <LoginWithGoogleForm />
          </div>
        </div>
      </div>

      <!-- Instructions -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <h2 class="card-title">How to Test One Tap</h2>
          <ol class="list-decimal list-inside space-y-2">
            <li>Make sure you're logged out</li>
            <li>Refresh the page</li>
            <li>One Tap should appear automatically after 1 second</li>
            <li>If you dismiss it, you can manually trigger it with the button above</li>
            <li>Try logging in and out to see the behavior</li>
          </ol>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from '~/store/auth.store';
import LoginWithGoogleForm from '~/components/auth/LoginWithGoogleForm.vue';

const authStore = useAuthStore();
const { initializeOneTap, isOneTapAvailable } = useGoogleOneTapAuth();

const isGoogleApiLoaded = computed(() => {
  return typeof window !== 'undefined' && !!window.google;
});

const logout = () => {
  authStore.logUserOut();
};

const manualInitOneTap = () => {
  initializeOneTap({
    autoSelect: true,
    cancelOnTapOutside: false,
  });
};

// Set page title
useHead({
  title: 'Google One Tap Test - Việc làm Lâm Đồng'
});
</script>
