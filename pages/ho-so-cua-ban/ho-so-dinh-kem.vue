<template>
  <div class="relative h-full lg:px-6">
    <div class="pt-2 pb-36">
      <h1 class="font-bold text-2xl mb-4">
        {{ pageTitle }}
      </h1>
      <ResumeCompletedAlert :completed="tinyintToBoolean(resume?.completed)" />
      <Collapse
        title="Tải CV đính kèm"
        :border="true"
        :border-color="'border-error/80'"
        badge-text="Yêu cầu"
        badge-color="badge-error"
        :required="true"
        :valid="isUpdate && tinyintToBoolean(resume?.cvFileCompleted)"
        :show-adding="false"
        :loading="updatePending || createPending"
      >
        <div>
          <div
            v-if="cv"
            class="mb-4"
          >
            <NuxtLink
              :to="cv.url"
              target="_blank"
              class="flex justify-between border border-base-200 rounded-sm p-2"
            >
              <div class="flex justify-start items-center">
                <div>
                  <img
                    :src="'/icons/pdf_file.svg'"
                    class="w-8 h-8"
                  />
                </div>
                <div class="text-sm font-light ml-2">{{ cv.name }}</div>
              </div>
              <div>
                <a
                  href="javascript:void(0)"
                  class="btn btn-soft btn-circle btn-primary !rounded-full !p-1 !w-8 !h-8"
                  @click="cv = null"
                >
                  <IconTrash class="w-4 h-4" />
                </a>
              </div>
            </NuxtLink>
          </div>
          <button
            class="btn btn-soft btn-primary max-w-40 w-full"
            @click="onOpenFileModal"
          >
            <IconUpload class="w-6 h-6" />
            <span>Tải file</span>
            <input
              id="cv_upload_input"
              type="file"
              accept=".pdf"
              class="hidden"
            />
          </button>
          <p class="text-gray-500 text-xs my-2 font-light">
            Định dạng file .doc, .docx, .pdf dung lượng {{ '<=' }} 2 MB
          </p>
        </div>

        <div
          v-if="cv?.url && cv.url !== resume?.cvFileUrl"
          class="flex items-center justify-end"
        >
          <button
            class="btn btn-primary"
            @click="onUpdateCvUrl"
          >
            Lưu thông tin
          </button>
        </div>
      </Collapse>
      <AccountProfile />

      <GeneralInformationSection
        :form-open-at-start="!isUpdate"
        :resume="isUpdate ? resume : null"
        :type="ResumeType.offline"
        :required="true"
        :valid="isUpdate && tinyintToBoolean(resume?.generalInfoCompleted)"
      />

      <ResumeFormFooter
        @on-preview="onPreview"
        @on-request-approval="onPublishResume"
      />

      <dialog
        id="modal_resume_review"
        class="modal modal-bottom sm:modal-middle"
      >
        <div class="modal-box modal-bottom md:w-11/12 md:max-w-4xl md:max-h-[calc(100vh-4rem)]">
          <PreviewSection
            v-if="resume && useProfile.profile"
            :profile="useProfile.profile"
            :resume="resume"
          />
          <div class="modal-action">
            <form method="dialog">
              <button class="btn">Đóng</button>
            </form>
          </div>
        </div>
      </dialog>
    </div>
  </div>
</template>
<script setup lang="ts">
import { getAccessToken } from '@/utils/authLocalstorage';
import Collapse from '~/components/common/collapse.vue';
import IconTrash from '~/components/icons/IconTrash.vue';
import IconUpload from '~/components/icons/IconUpload.vue';
import GeneralInformationSection from '~/components/resume/GeneralInformation/GeneralInformationSection.vue';
import type { HttpResponse } from '~/store/httpRequest.store';
import ResumeCompletedAlert from '~/components/resume/ResumeCompletedAlert.vue';
import ResumeFormFooter, { type ResumeFormFooterSubmitPayload } from '~/components/resume/ResumeFormFooter.vue';
import PreviewSection from '~/components/resume/Preview/PreviewSection.vue';

import { useResumeStore } from '~/store/resume';
import { ResumeStatus, ResumeType } from '~/types';
import { useProfileStore } from '~/store/profile';

export interface IGeneralInformationFormModel {
  expectPosition: string;
  currentLevelId: number | null;
  expectLevelId: number | null;
  expectSalaryId: number | null;
  occupationIds: number[];
  jobEducationId: number | null;
  jobExperienceId: number | null;
  jobMethodId: number | null;
  jobSkills: string[];
  jobProvinceIds: number[];
  careerObjective: string | null;
}

const { createResume, updateGeneralInfo, getResume } = useResumeStore();
const useProfile = useProfileStore();
const { resume, updatePending, createPending } = storeToRefs(useResumeStore());

definePageMeta({
  layout: 'profile',
});

const generalInformationCompleted = computed(() => {
  if (!resume || !resume.value) return false;
  const cvFileUrlValid = !!resume.value.cvFileUrl;
  const expectPositionValid = !!resume.value.expectPosition;
  const occupationIdsValid = !!(resume.value.occupationIds?.length > 0);
  const currentLevelIdValid = !!resume.value.currentLevelId;
  const expectLevelIdValid = !!resume.value.expectLevelId;
  const expectSalaryValid = !!resume.value.expectSalary;
  const experienceIdValid = !!resume.value.experienceId;
  const provinceIdsValid = !!(resume.value.provinceIds?.length > 0);

  return (
    cvFileUrlValid &&
    expectPositionValid &&
    occupationIdsValid &&
    currentLevelIdValid &&
    expectLevelIdValid &&
    expectSalaryValid &&
    experienceIdValid &&
    provinceIdsValid
  );
});

const isUpdate = computed(() => {
  const resumeId = parseResumeIdFromQuery();
  return !!resumeId;
});

const pageTitle = computed(() => {
  return isUpdate.value ? 'Cập nhật hồ sơ' : 'Tạo hồ sơ';
});

const cv = ref<{
  url: string;
  name: string;
} | null>(null);

const onOpenFileModal = () => {
  const input = document.getElementById('cv_upload_input') as HTMLInputElement;
  input.click();
  input.addEventListener('change', uploadFile);
};

const uploadFile = (e: Event) => {
  const target = e.target as HTMLInputElement;
  if (!target.files) return;
  const file = target.files[0];
  if (file) {
    const baseUrl = useRuntimeConfig().public.apiUrl;
    const formData = new FormData();
    formData.append('file', file);
    $fetch<HttpResponse<any>>(`${baseUrl}/v1/cv/me/upload`, {
      method: 'POST',
      body: formData,
      headers: {
        Authorization: `Bearer ${getAccessToken()}`,
      },
    })
      .then((res) => {
        const cvFile = res.data?.file?.url;
        if (cvFile) {
          cv.value = {
            url: cvFile,
            name: file.name,
          };
        }
      })
      .catch((err) => {
        console.log('Upload file failed', err);
      });
  }
};

const parseResumeIdFromQuery = () => {
  const rawResumeId = useRoute().query.resume_id as string;
  if (!rawResumeId) return null;
  return parseInt(rawResumeId);
};

const onUpdateCvUrl = () => {
  const resumeId = parseResumeIdFromQuery();
  if (resumeId) {
    updateGeneralInfo(resumeId, {
      cvFileUrl: cv.value?.url,
    });
  } else {
    createResume({
      type: ResumeType.offline,
      cvFileUrl: cv.value?.url,
    });
  }
};

const onPreview = () => {
  const modal = document.getElementById('modal_resume_review') as HTMLDialogElement;
  modal.showModal();
};

const onPublishResume = async (payload: ResumeFormFooterSubmitPayload) => {
  const resumeId = parseResumeIdFromQuery();
  if (!resumeId) {
    return;
  }

  await updateGeneralInfo(resumeId, {
    status: ResumeStatus.active,
  });
};

onMounted(() => {
  const resumeId = parseResumeIdFromQuery();
  if (resumeId) {
    getResume(resumeId);
  }
});

watch(
  () => useResumeStore().getResumeSuccess,
  (value: boolean, oldValue: boolean) => {
    if (value && !oldValue) {
      const resumeData = useResumeStore().resume;
      if (!resumeData) return;
      if (resumeData.cvFileUrl) {
        cv.value = {
          url: resumeData.cvFileUrl,
          name: 'CV đính kèm',
        };
      }
    }
  },
);

watch(
  () => useResumeStore().updateSuccess,
  (value: boolean, oldValue: boolean) => {
    if (value && !oldValue) {
      useRouter().push({
        name: 'ho-so-cua-ban',
      });
    }
  },
);
</script>
