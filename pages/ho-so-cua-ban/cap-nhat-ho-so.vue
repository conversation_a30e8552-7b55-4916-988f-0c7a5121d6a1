<template>
  <div>
    <div class="relative">
      <Container class="max-h-[calc(100vh - 60px)] overflow-y-scroll hide-scrollbar mb-36">
        <h1 class="font-bold text-2xl my-4">
          {{ pageTitle }}
        </h1>
        <ResumeCompletedAlert :completed="tinyintToBoolean(resume?.completed)" />
        <Spin :loading="updatePending || getResumePending">
          <GeneralInformationSection
            :form-open-at-start="!isUpdate"
            :resume="isUpdate ? resume : null"
            :type="ResumeType.online"
            :required="true"
            :valid="tinyintToBoolean(resume?.generalInfoCompleted)"
          />
          <template v-if="isUpdate">
            <ExperienceSection />
            <AcademySection />
            <LanguageSection />
            <ITSection />
            <ReferenceSection />
            <ResumeFormFooter
              :is-search-allowed="resume?.isSearchAllowed"
              @on-preview="onPreview"
              @on-request-approval="onPublishResume"
            />
          </template>
        </Spin>
      </Container>
    </div>

    <dialog
      id="modal_resume_review"
      class="modal modal-bottom sm:modal-middle"
    >
      <div class="modal-box modal-bottom md:w-11/12 md:max-w-4xl md:max-h-[calc(100vh-4rem)]">
        <PreviewSection
          v-if="resume && useProfile.profile"
          :profile="useProfile.profile"
          :resume="resume"
        />
        <div class="modal-action">
          <form method="dialog">
            <button class="btn">Đóng</button>
          </form>
        </div>
      </div>
    </dialog>
  </div>
</template>
<script setup lang="ts">
import Spin from '~/components/common/Spin.vue';
import Container from '~/components/layouts/container.vue';
import AcademySection from '~/components/resume/AcademySection.vue';
import ExperienceSection from '~/components/resume/Experience/ExperienceSection.vue';
import GeneralInformationSection from '~/components/resume/GeneralInformation/GeneralInformationSection.vue';
import ITSection from '~/components/resume/ITSection.vue';
import LanguageSection from '~/components/resume/Language/LanguageSection.vue';
import PreviewSection from '~/components/resume/Preview/PreviewSection.vue';
import ReferenceSection from '~/components/resume/ReferenceSection.vue';
import ResumeCompletedAlert from '~/components/resume/ResumeCompletedAlert.vue';
import ResumeFormFooter, { type ResumeFormFooterSubmitPayload } from '~/components/resume/ResumeFormFooter.vue';
import { ResumeType } from '~/types/resume.interface';

import { useProfileStore } from '~/store/profile';
import { useResumeStore } from '~/store/resume';
import { ResumeStatus } from '~/types';
const useProfile = useProfileStore();
const { getResume, updateGeneralInfo } = useResumeStore();
const useResume = useResumeStore();
const { resume, updatePending, getResumePending } = storeToRefs(useResumeStore());

definePageMeta({
  layout: 'profile',
  middlewares: ['auth'],
});

const isUpdate = computed(() => {
  const resumeId = parseResumeIdFromQuery();
  return !!resumeId;
});

const pageTitle = computed(() => {
  return isUpdate.value ? 'Cập nhật hồ sơ' : 'Tạo hồ sơ';
});

const parseResumeIdFromQuery = () => {
  const rawResumeId = useRoute().query.resume_id as string;
  if (!rawResumeId) return null;
  return parseInt(rawResumeId);
};

const onPublishResume = async (payload: ResumeFormFooterSubmitPayload) => {
  console.log('onPublishResume', payload);
  const resumeId = parseResumeIdFromQuery();
  if (!resumeId) {
    return;
  }

  await updateGeneralInfo(resumeId, {
    // isSearchAllowed: payload.isSearchAllowed,
    status: ResumeStatus.active,
  });
};

const onPreview = () => {
  const modal = document.getElementById('modal_resume_review') as HTMLDialogElement;
  modal.showModal();
};

onMounted(() => {
  const resumeId = parseResumeIdFromQuery();
  if (resumeId) {
    getResume(resumeId);
  }
});

watch(
  () => useResume.updateSuccess,
  (val: boolean, oldValue: boolean) => {
    if (val && oldValue !== val) {
      useRouter().push({
        name: 'ho-so-cua-ban',
      });
    }
  },
);
</script>
