<template>
  <Container>
    <Spin :loading="loading">
      <div class="text-2xl font-semibold my-4"><PERSON><PERSON> sơ của bạn</div>
      <ResumeAlert />
      <div>
        <div class="text-gray-400 text-sm font-semibold"><PERSON><PERSON> sơ trực tuyến</div>
        <template v-if="onlineResumes?.length">
          <ResumeItem
            v-for="resume of onlineResumes"
            :key="resume.id"
            :resume="resume"
          />
        </template>
        <OnlineResumeEmpty v-else />

        <div class="text-gray-400 text-sm font-semibold"><PERSON><PERSON> sơ đính kèm</div>

        <template v-if="offlineResumes?.length">
          <ResumeItem
            v-for="resume of offlineResumes"
            :key="resume.id"
            :resume="resume"
          />
        </template>
        <OfflineResumeEmpty v-else />
      </div>
    </Spin>
  </Container>
</template>
<script setup lang="ts">
import Spin from '~/components/common/Spin.vue';
import IconAttach from '~/components/icons/IconAttach.vue';
import IconDocument from '~/components/icons/IconDocument.vue';
import Container from '~/components/layouts/container.vue';
import ResumeAlert from '~/components/resume/ResumeAlert.vue';
import ResumeItem from '~/components/resume/ResumeItem.vue';
import { useResumeStore } from '~/store/resume';
import OnlineResumeEmpty from '~/components/resume/OnlineResumeEmpty.vue';
import OfflineResumeEmpty from '~/components/resume/OfflineResumeEmpty.vue';

const { getResumes, getAttachedResumes, getOnlineResumes } = useResumeStore();
const { resumes, onlineResumes, offlineResumes, getResumePending, deletePending } = storeToRefs(useResumeStore());

definePageMeta({
  layout: 'profile',
  middleware: 'auth',
});

const loading = computed(() => {
  return getResumePending.value || deletePending.value;
});

onMounted(() => {
  getAttachedResumes();
  getOnlineResumes();
});
</script>
