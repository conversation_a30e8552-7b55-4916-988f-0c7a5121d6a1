<template>
  <Container>
    <h1 class="text-2xl font-bold"><PERSON><PERSON> sơ của bạn</h1>

    <div
      role="alert"
      class="alert alert-info"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        class="stroke-current shrink-0 w-6 h-6"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
        />
      </svg>
      <ul>
        <li><PERSON><PERSON><PERSON> có thể tạo tối đa 02 hồ sơ..</li>
        <li>B<PERSON>t “Cho phép tìm kiếm” sẽ tăng tối đa cơ hội được Nhà tuyển dụng liên hệ với bạn.</li>
        <li><PERSON><PERSON><PERSON> <PERSON>ồ sơ ở trạng thái “Đã duyệt” đ<PERSON><PERSON> có thể sử dụng để nộp <PERSON><PERSON> sơ một cách nhanh chóng.</li>
      </ul>
    </div>

    <div>
      <h1><PERSON><PERSON> sơ trực tuyến</h1>
      <div class="flex justify-between border-neutral-content bg-white p-4 rounded-md mb-4">
        <div class="flex items-center">
          <div class="mr-4">
            <img
              src="https://via.placeholder.com/55"
              alt="avatar"
            />
          </div>
          <div class="font-bold">IT Operations Manager</div>
        </div>
        <div class="space-x-2">
          <button class="btn btn-primary btn-soft">Sửa</button>
        </div>
      </div>
    </div>
    <div>
      <h1>Hồ sơ đính kèm</h1>
      <div class="flex justify-between border-neutral-content bg-white p-4 rounded-md mb-4">
        <div class="flex items-center">
          <div class="mr-4">
            <img
              src="https://via.placeholder.com/55"
              alt="avatar"
            />
          </div>
          <div class="font-bold">IT Operations Manager</div>
        </div>
        <div class="space-x-2">
          <button class="btn btn-primary btn-soft">Sửa</button>
        </div>
      </div>
      <div class="flex justify-between border-neutral-content bg-white p-4 rounded-md">
        <div class="flex items-center">
          <div class="mr-4">
            <img
              src="https://via.placeholder.com/55"
              alt="avatar"
            />
          </div>
          <div class="font-bold">IT Operations Manager</div>
        </div>
        <div class="space-x-2">
          <button class="btn btn-primary btn-soft">Sửa</button>
        </div>
      </div>
    </div>
  </Container>
</template>
<script setup lang="ts">
import Container from '~/components/layouts/container.vue';
import { useResumeStore } from '~/store/resume';

const { getResumes } = useResumeStore();
const { resumes } = storeToRefs(useResumeStore());

definePageMeta({
  layout: 'profile',
});

onMounted(() => {
  getResumes();
});
</script>
