<template>
  <div>
    <ProfileHeader name="Thông tin nhà tuyển dụng" />
    <div class="lg:mx-6">
      <Collapse
        :show-adding="false"
        title="Thông tin công ty"
        :loading="createPending || updatePending"
      >
        <ProfileForm
          v-if="profile"
          :default-profile="profile"
          @ok="onSubmit"
        />
      </Collapse>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useEmployerCompanyStore } from '@/store/employerCompany';
import ProfileHeader from '~/components/account/ProfileHeader.vue';
import Collapse from '~/components/common/collapse.vue';
import ProfileForm, { type IEmployerProfileForm } from '~/components/employer/profile/ProfileForm.vue';
import { ToastType, useToast } from '~/store/toast';

const { getMyCompany, update, create } = useEmployerCompanyStore();

const { profile, createPending, updatePending } = storeToRefs(useEmployerCompanyStore());

definePageMeta({
  layout: 'employer',
});

const onSubmit = (payload: IEmployerProfileForm) => {
  // errors.value = validate();
  // if (Object.values(errors.value).some((e) => e)) {
  //   return;
  // }
  if (!profile.value) {
    useToast().error('Không tìm thấy thông tin công ty');
    return;
  }
  const body = {
    name: payload.name,
    // taxCode: taxCode.value,
    logo: payload.logo || null,
    address: payload.address,
    provinceId: payload.provinceId,
    employeeSizeId: payload.employeeSizeId,
    // occupationIds: payload.o || [],
    phone: payload.phone || null,
  };
  update({
    id: profile.value.id,
    ...body,
  });
};

onMounted(() => {
  getMyCompany();
});

watch(
  () => useEmployerCompanyStore().createSuccess,
  (value, oldValue) => {
    if (value && value !== oldValue) {
      useToast().showToast('Cập nhật thông tin công ty thành công', ToastType.Success);
    }
  },
);

watch(
  () => useEmployerCompanyStore().updateSuccess,
  (value, oldValue) => {
    if (value && value !== oldValue) {
      useToast().showToast('Cập nhật thông tin công ty thành công', ToastType.Success);
    }
  },
);

watch(
  () => useEmployerCompanyStore().updateError,
  (value, oldValue) => {
    if (value && value !== oldValue) {
      useToast().showToast('Cập nhật thông tin công ty thất bại', ToastType.Error);
    }
  },
);
</script>
