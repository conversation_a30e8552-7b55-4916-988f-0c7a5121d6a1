<template>
  <div class="max-h-full">
    <ProfileHeader name="Tạo tài khoản nhà tuyển dụng" />
    <br />
    <div class="bg-white card card-lg rounded-none">
      <div class="card-body">
        <Spin :loading="useEmployer.createPending">
          <Container>
            <ProfileForm
              ok-text="Hoàn tất"
              @ok="onOk"
            />
          </Container>
        </Spin>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
definePageMeta({
  layout: 'default',
  middleware: 'auth',
});

import ProfileHeader from '~/components/account/ProfileHeader.vue';
import Container from '~/components/layouts/container.vue';
import ProfileForm, { type IEmployerProfileForm } from '~/components/employer/profile/ProfileForm.vue';
import Spin from '~/components/common/Spin.vue';
import { useEmployerCompanyStore } from '~/store/employerCompany';
import { useToast } from '~/store/toast';
import { useAuthStore } from '~/store/auth.store';

const useEmployer = useEmployerCompanyStore();
const useAuth = useAuthStore();

const onOk = (payload: IEmployerProfileForm) => {
  useEmployer.create(payload);
};

const handleIfCompanyExists = async () => {
  console.log('handleIfCompanyExists', useEmployer.profile, useAuth.profile);
  if (useEmployer.profile && useAuth.profile?.id === useEmployer.profile.userId) {
    useRouter().push('/employer/accounts/profile');
  }
};

const init = async () => {
  if (useEmployer.getProfileSuccess) {
    handleIfCompanyExists();
  } else {
    await useEmployer.getMyCompany();
  }
}

onMounted(() => {
  init();
});

watch(
  () => useEmployer.createError,
  (value, oldValue) => {
    if (value && value !== oldValue) {
      useToast().error(useEmployer.createMessage || 'Có lỗi xảy ra, vui lòng thử lại sau');
    }
  },
);

watch(
  () => useEmployer.createSuccess,
  (value, oldValue) => {
    if (value && value !== oldValue) {
      useToast().success(useEmployer.createMessage || 'Tạo tài khoản thành công');
      handleIfCompanyExists();
    }
  },
);

watch(
  () => useEmployer.getProfileSuccess,
  (value, oldValue) => {
    if (value && value !== oldValue) {
      handleIfCompanyExists();
    }
  },
);
</script>
