<template>
  <div>
    <ProfileHeader name="Thông tin nhà tuyển dụng" />
    <div class="md:mx-6">
      <Collapse
        title="Thông tin doanh nghiệp"
        :show-adding="false"
        :loading="updatePending"
      >
        <DescriptionCompanyForm
          :default-website="profile?.website"
          :default-overview="profile?.overview"
          @ok="onSubmit"
        />
      </Collapse>
      <br />
      <Collapse
        title="Ảnh bìa công ty"
        :show-adding="false"
      >
        <HorizontalFormItem
          :required="false"
          label="Ảnh bìa công ty"
        >
          <div class="max-w-[600px] h-[180px]">
            <img
              :src="profile?.bgImage || 'https://cdn1.Vieclamlamdong.site/images/assets/img/generic_8.jpg'"
              alt="Ảnh bìa công ty"
              class="w-full object-cover h-full rounded-md"
            />
          </div>
        </HorizontalFormItem>

        <HorizontalFormItem
          :required="false"
          label=""
        >
          <div class="flex space-x-2 justify-between items-center md:justify-start md:space-x-12">
            <button
              class="btn btn-primary"
              @click="onUploadBgImgModalOpen"
            >
              Thay đổi ảnh bìa
            </button>
            <NuxtLink
              :to="{
                path: `/danh-sach-tin-tuyen-dung${profile?.slug}id-${profile?.id}`,
              }"
              target="_blank"
              class="text-primary text-sm font-semibold"
            >
              Xem trang công ty của tôi
            </NuxtLink>
          </div>
        </HorizontalFormItem>
      </Collapse>
    </div>
    <dialog
      id="company_trade_mark_modal"
      class="modal"
    >
      <div class="modal-box">
        <form method="dialog">
          <a
            href="javascript:void(0)"
            class="absolute right-2 top-2"
          >
            <IconsIconClearRound class="w-6 h-6 text-primary" />
          </a>
        </form>
        <h3 class="text-lg font-bold">Chọn ảnh bìa</h3>
        <Spin :loading="uploading || updateBgPending">
          <a
            class="block bg-light text-center border border-dashed p-6 border-primary rounded-lg bg-primary/10 my-6"
            href="javascript:void(0)"
            @click="onUpload"
          >
            <button class="btn btn-primary btn-sm font-light">
              <IconsIconUpload class="w-4 h-4" />
              Thêm ảnh của bạn
            </button>
            <input
              id="input_upload_background"
              type="file"
              class="hidden"
              accept="image/jpg,image/jpeg,image/png"
              @change="onFileChange"
            />
            <p class="text-sm font-light my-2">Kích thước tối thiểu 1400x800 pixel, kích cỡ tối đa 3MB</p>
            <p class="text-primary font-light text-xs">
              Vui lòng sử dụng hình lớn hơn 2000 pixel để có chất lượng cao nhất
            </p>
          </a>
        </Spin>
        <div v-if="backgroundImage">
          <img
            :src="backgroundImage"
            alt="Ảnh bìa công ty"
            class="object-cover w-[624px] h-[125px] rounded-md"
          />
        </div>

        <div class="modal-action">
          <form
            method="dialog"
            class="flex justify-end gap-2"
          >
            <button
              class="btn"
              :disabled="updateBgPending"
              @click="onUploadBgImgModalClose"
            >
              Hủy
            </button>
            <button
              class="btn"
              :disabled="updateBgPending || !backgroundImage"
              @click="onUpdateBgImg"
            >
              Xác nhận
            </button>
          </form>
        </div>
      </div>
    </dialog>
  </div>
</template>
<script setup lang="ts">
import ProfileHeader from '~/components/account/ProfileHeader.vue';
import Collapse from '~/components/common/collapse.vue';
import RichEditor from '~/components/common/RichEditor.vue';
import Spin from '~/components/common/Spin.vue';
import DescriptionCompanyForm, { type ICompanyDescriptionPayload } from '~/components/employer/profile/DescriptionCompanyForm.vue';
import HorizontalFormItem from '~/components/form/HorizontalFormItem.vue';
import { useEmployerCompanyStore } from '~/store/employerCompany';
import type { HttpResponse } from '~/store/httpRequest.store';
import { ToastType, useToast } from '~/store/toast';
import type { Company } from '~/types';
import { type File } from '~/types';
const { update, getMyCompany, updateBgImgCompany } = useEmployerCompanyStore();
const {
  updateMessage,
  updatePending,
  updateError,
  updateSuccess,
  updateBgError,
  updateBgMessage,
  updateBgPending,
  updateBgSuccess,
  profile,
} = storeToRefs(useEmployerCompanyStore());

definePageMeta({
  layout: 'employer',
});

const overview = ref<string | null>(null);
const website = ref<string | null>(null);

const backgroundImage = ref<string | null>(null);
const uploading = ref<boolean>(false);
const baseFormRules = {
  overview: [{ required: true, message: 'Vui lòng nhập giới thiệu doanh nghiệp' }],
  website: [{ required: false, message: 'Vui lòng nhập website công ty' }],
};
const baseFormErrors = ref({
  overview: null as string | null,
  website: null as string | null,
});

const resetBaseFormErrors = () => {
  baseFormErrors.value = {
    overview: null,
    website: null,
  };
};

const validateForm = () => {
  resetBaseFormErrors();
  const overviewRequired = baseFormRules.overview.find((rule) => rule.required);
  // console.log(overviewRequired, overview.value);
  if (overviewRequired && !overview.value) {
    baseFormErrors.value.overview = overviewRequired.message;
  }
  const websiteRequired = baseFormRules.website.find((rule) => rule.required);
  if (websiteRequired && !website.value) {
    baseFormErrors.value.website = websiteRequired.message;
  }
  return Object.values(baseFormErrors.value).every((error) => !error);
};

const onFileChange = async (e: Event) => {
  const target = e.target as HTMLInputElement;
  const file = target.files?.[0];
  if (file) {
    const reader = new FileReader();
    reader.onload = (e) => {
      backgroundImage.value = e.target?.result as string;
    };
    reader.readAsDataURL(file);

    const config = useRuntimeConfig();
    const baseUrl = config.public.apiUrl;
    const formData = new FormData();
    formData.append('file', file);
    try {
      uploading.value = true;
      const response = await fetch(`${baseUrl}/v1/files/background/upload`, {
        method: 'POST',
        body: formData,
        headers: {
          Authorization: `Bearer ${getAccessToken()}`,
        },
      });
      const apiResponse: HttpResponse<File> = await response.json();

      if (apiResponse && apiResponse.success) {
        backgroundImage.value = apiResponse.data?.url;
      }
    } catch (error: any) {
      console.error(error);
      const msg = error?.message || 'Cập nhật ảnh bìa công ty thất bại';
      useToast().showToast(msg, ToastType.Error);
    } finally {
      uploading.value = false;
    }
  }
};

const onWebsiteChange = (event: Event) => {
  website.value = (event.target as HTMLInputElement).value;
};

const onSubmit = (formPayload: ICompanyDescriptionPayload) => {
  // const validateSuccess = validateForm();
  // if (!validateSuccess) {
  //   return;
  // }
  const payload: Partial<Company> = {
    overview: formPayload.overview,
    website: formPayload.website,
  };
  update(payload);
};

const mapCompanyToForm = (company: Company | null) => {
  if (!company) {
    return;
  }
  overview.value = company.overview;
  website.value = company.website;
};

const onUpload = () => {
  const input = document.getElementById('input_upload_background');
  if (input) {
    input.click();
  }
};

const onUploadBgImgModalClose = () => {
  const modal = document.getElementById('company_trade_mark_modal') as any;
  if (modal) {
    modal.close();
  }
};
const onUploadBgImgModalOpen = () => {
  const modal = document.getElementById('company_trade_mark_modal') as any;
  if (modal) {
    modal.showModal();
  }
};

const onUpdateBgImg = () => {
  if (!backgroundImage.value) {
    useToast().showToast('Vui lòng chọn ảnh bìa công ty', ToastType.Error);
    return;
  }
  updateBgImgCompany(backgroundImage.value);
};

onMounted(() => {
  getMyCompany();
});

watch(
  () => useEmployerCompanyStore().updateSuccess,
  (value) => {
    if (value) {
      useToast().showToast('Cập nhật thông tin công ty thành công', ToastType.Success);
    }
  },
);

watch(
  () => useEmployerCompanyStore().updateError,
  (value) => {
    if (value) {
      const msg: string = updateMessage.value || 'Cập nhật thông tin công ty thất bại';
      useToast().showToast(msg, ToastType.Error);
    }
  },
);

watch(
  () => useEmployerCompanyStore().profile,
  (value) => {
    mapCompanyToForm(value);
  },
  { immediate: true },
);

watch(
  () => useEmployerCompanyStore().updateBgSuccess,
  (value) => {
    if (value) {
      useToast().showToast('Cập nhật ảnh bìa công ty thành công', ToastType.Success);
      onUploadBgImgModalClose();
    }
  },
);

watch(
  () => useEmployerCompanyStore().updateBgError,
  (value) => {
    if (value) {
      const msg: string = updateBgMessage.value || 'Cập nhật ảnh bìa công ty thất bại';
      useToast().showToast(msg, ToastType.Error);
    }
  },
);
</script>
