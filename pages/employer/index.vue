<template>
  <div></div>
</template>
<script lang="ts" setup>
definePageMeta({
  layout: 'employer',
  middleware: 'auth',
});
import { useCompanyStore } from '~/store/company.store';
// const useCompany = useCompanyStore();
onBeforeMount(() => {
  useRouter().push('/employer/jobs/list');
});

// onMounted(() => {
//   useCompany.getCompany(1);
// });

// watch(
//   () => useCompany.getDetailsSuccess,
//   (newVal, oldValue) => {
//     if (newVal && newVal !== oldValue && !useCompany.company) {
//       useRouter().push('/employer/account/create');
//     }
//   },
// );
</script>
