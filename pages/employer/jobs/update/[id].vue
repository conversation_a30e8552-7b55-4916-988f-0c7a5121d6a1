<template>
  <div>
    <ProfileHeader name="<PERSON>ậ<PERSON> nhật tin tuyển" />
    <br />
    <div class="mx-2 lg:mx-6">
      <RecruitmentForm
        v-if="useEmployerRecruitment.recruitment && useCompany.company"
        :loading="loading"
        :recruitment="useEmployerRecruitment.recruitment"
        :company="useCompany.company"
        @submit="onSubmit"
      />
      <Empty v-else />
    </div>
  </div>
</template>
<script setup lang="ts">
import ProfileHeader from '~/components/account/ProfileHeader.vue';
import type { IPayload } from '~/components/employer/recruitment/RecruitmentForm.vue';
// import Form, { type IPayload } from '~/components/employer/recruitment/Form.vue';
import Empty from '~/components/common/Empty.vue';
import RecruitmentForm from '~/components/employer/recruitment/RecruitmentForm.vue';
import { useCompanyStore } from '~/store/company.store';
import { useEmployerRecruitmentStore } from '~/store/employerRecruitment';
import { useProfileStore } from '~/store/profile';
import { ToastType, useToast } from '~/store/toast';

// const { getDetails, update } = useEmployerRecruitmentStore();
const useEmployerRecruitment = useEmployerRecruitmentStore();
const { updatePending } = storeToRefs(useEmployerRecruitmentStore());
const useCompany = useCompanyStore();
const useProfile = useProfileStore();

definePageMeta({
  layout: 'employer',
});

const recruitmentId = ref<number | null>(null);
const loading = computed<boolean>(() => {
  return useEmployerRecruitment.getDetailsPending || useEmployerRecruitment.updatePending;
});

const onSubmit = (payload: IPayload) => {
  if (!recruitmentId.value) {
    useToast().showToast('Không tìm thấy tin tuyển dụng', ToastType.Error);
    return;
  }
  useEmployerRecruitment.update(recruitmentId.value, payload);
};

onMounted(() => {
  const rawId = useRoute().params.id;
  if (typeof rawId === 'string') {
    recruitmentId.value = parseInt(rawId);
    if (recruitmentId.value) {
      useEmployerRecruitment.getDetails(recruitmentId.value);
    }
  }
  useCompany.getCompany(1);
});

watch(
  () => useEmployerRecruitment.updateSuccess,
  (success) => {
    if (success) {
      useToast().showToast('Cập nhật tin tuyển dụng thành công', ToastType.Success);
      useRouter().push({
        path: '/employer/jobs/list',
      });
    }
  },
);

watch(
  () => useEmployerRecruitment.updateError,
  (error) => {
    if (error) {
      useToast().showToast('Cập nhật tin tuyển dụng thất bại', ToastType.Error);
    }
  },
);
</script>
