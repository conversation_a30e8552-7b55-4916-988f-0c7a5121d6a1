<template>
  <div>
    <ProfileHeader name="Tạo tin tuyển dụng" />
    <br />
    <div class="lg:mx-6">
      <div class="bg-white">
        <CommonCheckBox v-model="jumpPage" />
      </div>
      <RecruitmentForm
        v-if="useCompany.company && useProfile.profile"
        :recruitment="undefined"
        :contact-email="useProfile.profile?.emailAddress"
        :contact-phones="useProfile.profile?.phoneNumber ? [useProfile.profile?.phoneNumber] : []"
        :contact-name="useProfile.profile?.fullName"
        :loading="useEmployerRecruitment.createPending"
        :company="useCompany.company"
        @submit="onSubmit"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
import ProfileHeader from '~/components/account/ProfileHeader.vue';
import RecruitmentForm from '~/components/employer/recruitment/RecruitmentForm.vue';
import { useCompanyStore } from '~/store/company.store';
import { useEmployerRecruitmentStore } from '~/store/employerRecruitment';
import { useProfileStore } from '~/store/profile';
import { ToastType, useToast } from '~/store/toast';

definePageMeta({
  layout: 'employer',
  middleware: 'auth',
});
const useProfile = useProfileStore();
const useEmployerRecruitment = useEmployerRecruitmentStore();
const useCompany = useCompanyStore();

const jumpPage = ref<boolean>(true);

const onSubmit = (payload: Record<string, any>) => {
  useEmployerRecruitment.create(payload);
};

onMounted(() => {
  useCompany.getCompany(1);
  useProfile.getProfile();
});
watch(
  () => useEmployerRecruitment.createSuccess,
  (value, oldValue) => {
    if (value && value !== oldValue) {
      useToast().success('Tạo tin tuyển dụng thành công');
      if (!useEmployerRecruitment.recruitment) return;
      if (!jumpPage.value) {
        return;
      }
      useRouter().push({
        path: '/employer/jobs/update/' + useEmployerRecruitment.recruitment?.id,
      });
    }
  },
);
watch(
  () => useEmployerRecruitment.createError,
  (value, oldValue) => {
    if (value && value !== oldValue) {
      useToast().error(useEmployerRecruitment.createMessage || 'Tạo tin tuyển dụng thất bại');
    }
  },
);
</script>
