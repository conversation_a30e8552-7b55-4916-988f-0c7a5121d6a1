<template>
  <div class="h-full">
    <ProfileHeader name="<PERSON><PERSON>ản lý danh sách ứng tuyển" />
    <div class="lg:mx-6 h-full">
      <CommonCollapse
        title="Danh sách ứng tuyển"
        :show-adding="false"
        :loading="loading"
        class="h-full"
      >
        <div class="flex justify-start items-center mb-4 h-full">
          <div class="text-sm w-full max-w-32">Tr<PERSON>ng thái</div>
          <div class="w-full max-w-56">
            <DropdownSearchSelect
              v-model:model-value="formFilterState.status"
              :list="[
                {
                  id: null,
                  name: 'T<PERSON>t cả',
                },
                ...Object.keys(RecruitmentAppliedStatusText).map((key) => ({
                  id: key,
                  name: RecruitmentAppliedStatusText[key],
                })),
              ]"
              id-key="id"
              value-key="name"
              placeholder="Chọn trạng thái"
              @select="() => onFilter()"
            />
          </div>
        </div>
        <div class="text-sm px-3 mt-6 mb-4">
          Tổng số <b>{{ commonUtils.formatNumber(useEmployerAppliedRecruitment.total) }}</b> hồ sơ ứng tuyển
        </div>
        <div class="overflow-x-auto h-full">
          <table class="table table-zebra">
            <thead>
              <tr class="text-gray-600 text-base">
                <td class="font-medium">Tên hồ sơ</td>
                <td class="min-w-md font-medium">Tin đăng</td>
                <td class="font-medium">Thời gian nộp</td>
                <th class="font-medium">Trạng thái</th>
              </tr>
            </thead>
            <tbody>
              <template v-if="useEmployerAppliedRecruitment.items.length">
                <tr
                  v-for="(item, idx) in useEmployerAppliedRecruitment.items"
                  :key="idx"
                >
                  <td class="text-blue-500 font-semibold">
                    <NuxtLink :to="`/employer/jobs/applied/${item.resumeId}/resume`">
                      {{ item.resume?.expectPosition }}
                    </NuxtLink>
                  </td>
                  <td class="text-blue-500 font-semibold">
                    <NuxtLink :to="`/employer/jobs/details/${item.resumeId}`">
                      {{ item.recruitment?.title }}
                    </NuxtLink>
                  </td>
                  <td>{{ formatDate(item.createdAt) }}</td>
                  <th class="max-w-44">
                    <div class="max-w-44">
                      <DropdownSearchSelect
                        :list="[
                          ...Object.keys(RecruitmentAppliedStatusText).map((key) => ({
                            id: key,
                            name: RecruitmentAppliedStatusText[key],
                          })),
                        ]"
                        class="max-w-44"
                        id-key="id"
                        value-key="name"
                        :value="item.status"
                        @select="(status) => onUpdate(item.id, status)"
                      />
                    </div>
                  </th>
                </tr>
              </template>
              <tr v-else>
                <td colspan="4">
                  <Empty description="Nội dung rỗng" />
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <Pagination
          :total="useEmployerAppliedRecruitment.total"
          :current-page="formFilterState.page"
          :page-size="formFilterState.limit"
          @change="
            ($event) => {
              formFilterState.page = $event;
              onFilter();
            }
          "
        />
      </CommonCollapse>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { UnwrapRef } from 'vue';
import ProfileHeader from '~/components/account/ProfileHeader.vue';
import DropdownSearchSelect from '~/components/common/DropdownSearchSelect.vue';
import Empty from '~/components/common/Empty.vue';
import Pagination from '~/components/common/pagination.vue';
import { RecruitmentAppliedStatusText } from '~/constants/index';
import { useEmployerAppliedRecruitmentStore } from '~/store/employerAppliedRecruitment.store';
import { ToastType, useToast } from '~/store/toast';
import { EnumRecruitmentApplyStatus } from '~/types';
import * as commonUtils from '~/utils/common';

definePageMeta({
  layout: 'employer',
  middleware: 'auth',
});

const useEmployerAppliedRecruitment = useEmployerAppliedRecruitmentStore();

const loading = computed(() => {
  return useEmployerAppliedRecruitment.getListPending || useEmployerAppliedRecruitment.updatePending;
});

interface FormFilterState {
  search: string;
  status: string | null;
  page: number;
  limit: number;
}
const formFilterState: UnwrapRef<FormFilterState> = reactive({
  search: '',
  status: null,
  page: 1,
  limit: 20,
});

const onFilter = () => {
  useEmployerAppliedRecruitment.getList({
    page: formFilterState.page,
    limit: formFilterState.limit,
    status: (formFilterState.status as EnumRecruitmentApplyStatus) || undefined,
  });
};

const onUpdate = (id: number | null, status: string | null | number) => {
  if (id && status) {
    const statusValue = String(status);
    const isStatusValid = Object.values(EnumRecruitmentApplyStatus).includes(statusValue as EnumRecruitmentApplyStatus);
    if (!isStatusValid) {
      return;
    }
    useEmployerAppliedRecruitment.update(id, statusValue as EnumRecruitmentApplyStatus);
  }
};
onMounted(() => {
  onFilter();
});

watch(
  () => useEmployerAppliedRecruitment.updateSuccess,
  (value: boolean, oldValue: boolean) => {
    if (value && !oldValue) {
      useToast().showToast('Cập nhật thành công', ToastType.Success);
    }
  },
);

watch(
  () => useEmployerAppliedRecruitment.updateError,
  (value: boolean, oldValue: boolean) => {
    if (value && !oldValue) {
      useToast().showToast(useEmployerAppliedRecruitment.updateMessage || 'Cập nhật thất bại', ToastType.Error);
    }
  },
);
</script>
