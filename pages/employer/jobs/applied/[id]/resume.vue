<template>
  <div>
    <ProfileHeader name="Chi tiết ứng viên" />

    <div class="mx-2 lg:mx-6">
      <div class="bg-white p-4 mt-4 rounded-md">
        <div
          v-if="customer"
          class="flex justify-start"
        >
          <div class="avatar">
            <div class="w-20 rounded-full">
              <img alt="vietlamlamdong.site" :src="customer.avatar" />
            </div>
          </div>
          <div class="ml-3 flex flex-col justify-start">
            <div class="font-semibold">
              {{ customer.fullName }}
            </div>
            <div class="font-light text-sm text-gray-600">
              {{ resume?.careerObjective }}
            </div>
          </div>
        </div>
        <br />

        <Divider />
        <PreviewSection
          :profile="customer"
          :resume="resume"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: 'employer',
  middleware: 'auth',
});

import ProfileHeader from '~/components/account/ProfileHeader.vue';
import Divider from '~/components/common/Divider.vue';
import PreviewSection from '~/components/resume/Preview/PreviewSection.vue';
import { useCommonStore } from '~/store/common.store';
import { useEmployerAppliedRecruitmentStore } from '~/store/employerAppliedRecruitment.store';

const useCommon = useCommonStore();
const useEmployerAppliedRecruitment = useEmployerAppliedRecruitmentStore();

const customer = computed(() => useEmployerAppliedRecruitment.details?.customer);
const resume = computed(() => useEmployerAppliedRecruitment.details?.resume);

onMounted(() => {
  const id = useRoute().params.id as string;
  useEmployerAppliedRecruitment.getDetails(parseInt(id, 10));
});
</script>
