<template>
  <div>
    <ProfileHeader name="Quản lý tin đăng" />
    <br />
    <div class="lg:mx-6">
      <Collapse
        title="Danh sách đăng tuyển"
        :show-adding="false"
        :loading="getListPending || deletePending || updatePending"
        class="mb-48"
      >
        <template #extra>
          <NuxtLink
            to="/employer/jobs/create"
            class="btn-primary btn btn-outline text-nowrap"
          >
            <span class="w-6 h-6">
              <IconPlus />
            </span>
            Tạo tin
          </NuxtLink>
        </template>
        <div class="flex justify-start items-center mb-4">
          <!-- <div class="text-sm w-full max-w-16">Bộ lọc</div> -->
          <div class="grid grid-cols-1 w-full md:gap-4 md:grid-cols-3 lg:grid-cols-4">
            <FormItem label="Từ khóa">
              <input
                v-model="keyword"
                class="input input-bordered w-full"
                placeholder="Tìm kiếm theo tên tin"
                @keyup.enter="onFilter"
              />
            </FormItem>
            <FormItem label="Trạng thái">
              <JobStatusSelect
                v-model="status"
                class="w-full"
                :empty-select="true"
                @on-change="onFilter"
              />
            </FormItem>
            <FormItem label="Ngày bắt đầu">
              <Calendar
                v-model:model-value="fromDate"
                placeholder="Ngày bắt đầu"
                :max="toDate"
                @on-change="onFilter"
              />
            </FormItem>
            <FormItem label="Ngày kết thúc">
              <Calendar
                v-model:model-value="toDate"
                :min="fromDate"
                placeholder="Ngày kết thúc"
                @on-change="onFilter"
              />
            </FormItem>
          </div>
        </div>
        <div class="text-sm px-4 my-4">
          Tổng số <b>{{ commonUtils.formatNumber(total) }}</b> bài đăng
        </div>
        <div class="overflow-x-auto">
          <table class="table table-zebra table-pin-cols">
            <thead>
              <tr class="!text-black text-sm">
                <!-- <th>
                  <a
                    class="flex items-center"
                    href="javascript:void(0)"
                    @click="onSort('id')"
                  >
                    <span class="mr-1">Mã tin</span>
                    <SortArrow :direction="orderBy === 'id' ? sortBy : undefined" />
                  </a>
                </th> -->
                <td class="font-medium">Tên tin đăng</td>
                <td
                  class="font-medium"
                  v-if="useCompany.company?.isBot"
                >
                  Công ty
                </td>
                <td
                  class="font-medium"
                  v-if="useCompany.company?.isBot"
                >
                  Logo
                </td>
                <td class="font-medium">
                  <a
                    class="flex items-center"
                    href="javascript:void(0)"
                    @click="onSort('applyExpiredAt')"
                  >
                    <span class="mr-1">Thời hạn</span>
                    <SortArrow :direction="orderBy === 'applyExpiredAt' ? sortBy : undefined" />
                  </a>
                </td>
                <td class="font-medium">
                  <a
                    class="flex items-center"
                    href="javascript:void(0)"
                  >
                    <span class="mr-1">Lượt nộp</span>
                    <SortArrow />
                  </a>
                </td>
                <td class="font-medium">
                  <a
                    class="flex items-center"
                    href="javascript:void(0)"
                    @click="onSort('totalViews')"
                  >
                    <span class="mr-1">Lượt xem</span>
                    <SortArrow :direction="orderBy === 'totalViews' ? sortBy : undefined" />
                  </a>
                </td>
                <td class="font-medium">Công bố</td>
                <td class="font-medium">Trạng thái</td>
                <td class="font-medium">Thời gian tạo</td>
                <th class="font-medium">Hành động</th>
              </tr>
            </thead>
            <tbody>
              <template v-if="items.length">
                <tr
                  v-for="(item, idx) in items"
                  :key="item.id"
                  class="font-light text-sm"
                >
                  <!-- <th>{{ item.id }}</th> -->
                  <td>
                    <div class="text-sm">{{ item.title }}</div>
                  </td>
                  <td v-if="useCompany.company?.isBot">
                    {{ item.companyName }}
                  </td>
                  <td v-if="useCompany.company?.isBot">
                    <img
                      :src="item.logo"
                      class="w-10 h-10 rounded"
                    />
                  </td>
                  <td>{{ formatDateOnly(item.applyExpiredAt) }}</td>
                  <td>{{ '-' }}</td>
                  <td class="text-xs">{{ item.totalViews }}</td>
                  <!-- <td>{{ item.isPublished ? 'Đang hiện' : 'Đang ẩn' }}</td> -->
                  <td>
                    <input
                      type="checkbox"
                      :checked="Boolean(item.isPublished)"
                      class="toggle toggle-primary"
                      :disabled="item.status !== ERecruitmentStatus.APPROVED"
                      @click="onPublish(item.id, item.isPublished ? TinyInt.No : TinyInt.Yes)"
                    />
                  </td>
                  <td>
                    <div :class="['badge badge-sm badge-soft text-nowrap', occupationStatusClassMap[item.status]]">
                      {{ RecruitmentStatusTextMapper[item.status] ?? item.status }}
                    </div>
                  </td>
                  <td>
                    {{ formatDate(item.createdAt) }}
                  </td>
                  <th>
                    <button
                      class="btn btn-ghost btn-circle"
                      :popovertarget="'popover-' + idx"
                      :style="'anchor-name: --anchor-' + idx"
                    >
                      <IconThreeDots />
                    </button>
                    <ul
                      :id="'popover-' + idx"
                      popover
                      class="dropdown max-w-48 w-full rounded-sm !border-base-300 menu bg-white dropdown-left dropdown-bottom shadow-lg"
                      :style="'position-anchor: --anchor-' + idx"
                    >
                      <li>
                        <NuxtLink
                          target="_blank"
                          :to="`/employer/jobs/details/${item.id}`"
                        >
                          <IconsIconEye class="w-4 h-4" />
                          <span class="ml-4">Xem lại</span>
                        </NuxtLink>
                      </li>
                      <li>
                        <NuxtLink
                          target="_blank"
                          :to="`/employer/jobs/update/${item.id}`"
                        >
                          <IconsIconPen class="w-4 h-4" />
                          <span class="ml-4">Chỉnh sửa</span>
                        </NuxtLink>
                      </li>
                      <li>
                        <label
                          class="cursor-pointer flex items-center"
                          @click="onCopyLink(item)"
                        >
                          <IconsIconDocument class="w-4 h-4" />
                          <span class="ml-4">Sao chép</span>
                        </label>
                      </li>
                      <!-- <li>
                        <label class="cursor-pointer flex items-center">
                          <IconsIconCalendar class="w-4 h-4" />
                          <span class="ml-4">Gia hạn nộp</span>
                        </label>
                      </li> -->
                      <li>
                        <Confirm
                          title="Xóa tin"
                          message="Bạn có chắc chắn muốn xóa tin này không?"
                          @ok="onDelete(item.id)"
                        >
                          <label class="cursor-pointer flex items-center">
                            <IconsIconTrash class="w-4 h-4 text-error" />
                            <span class="ml-6"> Xóa tin </span>
                          </label>
                        </Confirm>
                      </li>
                    </ul>
                  </th>
                </tr>
              </template>
              <tr v-else>
                <td
                  colspan="8"
                  class="text-center text-gray-600"
                >
                  <Empty />
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="flex justify-center items-center">
          <Pagination
            :total="total"
            :page-size="pageSize"
            :current-page="page"
            :on-change="onPageChange"
          />
        </div>
      </Collapse>
    </div>
  </div>
</template>
<script setup lang="ts">
import { formatDateOnly } from '@/utils/dateFormatter.utils';
import Empty from '~/components/common/Empty.vue';
import ProfileHeader from '~/components/account/ProfileHeader.vue';
import Collapse from '~/components/common/collapse.vue';
import Pagination from '~/components/common/pagination.vue';
import SortArrow from '~/components/common/SortArrow.vue';
import JobStatusSelect from '~/components/employer/recruitment/JobStatusSelect.vue';
import IconThreeDots from '~/components/icons/IconThreeDots.vue';
import { RecruitmentStatusTextMapper } from '~/constants/recruitment.constant';
import { useEmployerRecruitmentStore } from '~/store/employerRecruitment';
import { useCompanyStore } from '~/store/company.store';
import IconPlus from '~/components/icons/IconPlus.vue';
import { ToastType, useToast } from '~/store/toast';
import { EnumRecruitmentApplyStatus, TinyInt, type CanBeNil, type Recruitment } from '~/types';
import Confirm from '~/components/common/Confirm.vue';
import FormItem from '~/components/form/formItem.vue';
import Calendar from '~/components/common/DatePicker.vue';
import { ERecruitmentStatus } from '~/types/recruitment.interface';
import * as pageLinkUtils from '~/utils/pageLink.util';
import * as commonUtils from '~/utils/common';
// only import cally in client side
// import {
//   CalendarDate
// } from 'cally';

const { getList, update, deleteById } = useEmployerRecruitmentStore();
const useCompany = useCompanyStore();
const useEmployerRecruitment = useEmployerRecruitmentStore();
const { total, items, getListPending, deletePending, updatePending } = storeToRefs(useEmployerRecruitmentStore());
definePageMeta({
  layout: 'employer',
  middleware: 'auth',
});

type SortBy = 'asc' | 'desc';
type OrderBy = 'id' | 'publishedAt' | 'applyExpiredAt' | 'totalViews';
type RecruitmentStatus = EnumRecruitmentApplyStatus | null;

const page = ref(1);
const status = ref<RecruitmentStatus | null>(null);
const pageSize = ref(10);
const fromDate = ref<CanBeNil<string>>(null);
const toDate = ref<CanBeNil<string>>(null);
const sortBy = ref<SortBy>('desc');
const orderBy = ref<OrderBy>('id');
const keyword = ref<string>('');

const occupationStatusClassMap: Record<string, string> = {
  [EnumRecruitmentApplyStatus.APPROVED]: 'badge-success',
  [EnumRecruitmentApplyStatus.INTERVIEWING]: 'badge-secondary',
  [EnumRecruitmentApplyStatus.PENDING]: 'badge-warning',
  [EnumRecruitmentApplyStatus.REJECTED]: 'badge-error',
  [EnumRecruitmentApplyStatus.SUITABLE]: 'badge-primary',
};

const onPageChange = (value: number) => {
  page.value = value;
  onFilter();
};

const onFilter = () => {
  const body = {
    limit: pageSize.value,
    page: page.value,
    sortBy: sortBy.value, // id, publishedAt, expiredAt, totalViews
    orderBy: orderBy.value, // asc, desc
    status: status.value ? status.value : undefined,
    fromDate: fromDate.value ? fromDate.value : undefined,
    toDate: toDate.value ? toDate.value : undefined,
    keyword: keyword.value ? keyword.value : undefined,
  };
  getList(body);
};

const onSort = (value: OrderBy) => {
  if (orderBy.value === value) {
    sortBy.value = sortBy.value === 'asc' ? 'desc' : 'asc';
  } else {
    orderBy.value = value;
    sortBy.value = 'desc';
  }
  onFilter();
};

const onPublish = (id: number, isPublished: TinyInt) => {
  if (!id) return;
  update(id, { isPublished });
};

// draft to waiting for review
const onRequestReview = (id: number) => {
  if (!id) return;
  update(id, { status: EnumRecruitmentApplyStatus.PENDING });
};

const onCopyLink = (item: Recruitment) => {
  const url = pageLinkUtils.getJobLinkWithDomain(item);
  navigator.clipboard.writeText(url).then(() => {
    useToast().success('Sao chép liên kết thành công');
  });
};

const onDelete = (id: number) => {
  deleteById(id);
};

onMounted(() => {
  onFilter();
  useCompany.getCompany(1);
});

watch(
  () => useEmployerRecruitment.deleteSuccess,
  (value) => {
    if (value) {
      onFilter();
    }
  },
);
watch(
  () => useEmployerRecruitment.updateSuccess,
  (value) => {
    if (value) {
      onFilter();
    }
  },
);

watch(
  () => useEmployerRecruitment.deleteError,
  (value, oldValue) => {
    if (value && value !== oldValue) {
      useToast().showToast('Xóa tin đăng không thành công', ToastType.Error);
    }
  },
);
</script>
