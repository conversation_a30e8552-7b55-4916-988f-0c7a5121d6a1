<template>
  <div>
    <ProfileHeader :name="useEmployerRecruit.recruitment?.title" />
    <div class="mx-2 lg:mx-6">
      <div class="bg-white rounded-md p-6 my-4">
        <h1>{{ useEmployerRecruit.recruitment?.title }}</h1>
        <div class="grid grid-cols-2 gap-2">
          <PreviewItem
            label="Nghề nghiệp"
            :content="useEmployerRecruit.recruitment?.occupations?.map((item) => item.name).join(', ')"
          />
          <PreviewItem
            label="Yêu cầu giới tính"
            :content="useEmployerRecruit.recruitment?.jobGenderId === TinyInt.Yes ? 'Nam' : 'Nữ'"
          />
          <PreviewItem
            label="Hình thức làm việc"
            :content="useCommon.findJobMethodById(useEmployerRecruit.recruitment?.jobMethodId)?.name"
          />
          <PreviewItem
            label="<PERSON><PERSON><PERSON> cầu bằng cấp"
            :content="useCommon.findDegreeById(useEmployerRecruit.recruitment?.jobDegreeId)?.name"
          />
          <PreviewItem
            label="Cấp bậc"
            :content="useCommon.findJobLevelById(useEmployerRecruit.recruitment?.jobLevelId)?.name"
          />
          <PreviewItem
            label="Yêu cầu kinh nghiệm"
            :content="useCommon.findExperienceById(useEmployerRecruit.recruitment?.jobExperienceId)?.name"
          />
          <PreviewItem
            label="Số lượng tuyển"
            :content="useEmployerRecruit.recruitment?.vacancyQuantity?.toString()"
          />
          <PreviewItem
            label="Thời gian thử việc"
            :content="useEmployerRecruit.recruitment?.probationDuration?.toString() + ' tháng'"
          />
          <PreviewItem
            label="Yêu cầu độ tuổi"
            :content="
              useEmployerRecruit.recruitment?.minAge?.toString() +
              ' - ' +
              useEmployerRecruit.recruitment?.maxAge?.toString() +
              ' tuổi'
            "
          />
          <PreviewItem
            label="Hạn nộp hồ sơ"
            :content="formatDateOnly(useEmployerRecruit.recruitment?.applyExpiredAt)"
          />
          <PreviewItem
            label="Địa điểm làm việc"
            :content="useEmployerRecruit.recruitment?.workspaces?.map((item) => item.address).join(', ')"
          />
        </div>
        <Divider />
        <h1 class="text-xl font-semibold my-4">Mô tả công việc</h1>
        <div
          v-if="useEmployerRecruit.recruitment?.descriptionHtml"
          class="break-words text-[14px] my-6 leading-6"
          v-html="useEmployerRecruit.recruitment.descriptionHtml"
        />

        <h1
          v-if="useEmployerRecruit.recruitment?.requirementHtml"
          class="text-2xl font-semibold my-6"
        >
          Yêu cầu công việc
        </h1>
        <div
          v-if="useEmployerRecruit.recruitment?.descriptionHtml"
          class="break-words text-[14px] mb-2 leading-6"
          v-html="useEmployerRecruit.recruitment.requirementHtml"
        />

        <h1
          v-if="useEmployerRecruit.recruitment?.benefitHtml"
          class="text-2xl font-semibold my-6"
        >
          Quyền lợi
        </h1>
        <div
          v-if="useEmployerRecruit.recruitment?.benefitHtml"
          class="break-words text-[14px] mb-2 leading-6"
          v-html="useEmployerRecruit.recruitment.benefitHtml"
        />
        <br />
        <Divider />
        <h1 class="text-xl font-semibold my-4">Thông tin liên hệ</h1>
        <div class="grid grid-cols-2 gap-2">
          <PreviewItem
            label="Tên công ty"
            :content="useEmployerRecruit.recruitment?.contactName"
          />
          <PreviewItem
            label="Địa chỉ"
            :content="useEmployerRecruit.recruitment?.contactName"
          />
          <PreviewItem
            label="Email"
            :content="useEmployerRecruit.recruitment?.contactPhone"
          />
          <PreviewItem
            label="Số điện thoại"
            :content="useEmployerRecruit.recruitment?.contactAddress"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
definePageMeta({
  layout: 'employer',
  middleware: 'auth',
});

import ProfileHeader from '~/components/account/ProfileHeader.vue';
import Divider from '~/components/common/Divider.vue';
import PreviewItem from '~/components/resume/Preview/PreviewItem.vue';
import { useCommonStore } from '~/store/common.store';
import { useEmployerRecruitmentStore } from '~/store/employerRecruitment';
import { TinyInt } from '~/types';

const useEmployerRecruit = useEmployerRecruitmentStore();
const useCommon = useCommonStore();

onMounted(() => {
  const id = useRoute().params.id as string;
  useEmployerRecruit.getDetails(parseInt(id));
});
</script>
