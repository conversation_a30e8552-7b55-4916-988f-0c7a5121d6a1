<template>
  <div>
    <ProfileHeader name="<PERSON><PERSON> sơ ứng tuyển" />
    <div class="mx-5">
      <div class="flex justify-between items-center bg-white rounded-md my-4 pt-4 px-4">
        <div class="text-sm">Trạng thái tuyển dụng</div>
        <a
          v-for="(status, idx) in statuses"
          :key="idx"
          :class="['flex items-center py-2 text-gray-500', { [filterActiveClass]: activeStatus === status.code }]"
          href="javascript:void(0)"
          @click="onFilterStatus(status.code)"
        >
          <component
            :is="status.icon"
            v-if="status.icon"
            class="w-5 h-5"
          />
          <div class="mx-2">tất cả</div>
          <div class="rounded-full bg-gray-200 w-5 h-5 text-center my-auto text-sm">{{ status.count }}</div>
        </a>
      </div>
      <div class="bg-white">
        <table class="table">
          <thead>
            <tr class="text-black text-sm">
              <th class="text-left"><PERSON><PERSON><PERSON> <PERSON>ồ sơ</th>
              <th class="text-left">Mức phù hợp</th>
              <th class="text-left">Tin đăng</th>
              <th class="text-left">
                <a
                  class="flex items center"
                  href="javascript:void(0)"
                >
                  <span>Ngày ứng tuyển</span>
                  <SortArrow class="ml-2" />
                </a>
              </th>
              <th class="text-left">Trạng thái</th>
            </tr>
          </thead>
        </table>
        <tbody v-if="list && list.length"></tbody>
        <div
          v-else
          class="!py-12"
        >
          <NoData title="Chưa có ứng viên ứng tuyển" />
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import ProfileHeader from '~/components/account/ProfileHeader.vue';
import NoData from '~/components/common/NoData.vue';
import SortArrow from '~/components/common/SortArrow.vue';
import IconCancel from '~/components/icons/IconCancel.vue';
import IconCheckFilled from '~/components/icons/IconCheckFilled.vue';
import IconEye from '~/components/icons/IconEye.vue';
import IconGrid from '~/components/icons/IconGrid.vue';
import IconPhoneOutline from '~/components/icons/IconPhoneOutline.vue';
import IconSuitcaseOutline from '~/components/icons/IconSuitcaseOutline.vue';
import { EnumRecruitmentApplyStatus } from '~/types';
definePageMeta({
  layout: 'employer',
});

type filterStatus = 'all' | EnumRecruitmentApplyStatus;
const filterActiveClass = ref('text-primary font-semibold border-b-[2px] border-primary');
const statuses = ref<
  {
    code: filterStatus;
    name: string;
    count: number;
    icon: any;
  }[]
>([
  { code: 'all', name: 'Tất cả', count: 0, icon: IconGrid },
  { code: EnumRecruitmentApplyStatus.PENDING, name: 'Chờ đánh giá', count: 0, icon: IconEye },
  { code: EnumRecruitmentApplyStatus.SUITABLE, name: 'Phù hợp', count: 0, icon: IconCheckFilled },
  { code: EnumRecruitmentApplyStatus.INTERVIEWING, name: 'Đang phỏng vấn', count: 0, icon: IconPhoneOutline },
  { code: EnumRecruitmentApplyStatus.APPROVED, name: 'Đã tuyển', count: 0, icon: IconSuitcaseOutline },
  { code: EnumRecruitmentApplyStatus.REJECTED, name: 'Không phù hợp', count: 0, icon: IconCancel },
]);
const list = ref<any[]>([]);
const activeStatus = ref<filterStatus>('all');
const onFilterStatus = (status: filterStatus) => {
  activeStatus.value = status;
};
</script>
