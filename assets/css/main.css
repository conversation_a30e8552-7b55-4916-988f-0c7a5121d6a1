@reference "./app.css";

.bg-primary-bright {
  background-color: #f0edf8;
}
.text-primary-bright {
  color: #5c27d6;
}

/* Your custom styles here */
/* .btn {
  @apply rounded-md bg-primary border-none text-white;
} */
/* .btn:active {
  @apply !animate-none;
}
.btn:focus {
  @apply !animate-none;
}
.btn:hover {
  @apply bg-primary/25;
}

.btn:active {
  @apply animate-none;
} */
/* .btn.btn-outline:hover {
  @apply bg-white text-primary;
} */

/* .textarea {
  @apply bg-white font-light;
} */
.textarea:focus {
  @apply outline-none;
}
/* .textarea-bordered {
  @apply border-primary/20;
} */
/* .input {
  @apply border bg-white rounded-md font-light;
}
.input-sm {
  @apply h-9;
}
.input-round-sm {
  @apply rounded-sm;
} */

.input:focus,
.input:focus-within {
  @apply outline-none;
}

/* .input-bordered {
  @apply border-info/20;
}
.input[disabled] {
  @apply bg-gray-100 text-gray-400;
} */

.select {
  @apply bg-white font-light;
}
.select:focus {
  @apply outline-none;
}
/* .select-bordered {
  @apply border-primary/20;
} */
.form-item {
  @apply mb-4;
}
.form-item.form-item-error .input {
  @apply border-error;
}
.form-item.form-item-error .select {
  @apply border-error;
}
.form-item.form-item-error .textarea {
  @apply border-error;
}

/* .menu :where(li ul):before {
  width: 0;
} */

.hide-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.page-item {
  @apply w-8 h-8 justify-center flex items-center bg-white border border-gray-200 rounded-full font-light;
}

.page-item.page-item-active {
  @apply bg-info text-white;
}

.mb-container {
  @apply mx-auto px-2.5 w-full max-w-7xl;
}

.container {
  @apply mx-auto w-full max-w-7xl lg:max-w-screen-xl lg:px-3 lg:px-0;
}

html {
  /* @apply overflow-hidden; padding-right: 0px; */
}

/* .menu li > *:active {
  @apply !bg-primary/5;
}

.menu ul {
  margin-inline-start: 0;
} */

/* .job-btn {
  @apply bg-[#f8f6fb] text-primary max-w-48 w-full h-12 rounded-md text-sm font-medium flex justify-center items-center px-4 text-nowrap;
}

.job-btn-primary {
  @apply bg-[#f8f6fb] text-primary max-w-48 w-full h-12 rounded-md text-sm font-medium flex justify-center items-center px-4 text-nowrap !bg-primary !text-white;
}
.job-btn-outline {
  @apply !bg-white text-primary max-w-48 w-full !border !border-primary rounded-md text-sm font-semibold;
} */

.job-dropdown {
  @apply bg-white border border-gray-200 rounded-md shadow-lg absolute left-0;
}

.ql-toolbar.ql-snow,
.ql-container.ql-snow {
  @apply !border-info;
}
.ql-toolbar.ql-snow {
  @apply !rounded-t-md;
}
.ql-container {
  @apply !rounded-b-md;
  @apply !font-['Lexend'];
}
.ql-toolbar.ql-snow * {
  @apply !text-secondary;
}

.ql-editor {
  min-height: 120px;
}

.ql-toolbar button {
  width: 34px !important; /* Width per item */
  height: 29px !important; /* Height per item */
  border-radius: 6px;
}

.ql-toolbar .ql-align {
  width: 50px; /* Width for align buttons */
}

.ql-toolbar .custom-undo,
.ql-toolbar .custom-redo {
  width: 40px;
  height: 35px;
}

/* custom daisyui */
@utility input {
  @apply rounded-full;
}
