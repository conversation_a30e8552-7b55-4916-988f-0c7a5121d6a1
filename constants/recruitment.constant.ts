import { ERecruitmentStatus } from '@/types/recruitment.interface';

export const RecruitmentStatusTextMapper: {
  [key in ERecruitmentStatus]: string;
} = {
  [ERecruitmentStatus.PENDING]: '<PERSON><PERSON> duy<PERSON>t',
  [ERecruitmentStatus.APPROVED]: 'Đã duyệt',
  [ERecruitmentStatus.REJECTED]: 'Từ chối',
  [ERecruitmentStatus.DRAFT]: 'Bản nháp',
};

export const RecruitmentStatusColorMapper: {
  [key in ERecruitmentStatus]: string;
} = {
  [ERecruitmentStatus.PENDING]: 'warning',
  [ERecruitmentStatus.APPROVED]: 'success',
  [ERecruitmentStatus.REJECTED]: 'danger',
  [ERecruitmentStatus.DRAFT]: 'secondary',
};
