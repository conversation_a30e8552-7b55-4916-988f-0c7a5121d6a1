import { EnumRecruitmentApplyStatus } from '~/types';

export const RecruitmentAppliedStatusText: Record<string, string> = {
  [EnumRecruitmentApplyStatus.PENDING]: 'Chờ đánh giá',
  [EnumRecruitmentApplyStatus.SUITABLE]: '<PERSON><PERSON> hợp',
  [EnumRecruitmentApplyStatus.INTERVIEWING]: 'Đang phỏng vấn',
  [EnumRecruitmentApplyStatus.APPROVED]: 'Đã tuyển',
  [EnumRecruitmentApplyStatus.REJECTED]: 'Không phù hợp',
};

export const RecruitmentAppliedStatusColor: Record<string, string> = {
  [EnumRecruitmentApplyStatus.PENDING]: 'gray',
  [EnumRecruitmentApplyStatus.SUITABLE]: 'green',
  [EnumRecruitmentApplyStatus.INTERVIEWING]: 'blue',
  [EnumRecruitmentApplyStatus.APPROVED]: 'green',
  [EnumRecruitmentApplyStatus.REJECTED]: 'red',
};
