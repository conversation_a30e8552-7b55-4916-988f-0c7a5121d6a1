import { useGoogleOneTapAuth } from '~/composables/useGoogleOneTap';
import { useAuthStore } from '~/store/auth.store';

export default defineNuxtPlugin(() => {
  // Initialize Google One Tap globally when the app starts
  const { initializeOneTap, isOneTapAvailable } = useGoogleOneTapAuth();
  const authStore = useAuthStore();

  // Initialize One Tap after a short delay to ensure Google API is loaded
  const initWithDelay = () => {
    setTimeout(() => {
      if (isOneTapAvailable()) {
        initializeOneTap({
          autoSelect: true,
          cancelOnTapOutside: false,
        });
      }
    }, 1000); // 1 second delay
  };

  // Initialize on app start
  onMounted(() => {
    initWithDelay();
  });

  // Re-initialize when user logs out
  watch(() => authStore.authenticated, (isAuthenticated) => {
    if (!isAuthenticated) {
      // Delay to ensure auth state has fully updated
      setTimeout(() => {
        initWithDelay();
      }, 500);
    }
  });
});
