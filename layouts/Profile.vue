<template>
  <ClientOnly>
    <MobileHeader v-if="isMobile" />
    <Header v-else />
    <!-- <div class="flex justify-start min-h-[calc(100vh-64px)]">
      <ProfileMenu v-if="!isMobile"/>
      <div :class="[isMobile ? null : 'w-[calc(100%-320px)]', 'w-full h-[calc(100vh-64px)]']">
        <slot />
      </div>
    </div> -->
    <div class="flex justify-start min-h-[calc(100vh-64px)]">
      <ProfileMenu
        v-if="!isMobile"
        class="max-w-[280px]"
      />
      <div class="w-full mx-auto">
        <slot />
      </div>
    </div>
  </ClientOnly>
</template>

<script setup lang="ts">
import Header from '~/components/layouts/header.vue';
import MobileHeader from '~/components/layouts/mobileHeader.vue';
import ProfileMenu from '~/components/layouts/ProfileMenu.vue';

// definePageMeta({
//   title: "Tìm kiếm việc làm tốt - Tuyển dụng nhanh",
//   keywords:
//     "việc làm, tuyển dụng, tìm việc làm, tìm việc nhanh, việc làm 24h, viec lam, tuyen dung, tim viec lam, tim viec nhanh, viec lam 24h",
//   description:
//     "Tìm việc nhanh tại Vieclamlamdong.site với hơn 400,000 nhà tuyển dụng top đầu Việt Nam tùy theo lựa chọn ngành nghề văn phòng hành chính - kỹ thuật cơ khí điện tử - lao động phổ thông / fresher / chuyên môn tay nghề cao / quản lý giám sát / giám đốc / phó giám đốc với mức lương và đề xuất phù hợp.",
// });

const { isMobile } = useDevice();
</script>
