<template>
  <div>
    <!-- <MobileHeader v-if="isMobile" /> -->
    <MobileEmployerHeader v-if="isMobile" />
    <Header v-else />
    <div class="flex justify-start min-h-[calc(100vh-64px)]">
      <EmployerMenuBar v-if="!isMobile" />
      <div class="w-full mx-auto md:max-w-[calc(100vw-280px)]">
        <slot />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import EmployerMenuBar from '~/components/employer/layout/EmployerMenuBar.vue';
import Header from "~/components/layouts/header.vue";
import MobileEmployerHeader from '~/components/layouts/MobileEmployerHeader.vue';
import { useEmployerCompanyStore } from '~/store/employerCompany';
import { useToast } from '~/store/toast';
const useEmployerCompany = useEmployerCompanyStore();

definePageMeta({
  title: "Tìm kiếm việc làm tốt - Tuyển dụng nhanh",
  keywords:
    "việc làm, tuyển dụng, tìm việc làm, tìm việc nhanh, việc làm 24h, viec lam, tuyen dung, tim viec lam, tim viec nhanh, viec lam 24h",
  description:
    "Tìm việc nhanh tại Vieclamlamdong.site với hơn 400,000 nhà tuyển dụng top đầu Việt Nam tùy theo lựa chọn ngành nghề văn phòng hành chính - kỹ thuật cơ khí điện tử - lao động phổ thông / fresher / chuyên môn tay nghề cao / quản lý giám sát / giám đốc / phó giám đốc với mức lương và đề xuất phù hợp.",
});

const { isMobile } = useDevice();
onMounted(() => {
  useEmployerCompany.getMyCompany();
});

watch(
  () => useEmployerCompany.getProfileSuccess,
  (newVal, oldValue) => {
    console.log('useEmployerCompany.getProfileSuccess', newVal);
    if (newVal && newVal !== oldValue && !useEmployerCompany.profile) {
      useRouter().push('/employer/accounts/create');
    }
  },
);

watch(
  () => useEmployerCompany.getProfileError,
  (newVal, oldValue) => {
    console.log('useEmployerCompany.getProfileError', newVal);
    if (newVal && newVal !== oldValue) {
      useToast().error(
        useEmployerCompany.getProfileMessage || 'Có lỗi xảy ra, vui lòng thử lại sau',
      )
    }
  },
);
</script>
