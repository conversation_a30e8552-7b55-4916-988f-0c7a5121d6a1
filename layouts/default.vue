<template>
  <div>
    <MobileHeader v-if="isMobile" />
    <Header v-else />
    <div class="min-h-[calc(100vh-376px)]">
      <slot />
    </div>
    <Footer />
  </div>
</template>

<script setup lang="ts">
import Footer from '~/components/layouts/footer.vue';
import MobileHeader from '~/components/layouts/mobileHeader.vue';
import Header from '~/components/layouts/header.vue';

// definePageMeta({
//   title: "Tìm kiếm việc làm tốt - Tuyển dụng nhanh",
//   keywords:
//     "việc làm, tuyển dụng, tìm việc làm, tìm việc nhanh, việc làm 24h, viec lam, tuyen dung, tim viec lam, tim viec nhanh, viec lam 24h",
//   description:
//     "Tìm việc nhanh tại Vieclamlamdong.site với hơn 400,000 nhà tuyển dụng top đầu Việt Nam tùy theo lựa chọn ngành nghề văn phòng hành chính - kỹ thuật cơ khí điện tử - lao động phổ thông / fresher / chuyên môn tay nghề cao / quản lý giám sát / giám đốc / phó giám đốc với mức lương và đề xuất phù hợp.",
// });

const { isMobile } = useDevice();
</script>
